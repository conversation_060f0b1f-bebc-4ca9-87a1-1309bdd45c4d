class Package {
  final String id;
  final String nom;
  final String description;
  final double montantTotal;
  final double montantJournalier;
  final int dureeJours;
  final List<String> images;
  final DateTime dateCreation;
  final String categorie;
  
  Package({
    required this.id,
    required this.nom,
    required this.description,
    required this.montantTotal,
    required this.montantJournalier,
    required this.dureeJours,
    required this.images,
    required this.dateCreation,
    required this.categorie,
  });
  
  factory Package.fromJson(Map<String, dynamic> json) {
    return Package(
      id: json['id'],
      nom: json['nom'],
      description: json['description'],
      montantTotal: json['montantTotal'],
      montantJournalier: json['montantJournalier'],
      dureeJours: json['dureeJours'],
      images: List<String>.from(json['images']),
      dateCreation: DateTime.parse(json['dateCreation']),
      categorie: json['categorie'],
    );
  }
  
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'nom': nom,
      'description': description,
      'montantTotal': montantTotal,
      'montantJournalier': montant<PERSON>our<PERSON>ier,
      'dureeJours': dureeJours,
      'images': images,
      'dateCreation': dateCreation.toIso8601String(),
      'categorie': categorie,
    };
  }
}

class Souscription {
  final String id;
  final String userId;
  final String packageId;
  final DateTime dateSouscription;
  final List<VersementPackage> versements;
  final double monnaie;
  final String status; // 'en_cours', 'complete', 'annule'
  
  Souscription({
    required this.id,
    required this.userId,
    required this.packageId,
    required this.dateSouscription,
    required this.versements,
    this.monnaie = 0,
    this.status = 'en_cours',
  });
  
  // Nombre de versements déjà effectués
  int get versementsEffectues => versements.length;
  
  // Montant déjà versé
  double get montantVerse => versements.fold(0.0, (sum, versement) => sum + versement.montant);
  
  factory Souscription.fromJson(Map<String, dynamic> json) {
    return Souscription(
      id: json['id'],
      userId: json['userId'],
      packageId: json['packageId'],
      dateSouscription: DateTime.parse(json['dateSouscription']),
      versements: (json['versements'] as List).map((v) => VersementPackage.fromJson(v)).toList(),
      monnaie: json['monnaie'] ?? 0,
      status: json['status'] ?? 'en_cours',
    );
  }
  
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'userId': userId,
      'packageId': packageId,
      'dateSouscription': dateSouscription.toIso8601String(),
      'versements': versements.map((v) => v.toJson()).toList(),
      'monnaie': monnaie,
      'status': status,
    };
  }
}

class VersementPackage {
  final String id;
  final double montant;
  final DateTime date;
  
  VersementPackage({
    required this.id,
    required this.montant,
    required this.date,
  });
  
  factory VersementPackage.fromJson(Map<String, dynamic> json) {
    return VersementPackage(
      id: json['id'],
      montant: json['montant'],
      date: DateTime.parse(json['date']),
    );
  }
  
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'montant': montant,
      'date': date.toIso8601String(),
    };
  }
}
