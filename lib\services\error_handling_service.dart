import 'dart:async';
import 'dart:io';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:connectivity_plus/connectivity_plus.dart';
import 'package:callitris/services/optimized_navigation_service.dart';

/// Types d'erreurs gérées par l'application
enum ErrorType {
  network,
  authentication,
  validation,
  server,
  unknown,
  timeout,
  noInternet,
}

/// Classe représentant une erreur de l'application
class AppError {
  final ErrorType type;
  final String message;
  final String? details;
  final int? statusCode;
  final DateTime timestamp;
  final String? stackTrace;

  AppError({
    required this.type,
    required this.message,
    this.details,
    this.statusCode,
    DateTime? timestamp,
    this.stackTrace,
  }) : timestamp = timestamp ?? DateTime.now();

  @override
  String toString() {
    return 'AppError(type: $type, message: $message, statusCode: $statusCode)';
  }
}

/// Service de gestion d'erreurs optimisé
class ErrorHandlingService {
  static const String _tag = '[ErrorHandlingService]';
  static ErrorHandlingService? _instance;
  static ErrorHandlingService get instance =>
      _instance ??= ErrorHandlingService._();

  ErrorHandlingService._();

  // Historique des erreurs pour le debugging
  final List<AppError> _errorHistory = [];
  final int _maxHistorySize = 50;

  // Callbacks pour les différents types d'erreurs
  final Map<ErrorType, List<Function(AppError)>> _errorCallbacks = {};

  /// Gère une erreur de manière centralisée
  Future<void> handleError(
    dynamic error, {
    String? context,
    bool showToUser = true,
    bool logError = true,
  }) async {
    final appError = _parseError(error, context);

    if (logError) {
      _logError(appError);
    }

    _addToHistory(appError);
    _notifyCallbacks(appError);

    if (showToUser) {
      await _showErrorToUser(appError);
    }
  }

  /// Parse une erreur en AppError
  AppError _parseError(dynamic error, String? context) {
    if (error is AppError) {
      return error;
    }

    String message = 'Une erreur inattendue s\'est produite';
    ErrorType type = ErrorType.unknown;
    int? statusCode;
    String? details = context;

    if (error is SocketException) {
      type = ErrorType.network;
      message = 'Problème de connexion réseau';
    } else if (error is TimeoutException) {
      type = ErrorType.timeout;
      message = 'La requête a pris trop de temps';
    } else if (error is HttpException) {
      type = ErrorType.server;
      message = 'Erreur du serveur';
      final statusMatch = RegExp(r'(\d{3})').firstMatch(error.message);
      if (statusMatch != null) {
        statusCode = int.tryParse(statusMatch.group(1)!);
        message = _getHttpErrorMessage(statusCode!);
      }
    } else if (error is FormatException) {
      type = ErrorType.validation;
      message = 'Format de données invalide';
    } else if (error is Exception) {
      message = error.toString().replaceFirst('Exception: ', '');
    } else if (error is String) {
      message = error;
    }

    return AppError(
      type: type,
      message: message,
      details: details,
      statusCode: statusCode,
      stackTrace: kDebugMode ? StackTrace.current.toString() : null,
    );
  }

  /// Obtient un message d'erreur HTTP approprié
  String _getHttpErrorMessage(int statusCode) {
    switch (statusCode) {
      case 400:
        return 'Requête invalide';
      case 401:
        return 'Non autorisé - Veuillez vous reconnecter';
      case 403:
        return 'Accès interdit';
      case 404:
        return 'Ressource non trouvée';
      case 408:
        return 'Délai d\'attente dépassé';
      case 429:
        return 'Trop de requêtes - Veuillez patienter';
      case 500:
        return 'Erreur interne du serveur';
      case 502:
        return 'Passerelle défaillante';
      case 503:
        return 'Service temporairement indisponible';
      case 504:
        return 'Délai d\'attente de la passerelle';
      default:
        return 'Erreur du serveur ($statusCode)';
    }
  }

  /// Log une erreur
  void _logError(AppError error) {
    debugPrint('$_tag: ${error.type.name.toUpperCase()} - ${error.message}');
    if (error.details != null) {
      debugPrint('$_tag: Détails - ${error.details}');
    }
    if (error.statusCode != null) {
      debugPrint('$_tag: Code de statut - ${error.statusCode}');
    }
    if (kDebugMode && error.stackTrace != null) {
      debugPrint('$_tag: Stack trace - ${error.stackTrace}');
    }
  }

  /// Ajoute une erreur à l'historique
  void _addToHistory(AppError error) {
    _errorHistory.add(error);
    if (_errorHistory.length > _maxHistorySize) {
      _errorHistory.removeAt(0);
    }
  }

  /// Notifie les callbacks enregistrés
  void _notifyCallbacks(AppError error) {
    final callbacks = _errorCallbacks[error.type] ?? [];
    for (final callback in callbacks) {
      try {
        callback(error);
      } catch (e) {
        debugPrint('$_tag: Erreur dans le callback: $e');
      }
    }
  }

  /// Affiche l'erreur à l'utilisateur
  Future<void> _showErrorToUser(AppError error) async {
    // Vérifier la connectivité pour les erreurs réseau
    if (error.type == ErrorType.network) {
      final isConnected = await _checkConnectivity();
      if (!isConnected) {
        _showNoInternetError();
        return;
      }
    }

    // Afficher l'erreur appropriée
    String userMessage = _getUserFriendlyMessage(error);

    OptimizedNavigationService.showOptimizedSnackBar(
      message: userMessage,
      backgroundColor: _getErrorColor(error.type),
      icon: _getErrorIcon(error.type),
      duration: _getErrorDuration(error.type),
    );
  }

  /// Obtient un message convivial pour l'utilisateur
  String _getUserFriendlyMessage(AppError error) {
    switch (error.type) {
      case ErrorType.network:
        return 'Problème de connexion. Vérifiez votre réseau.';
      case ErrorType.authentication:
        return 'Session expirée. Veuillez vous reconnecter.';
      case ErrorType.validation:
        return 'Données invalides. Vérifiez vos informations.';
      case ErrorType.server:
        return 'Problème du serveur. Réessayez plus tard.';
      case ErrorType.timeout:
        return 'Délai d\'attente dépassé. Réessayez.';
      case ErrorType.noInternet:
        return 'Aucune connexion Internet détectée.';
      default:
        return error.message.isNotEmpty
            ? error.message
            : 'Une erreur s\'est produite.';
    }
  }

  /// Obtient la couleur appropriée pour le type d'erreur
  Color _getErrorColor(ErrorType type) {
    switch (type) {
      case ErrorType.network:
      case ErrorType.noInternet:
        return const Color(0xFFFF9800); // Orange
      case ErrorType.authentication:
        return const Color(0xFFF44336); // Rouge
      case ErrorType.validation:
        return const Color(0xFF2196F3); // Bleu
      case ErrorType.server:
      case ErrorType.timeout:
        return const Color(0xFF9C27B0); // Violet
      default:
        return const Color(0xFF607D8B); // Gris bleu
    }
  }

  /// Obtient l'icône appropriée pour le type d'erreur
  IconData _getErrorIcon(ErrorType type) {
    switch (type) {
      case ErrorType.network:
      case ErrorType.noInternet:
        return Icons.wifi_off;
      case ErrorType.authentication:
        return Icons.lock_outline;
      case ErrorType.validation:
        return Icons.warning_outlined;
      case ErrorType.server:
        return Icons.cloud_off_outlined;
      case ErrorType.timeout:
        return Icons.access_time_outlined;
      default:
        return Icons.error_outline;
    }
  }

  /// Obtient la durée d'affichage appropriée
  Duration _getErrorDuration(ErrorType type) {
    switch (type) {
      case ErrorType.authentication:
        return const Duration(seconds: 5);
      case ErrorType.validation:
        return const Duration(seconds: 4);
      default:
        return const Duration(seconds: 3);
    }
  }

  /// Vérifie la connectivité réseau
  Future<bool> _checkConnectivity() async {
    try {
      final connectivityResults = await Connectivity().checkConnectivity();
      return connectivityResults.isNotEmpty &&
          !connectivityResults.every(
            (result) => result == ConnectivityResult.none,
          );
    } catch (e) {
      return false;
    }
  }

  /// Affiche une erreur spécifique pour l'absence d'Internet
  void _showNoInternetError() {
    OptimizedNavigationService.showOptimizedSnackBar(
      message: 'Aucune connexion Internet',
      backgroundColor: const Color(0xFFFF5722),
      icon: Icons.wifi_off,
      duration: const Duration(seconds: 4),
      action: () async {
        // Réessayer la connectivité
        final isConnected = await _checkConnectivity();
        if (isConnected) {
          OptimizedNavigationService.showOptimizedSnackBar(
            message: 'Connexion rétablie',
            backgroundColor: const Color(0xFF4CAF50),
            icon: Icons.wifi,
            duration: const Duration(seconds: 2),
          );
        }
      },
      actionLabel: 'Réessayer',
    );
  }

  /// Enregistre un callback pour un type d'erreur
  void registerErrorCallback(ErrorType type, Function(AppError) callback) {
    _errorCallbacks.putIfAbsent(type, () => []).add(callback);
  }

  /// Supprime un callback pour un type d'erreur
  void unregisterErrorCallback(ErrorType type, Function(AppError) callback) {
    _errorCallbacks[type]?.remove(callback);
  }

  /// Obtient l'historique des erreurs
  List<AppError> get errorHistory => List.unmodifiable(_errorHistory);

  /// Nettoie l'historique des erreurs
  void clearErrorHistory() {
    _errorHistory.clear();
  }

  /// Obtient les statistiques d'erreurs
  Map<ErrorType, int> getErrorStatistics() {
    final stats = <ErrorType, int>{};
    for (final error in _errorHistory) {
      stats[error.type] = (stats[error.type] ?? 0) + 1;
    }
    return stats;
  }
}
