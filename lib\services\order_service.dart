import 'dart:convert';
import 'dart:async';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:http/http.dart' as http;
import 'package:shared_preferences/shared_preferences.dart';
import 'package:url_launcher/url_launcher.dart';
import 'package:rxdart/rxdart.dart';
import '../config/api_config.dart';
import 'auth_service.dart';
import 'cinetpay_service.dart';
import 'transaction_history_service.dart';
import 'app_state_service.dart';
import '../models/cinetpay_transaction.dart';

class OrderService {
  static const String _tag = '[OrderService]';

  // Streams pour la réactivité des commandes
  static final BehaviorSubject<List<Map<String, dynamic>>> _ordersSubject =
      BehaviorSubject<List<Map<String, dynamic>>>.seeded([]);
  static final BehaviorSubject<bool> _isLoadingOrdersSubject =
      BehaviorSubject<bool>.seeded(false);
  static final BehaviorSubject<String?> _ordersErrorSubject =
      BehaviorSubject<String?>.seeded(null);

  // Streams publics
  static Stream<List<Map<String, dynamic>>> get ordersStream =>
      _ordersSubject.stream;
  static Stream<bool> get isLoadingOrdersStream =>
      _isLoadingOrdersSubject.stream;
  static Stream<String?> get ordersErrorStream => _ordersErrorSubject.stream;

  // Getters
  static List<Map<String, dynamic>> get orders => _ordersSubject.value;
  static bool get isLoadingOrders => _isLoadingOrdersSubject.value;
  static String? get ordersError => _ordersErrorSubject.value;

  // Timer pour le rafraîchissement automatique
  static Timer? _refreshTimer;

  /// Initialise le service de commandes réactif
  static Future<void> initialize() async {
    print('$_tag: Initialisation du service de commandes réactif...');

    // Charger les commandes initiales
    await refreshOrders(silent: true);

    // Configurer le rafraîchissement automatique
    _setupAutoRefresh();

    print('$_tag: Service de commandes initialisé');
  }

  /// Configure le rafraîchissement automatique des commandes
  static void _setupAutoRefresh() {
    _refreshTimer?.cancel();

    // Rafraîchir les commandes toutes les 2 minutes si connecté
    _refreshTimer = Timer.periodic(const Duration(minutes: 2), (timer) async {
      if (AuthService.isLoggedInValue) {
        await refreshOrders(silent: true);
      }
    });
  }

  /// Rafraîchit les commandes de manière réactive
  static Future<void> refreshOrders({bool silent = false}) async {
    try {
      if (!silent) {
        _isLoadingOrdersSubject.add(true);
        _ordersErrorSubject.add(null);
      }

      final result = await getUserOrders();

      if (result['success']) {
        final List<Map<String, dynamic>> allOrders = [
          ...(result['activeOrders'] ?? []).cast<Map<String, dynamic>>(),
          ...(result['completedOrders'] ?? []).cast<Map<String, dynamic>>(),
        ];

        _ordersSubject.add(allOrders);
        AppStateService.instance.updateOrders(allOrders);

        print('$_tag: ${allOrders.length} commandes rafraîchies');
      } else {
        _ordersErrorSubject.add(
          result['message'] ?? 'Erreur lors du chargement des commandes',
        );
      }
    } catch (e) {
      print('$_tag: Erreur lors du rafraîchissement des commandes: $e');
      if (!silent) {
        _ordersErrorSubject.add(
          'Erreur lors du rafraîchissement des commandes',
        );
      }
    } finally {
      if (!silent) {
        _isLoadingOrdersSubject.add(false);
      }
    }
  }

  /// Met à jour une commande spécifique dans la liste
  static void updateOrder(Map<String, dynamic> updatedOrder) {
    final currentOrders = List<Map<String, dynamic>>.from(_ordersSubject.value);
    final orderIndex = currentOrders.indexWhere(
      (order) => order['id'] == updatedOrder['id'],
    );

    if (orderIndex != -1) {
      currentOrders[orderIndex] = updatedOrder;
      _ordersSubject.add(currentOrders);
      AppStateService.instance.updateOrders(currentOrders);
      print('$_tag: Commande ${updatedOrder['id']} mise à jour');
    }
  }

  /// Ajoute une nouvelle commande à la liste
  static void addOrder(Map<String, dynamic> newOrder) {
    final currentOrders = List<Map<String, dynamic>>.from(_ordersSubject.value);
    currentOrders.insert(0, newOrder); // Ajouter au début de la liste
    _ordersSubject.add(currentOrders);
    AppStateService.instance.updateOrders(currentOrders);
    print('$_tag: Nouvelle commande ${newOrder['id']} ajoutée');
  }

  // Passer une commande (pour un produit du catalogue ou un élément de carnet)
  // Passer une commande (pour un produit du catalogue ou un élément de carnet)
  static Future<Map<String, dynamic>> placeOrder(
    String itemId, {
    bool isCarnet = false,
  }) async {
    try {
      // Récupérer les données utilisateur et le token
      final userData = await AuthService.getUserData();
      final token = await AuthService.getAuthToken();

      if (userData == null || token == null) {
        return {
          'success': false,
          'message': 'Vous devez être connecté pour passer une commande.',
        };
      }

      final clientId = userData['id_client'].toString();

      print(
        'Envoi de commande: clientId=$clientId, itemId=$itemId, token=$token',
      );

      // Préparer les données de la requête
      final Map<String, dynamic> requestData = {
        'clientId': clientId,
        'id_item': itemId,
        // Ajouter un indicateur pour les commandes de carnet si nécessaire
        if (isCarnet) 'isCarnet': true,
      };

      // Envoyer la requête avec le token dans l'en-tête Authorization
      final response = await http
          .post(
            Uri.parse(ApiConfig.addCommandeEndpoint),
            headers: {
              'Content-Type': 'application/json',
              'Authorization': 'Bearer $token',
            },
            body: jsonEncode(requestData),
          )
          .timeout(const Duration(seconds: 30));

      print('Réponse de l\'API de commande: ${response.body}');

      // Gérer les réponses d'erreur spécifiques
      if (response.statusCode == 404) {
        // Essayer de décoder le message d'erreur
        try {
          final errorData = jsonDecode(response.body);
          final errorMessage = errorData['message'] ?? 'Produit non disponible';

          // Vérifier si le message contient "épuisé" ou "stock"
          if (errorMessage.toLowerCase().contains('épuisé') ||
              errorMessage.toLowerCase().contains('stock')) {
            return {
              'success': false,
              'message': 'Ce produit est actuellement en rupture de stock.',
              'stockError': true,
            };
          } else {
            return {'success': false, 'message': errorMessage};
          }
        } catch (e) {
          return {
            'success': false,
            'message': 'Ce produit est actuellement indisponible.',
          };
        }
      }

      // Traiter la réponse
      if (response.statusCode == 200 || response.statusCode == 201) {
        try {
          final jsonResponse = jsonDecode(response.body);

          // Vérifier si la réponse indique un succès
          if (jsonResponse.containsKey('success') &&
              jsonResponse['success'] == true) {
            // Rafraîchir les commandes après succès
            refreshOrders(silent: true);

            return {
              'success': true,
              'message':
                  jsonResponse['message'] ?? 'Commande passée avec succès !',
              'orderData': jsonResponse,
              'commande_id': jsonResponse['commande_id'] ?? '',
            };
          } else if (jsonResponse.containsKey('message')) {
            // Rafraîchir les commandes après succès
            refreshOrders(silent: true);

            return {
              'success': true,
              'message': jsonResponse['message'],
              'orderData': jsonResponse,
              'commande_id': jsonResponse['commande_id'] ?? '',
            };
          } else {
            // Rafraîchir les commandes après succès
            refreshOrders(silent: true);

            return {
              'success': true,
              'message': 'Commande passée avec succès !',
              'orderData': jsonResponse,
              'commande_id': jsonResponse['commande_id'] ?? '',
            };
          }
        } catch (e) {
          print('Erreur lors du décodage de la réponse: $e');

          // Si la réponse contient "success" ou "réussi", considérer comme un succès
          if (response.body.toLowerCase().contains('success') ||
              response.body.toLowerCase().contains('réussi')) {
            return {
              'success': true,
              'message': 'Commande passée avec succès !',
            };
          } else {
            return {
              'success': false,
              'message': 'Format de réponse invalide. Veuillez réessayer.',
            };
          }
        }
      } else {
        print('Erreur HTTP: ${response.statusCode}');
        return {
          'success': false,
          'message':
              'Erreur lors de la commande (${response.statusCode}). Veuillez réessayer.',
        };
      }
    } catch (e) {
      print('Exception lors de la commande: $e');
      return {
        'success': false,
        'message':
            'Erreur de connexion. Veuillez vérifier votre connexion internet.',
      };
    }
  }

  // Récupérer les commandes de l'utilisateur
  // Récupérer les commandes de l'utilisateur
  static Future<Map<String, dynamic>> getUserOrders() async {
    try {
      // Récupérer l'ID client et le token
      final userData = await AuthService.getUserData();
      final token = await AuthService.getAuthToken();

      if (userData == null || token == null) {
        return {
          'success': false,
          'message': 'Vous devez être connecté pour voir vos commandes.',
        };
      }

      final clientId = userData['id_client'].toString();

      final response = await http
          .get(
            Uri.parse(
              '${ApiConfig.baseUrl}/commande/getCommandeClient.php?id_client=$clientId',
            ),
            headers: {'Authorization': 'Bearer $token'},
          )
          .timeout(const Duration(seconds: 30));

      print('Réponse API commandes: ${response.body}');

      if (response.statusCode == 200) {
        final List<dynamic> ordersData = jsonDecode(response.body);

        // Séparer les commandes actives et terminées
        final List<Map<String, dynamic>> activeOrders = [];
        final List<Map<String, dynamic>> completedOrders = [];

        for (var order in ordersData) {
          final Map<String, dynamic> orderMap = Map<String, dynamic>.from(
            order,
          );

          // Déterminer si la commande est active ou terminée
          final int reste = int.tryParse(orderMap['reste'].toString()) ?? 0;

          if (reste > 0) {
            activeOrders.add(orderMap);
          } else {
            completedOrders.add(orderMap);
          }
        }

        return {
          'success': true,
          'orders': ordersData,
          'activeOrders': activeOrders,
          'completedOrders': completedOrders,
        };
      } else {
        return {
          'success': false,
          'message':
              'Erreur lors de la récupération des commandes (${response.statusCode}).',
        };
      }
    } catch (e) {
      print('Exception lors de la récupération des commandes: $e');
      return {
        'success': false,
        'message':
            'Erreur de connexion. Veuillez vérifier votre connexion internet.',
      };
    }
  }

  // Récupérer les détails d'une commande spécifique
  static Future<Map<String, dynamic>> getOrderDetails(String orderId) async {
    try {
      // Vérifier si l'utilisateur est connecté
      final isLoggedIn = await AuthService.isLoggedIn();
      if (!isLoggedIn) {
        return {
          'success': false,
          'message':
              'Vous devez être connecté pour voir les détails de la commande',
        };
      }

      // Récupérer le token d'authentification
      final token =
          await AuthService.getAuthToken(); // Correction ici: getAuthToken au lieu de getToken
      if (token == null) {
        return {
          'success': false,
          'message': 'Token d\'authentification non disponible',
        };
      }

      // Construire l'URL de l'API
      final apiUrl = '${ApiConfig.baseUrl}/commande/details/$orderId';

      print('Appel API détails commande: $apiUrl');

      // Effectuer la requête HTTP
      final response = await http.get(
        Uri.parse(apiUrl),
        headers: {
          'Authorization': 'Bearer $token',
          'Content-Type': 'application/json',
        },
      );

      print(
        'Réponse API détails commande (status: ${response.statusCode}): ${response.body}',
      );

      if (response.statusCode == 200) {
        try {
          final dynamic data = jsonDecode(response.body);

          if (data is Map) {
            return {'success': true, 'data': data};
          } else if (data is List && data.isNotEmpty) {
            return {'success': true, 'data': data[0]};
          } else {
            // Si la réponse est vide ou non reconnue, essayer de récupérer toutes les commandes
            print(
              'Format de réponse non reconnu, tentative de récupération depuis toutes les commandes',
            );
            return await _getOrderDetailsFromAllOrders(orderId);
          }
        } catch (e) {
          print('Erreur lors du décodage de la réponse: $e');
          return await _getOrderDetailsFromAllOrders(orderId);
        }
      } else {
        print('Erreur API: ${response.statusCode}');
        return await _getOrderDetailsFromAllOrders(orderId);
      }
    } catch (e) {
      print('Exception lors de la récupération des détails: $e');
      return {
        'success': false,
        'message':
            'Erreur de connexion. Veuillez vérifier votre connexion internet.',
      };
    }
  }

  // Méthode auxiliaire pour récupérer les détails d'une commande à partir de la liste complète
  static Future<Map<String, dynamic>> _getOrderDetailsFromAllOrders(
    String orderId,
  ) async {
    try {
      print(
        'Tentative de récupération des détails depuis la liste complète des commandes',
      );
      final ordersResult = await getUserOrders();

      if (ordersResult['success']) {
        final List<dynamic> allOrders = [
          ...(ordersResult['activeOrders'] ?? []),
          ...(ordersResult['completedOrders'] ?? []),
        ];

        // Chercher la commande par ID
        final orderData = allOrders.firstWhere(
          (order) => order['id'].toString() == orderId,
          orElse: () => null,
        );

        if (orderData != null) {
          print('Commande trouvée dans la liste complète: ${orderData['id']}');
          return {'success': true, 'data': orderData};
        } else {
          print('Commande non trouvée dans la liste complète');
          return {'success': false, 'message': 'Commande introuvable'};
        }
      } else {
        print('Échec de récupération de la liste des commandes');
        return {
          'success': false,
          'message':
              ordersResult['message'] ??
              'Impossible de récupérer les commandes',
        };
      }
    } catch (e) {
      print(
        'Exception lors de la récupération des détails depuis la liste: $e',
      );
      return {
        'success': false,
        'message': 'Erreur lors de la récupération des détails: $e',
      };
    }
  }

  // Ajouter un versement à une commande existante
  static Future<Map<String, dynamic>> addVersement(
    String commandeId,
    double montant, {
    double monnaie = 0,
    String? commandeKey,
    String? transactionCode,
  }) async {
    try {
      // Récupérer les données utilisateur et le token
      final userData = await AuthService.getUserData();
      final token = await AuthService.getAuthToken();

      if (userData == null || token == null) {
        return {
          'success': false,
          'message': 'Vous devez être connecté pour effectuer un versement.',
        };
      }

      final clientId = userData['id_client'].toString();

      print(
        'Envoi de versement: commandeId=$commandeId, clientId=$clientId, montant=$montant, monnaie=$monnaie',
      );

      // Convertir les valeurs en nombres entiers si nécessaire
      final int commandeIdInt = int.tryParse(commandeId) ?? 0;
      final int clientIdInt = int.tryParse(clientId) ?? 0;
      final int montantInt = montant.toInt();
      final int monnaieInt = monnaie.toInt();

      // Si aucune clé de commande n'est fournie, essayer de la récupérer
      String? cleCommande = commandeKey;
      if (cleCommande == null) {
        // Récupérer les détails de la commande pour obtenir la clé
        final orderDetails = await getActiveOrders();
        if (orderDetails['success']) {
          final List<dynamic> activeOrders = orderDetails['activeOrders'] ?? [];
          for (var order in activeOrders) {
            if (order['id'].toString() == commandeId) {
              cleCommande = order['cle']?.toString();
              print('Clé de commande trouvée: $cleCommande');
              break;
            }
          }
        }

        if (cleCommande == null) {
          return {
            'success': false,
            'message':
                'Impossible de trouver la clé de la commande. Veuillez réessayer.',
          };
        }
      }

      // Préparer les données de la requête avec la clé de commande correcte
      final Map<String, dynamic> requestData = {
        'commande_id': commandeIdInt,
        'cle': cleCommande, // Utiliser la clé spécifique à la commande
        'clientId': clientIdInt,
        'montant': montantInt,
        'monnaie': monnaieInt,
        'transaction_code': transactionCode,
      };

      print('Données de la requête: $requestData');

      // Envoyer la requête avec le token dans l'en-tête Authorization
      final response = await http
          .post(
            Uri.parse(ApiConfig.addVersementEndpoint),
            headers: {
              'Content-Type': 'application/json',
              'Authorization': 'Bearer $token',
            },
            body: jsonEncode(requestData),
          )
          .timeout(const Duration(seconds: 30));

      print(
        'Réponse de l\'API de versement (${response.statusCode}): ${response.body}',
      );

      if (response.statusCode == 200 || response.statusCode == 201) {
        try {
          // Vérifier si la réponse est vide
          if (response.body.isEmpty) {
            print('Réponse vide de l\'API de versement');
            refreshOrders(silent: true);
            return {
              'success': true,
              'message': 'Versement effectué avec succès !',
              'data': {'empty_response': true},
            };
          }

          final jsonResponse = jsonDecode(response.body);
          print('Réponse décodée: $jsonResponse');

          // Vérifier si la réponse indique un succès
          if (jsonResponse.containsKey('success') &&
              jsonResponse['success'] == true) {
            // Rafraîchir automatiquement les commandes après un versement réussi
            refreshOrders(silent: true);
            return {
              'success': true,
              'message':
                  jsonResponse['message'] ?? 'Versement effectué avec succès !',
              'data': jsonResponse,
            };
          } else if (jsonResponse.containsKey('message')) {
            // Si la réponse contient un message mais pas de succès explicite,
            // considérer comme un succès car le statut HTTP est 200 ou 201
            refreshOrders(silent: true);
            return {
              'success': true,
              'message': jsonResponse['message'],
              'data': jsonResponse,
            };
          } else {
            refreshOrders(silent: true);
            return {
              'success': true,
              'message': 'Versement effectué avec succès !',
              'data': jsonResponse,
            };
          }
        } catch (e) {
          print('Erreur lors du décodage de la réponse: $e');

          // Si la réponse contient "success" ou "réussi", considérer comme un succès
          if (response.body.toLowerCase().contains('success') ||
              response.body.toLowerCase().contains('réussi')) {
            refreshOrders(silent: true);
            return {
              'success': true,
              'message': 'Versement effectué avec succès !',
              'data': {'parse_error': true, 'raw_response': response.body},
            };
          }

          // Si la réponse est vide ou invalide, considérer comme un succès
          // car le statut HTTP est 200 ou 201
          refreshOrders(silent: true);
          return {
            'success': true,
            'message': 'Versement effectué avec succès !',
            'data': {'parse_error': true, 'raw_response': response.body},
          };
        }
      } else {
        print('Erreur HTTP: ${response.statusCode}');
        try {
          // Essayer de décoder la réponse d'erreur
          final errorData = jsonDecode(response.body);
          return {
            'success': false,
            'message':
                errorData['message'] ??
                'Erreur lors du versement (${response.statusCode}). Veuillez réessayer.',
            'data': errorData,
          };
        } catch (e) {
          return {
            'success': false,
            'message':
                'Erreur lors du versement (${response.statusCode}). Veuillez réessayer.',
            'data': {'status_code': response.statusCode, 'body': response.body},
          };
        }
      }
    } catch (e) {
      print('Exception lors du versement: $e');
      return {
        'success': false,
        'message':
            'Erreur de connexion. Veuillez vérifier votre connexion internet.',
        'error': e.toString(),
      };
    }
  }

  // Effectuer un versement avec CinetPay
  static Future<Map<String, dynamic>> addVersementWithCinetPay({
    required BuildContext context,
    required String commandeId,
    required double montant,
    String? commandeKey,
    double monnaie = 0,
  }) async {
    // Vérifier si le contexte est toujours monté
    if (!context.mounted) {
      return {'success': false, 'message': 'Contexte non disponible'};
    }
    try {
      // Récupérer les données utilisateur
      final userData = await AuthService.getUserData();
      final token = await AuthService.getAuthToken();

      if (userData == null || token == null) {
        return {
          'success': false,
          'message': 'Vous devez être connecté pour effectuer un versement.',
        };
      }

      final clientId = userData['id_client'].toString();
      final customerName =
          '${userData['nom'] ?? ''} ${userData['prenom'] ?? ''}'.trim();
      final customerEmail = userData['email']?.toString() ?? '';

      // Récupérer le numéro de téléphone avec plusieurs tentatives
      String? customerPhone = userData['telephone']?.toString();
      if (customerPhone == null || customerPhone.isEmpty) {
        customerPhone = userData['phone']?.toString();
      }
      if (customerPhone == null || customerPhone.isEmpty) {
        customerPhone = userData['numero_telephone']?.toString();
      }
      if (customerPhone == null || customerPhone.isEmpty) {
        customerPhone = userData['tel']?.toString();
      }
      if (customerPhone == null || customerPhone.isEmpty) {
        customerPhone = userData['telephone_client']?.toString();
      }

      print('📞 Numéro de téléphone récupéré: "$customerPhone"');
      print('👤 Données utilisateur disponibles: ${userData.keys.toList()}');

      // Validation du numéro de téléphone
      if (customerPhone == null || customerPhone.isEmpty) {
        return {
          'success': false,
          'message':
              'Numéro de téléphone requis pour effectuer un paiement. Veuillez mettre à jour votre profil.',
        };
      }

      print('Initialisation du paiement CinetPay pour la commande $commandeId');

      // Vérifier le contexte avant l'appel async
      if (!context.mounted) {
        return {'success': false, 'message': 'Contexte non disponible'};
      }
      // Initialiser le paiement CinetPay
      final initResult = await CinetPayService.initializePayment(
        context: context,
        amount: montant,
        description: 'Versement pour commande #$commandeId',
        customerName:
            customerName.isNotEmpty ? customerName : 'Client Callitris',
        customerEmail:
            customerEmail.isNotEmpty ? customerEmail : '<EMAIL>',
        customerPhone: customerPhone,
        metadata: {
          'type': TransactionType.versement.name,
          'orderId': commandeId,
          'clientId': clientId,
          'commandeKey': commandeKey,
          'monnaie': monnaie,
        },
      );

      if (!initResult['success']) {
        return {'success': false, 'message': initResult['message']};
      }

      final transaction = initResult['transaction'] as CinetPayTransaction;

      // Sauvegarder la transaction dans l'historique
      await TransactionHistoryService.saveTransaction(transaction);

      // Vérifier le contexte avant le deuxième appel async
      if (!context.mounted) {
        return {'success': false, 'message': 'Contexte non disponible'};
      }

      // Lancer le processus de paiement
      final paymentResult = await CinetPayService.processPayment(
        context: context,
        amount: montant,
        customerPhone:
            customerPhone, // customerPhone ne peut plus être null après validation
        transaction: transaction,
      );

      // Mettre à jour la transaction dans l'historique
      await TransactionHistoryService.saveTransaction(transaction);

      if (paymentResult['success']) {
        // Le paiement a réussi, maintenant enregistrer le versement dans votre système
        final versementResult = await addVersement(
          commandeId,
          montant,
          monnaie: monnaie,
          commandeKey: commandeKey,
          transactionCode: transaction.id,
        );

        if (versementResult['success']) {
          // Mettre à jour les métadonnées de la transaction avec l'ID du versement
          final updatedTransaction = transaction.copyWith(
            metadata: {
              ...transaction.metadata,
              'versementId': versementResult['data']?['id'],
              'backendSaved': true,
            },
          );

          await TransactionHistoryService.saveTransaction(updatedTransaction);

          return {
            'success': true,
            'message': 'Versement effectué avec succès via CinetPay !',
            'transaction': updatedTransaction,
            'versementData': versementResult['data'],
          };
        } else {
          // Le paiement a réussi mais l'enregistrement backend a échoué
          print(
            'Paiement CinetPay réussi mais échec de l\'enregistrement backend: ${versementResult['message']}',
          );

          return {
            'success': false,
            'message':
                'Paiement effectué mais erreur d\'enregistrement. Contactez le support.',
            'transaction': transaction,
            'paymentSuccess': true,
            'backendError': versementResult['message'],
          };
        }
      } else {
        return {
          'success': false,
          'message': paymentResult['message'],
          'transaction': transaction,
        };
      }
    } catch (e) {
      print('Exception lors du versement CinetPay: $e');
      return {'success': false, 'message': 'Erreur lors du paiement: $e'};
    }
  }

  // Récupérer les commandes actives de l'utilisateur
  static Future<Map<String, dynamic>> getActiveOrders() async {
    try {
      // Récupérer l'ID client et le token
      final userData = await AuthService.getUserData();
      final token = await AuthService.getAuthToken();

      if (userData == null || token == null) {
        print('Erreur: Utilisateur non connecté ou token manquant');
        return {
          'success': false,
          'message': 'Vous devez être connecté pour voir vos commandes.',
          'activeOrders': [],
        };
      }

      final clientId = userData['id_client'].toString();

      print('Récupération des commandes actives pour le client: $clientId');

      final response = await http
          .get(
            Uri.parse(
              '${ApiConfig.baseUrl}/commande/getCommandeClient.php?id_client=$clientId',
            ),
            headers: {'Authorization': 'Bearer $token'},
          )
          .timeout(const Duration(seconds: 30));

      print('Réponse API commandes (status: ${response.statusCode})');

      if (response.statusCode == 200) {
        try {
          final List<dynamic> ordersData = jsonDecode(response.body);

          print('Nombre total de commandes: ${ordersData.length}');

          // Filtrer les commandes actives (reste > 0)
          final List<Map<String, dynamic>> activeOrders = [];

          for (var order in ordersData) {
            final Map<String, dynamic> orderMap = Map<String, dynamic>.from(
              order,
            );

            // Afficher les clés disponibles pour le débogage
            print(
              'Clés disponibles dans la commande: ${orderMap.keys.toList()}',
            );

            // Déterminer si la commande est active
            final int reste =
                int.tryParse(orderMap['reste']?.toString() ?? '0') ?? 0;

            if (reste > 0) {
              // Enrichir les données de la commande avec des informations supplémentaires

              // Calculer le montant versé et restant
              final int paye =
                  int.tryParse(orderMap['paye']?.toString() ?? '0') ?? 0;
              final int jour =
                  int.tryParse(orderMap['jour']?.toString() ?? '0') ?? 0;
              final int journalier =
                  int.tryParse(
                    orderMap['journalier']?.toString() ??
                        orderMap['cout_journalier_kit']?.toString() ??
                        '0',
                  ) ??
                  0;

              orderMap['montantVerse'] = paye * journalier;
              orderMap['montantRestant'] = reste * journalier;
              orderMap['progress'] =
                  jour > 0 ? (paye / jour).clamp(0.0, 1.0) : 0.0;

              // Déterminer la date du prochain paiement
              final DateTime now = DateTime.now();
              final String nextPaymentDate =
                  '${now.day.toString().padLeft(2, '0')}/${now.month.toString().padLeft(2, '0')}/${now.year}';
              orderMap['nextPaymentDate'] = nextPaymentDate;

              activeOrders.add(orderMap);
            }
          }

          print('Nombre de commandes actives trouvées: ${activeOrders.length}');

          return {'success': true, 'activeOrders': activeOrders};
        } catch (e) {
          print('Erreur lors du décodage de la réponse: $e');
          return {
            'success': false,
            'message': 'Format de réponse invalide.',
            'activeOrders': [],
          };
        }
      } else {
        print('Erreur API: ${response.statusCode}');
        return {
          'success': false,
          'message':
              'Erreur lors de la récupération des commandes (${response.statusCode}).',
          'activeOrders': [],
        };
      }
    } catch (e) {
      print('Exception lors de la récupération des commandes actives: $e');
      return {
        'success': false,
        'message':
            'Erreur de connexion. Veuillez vérifier votre connexion internet.',
        'activeOrders': [],
      };
    }
  }

  // Récupérer l'historique des versements d'une commande
  static Future<Map<String, dynamic>> getOrderVersements(String orderId) async {
    try {
      // Vérifier si l'ID de commande est valide
      if (orderId.isEmpty) {
        return {
          'success': false,
          'message': 'ID de commande invalide',
          'versements': [], // Toujours retourner une liste vide, jamais null
        };
      }

      // Récupérer le token d'authentification
      final token = await AuthService.getAuthToken();

      if (token == null) {
        return {
          'success': false,
          'message':
              'Vous devez être connecté pour voir l\'historique des versements.',
          'versements': [], // Toujours retourner une liste vide, jamais null
        };
      }

      print('Récupération des versements pour la commande: $orderId');

      // Construire l'URL de l'API
      final apiUrl =
          '${ApiConfig.baseUrl}/commande/getVersementsCompte.php?id=$orderId';

      // Effectuer la requête HTTP
      final response = await http
          .get(Uri.parse(apiUrl), headers: {'Authorization': 'Bearer $token'})
          .timeout(const Duration(seconds: 30));

      print(
        'Réponse API versements (status: ${response.statusCode}): ${response.body}',
      );

      if (response.statusCode == 200) {
        try {
          // Vérifier si la réponse est vide
          if (response.body.isEmpty ||
              response.body == 'null' ||
              response.body == '[]') {
            return {
              'success': true,
              'message': 'Aucun versement trouvé',
              'versements':
                  [], // Toujours retourner une liste vide, jamais null
            };
          }

          final dynamic decodedData = jsonDecode(response.body);

          // Vérifier si la réponse est une liste
          if (decodedData is! List) {
            print(
              'La réponse n\'est pas une liste: ${decodedData.runtimeType}',
            );
            return {
              'success': true,
              'message': 'Format de réponse inattendu',
              'versements':
                  [], // Toujours retourner une liste vide, jamais null
            };
          }

          final List<dynamic> versementsData = decodedData;

          // Formater les données des versements
          final List<Map<String, dynamic>> formattedVersements =
              versementsData.map((versement) {
                // S'assurer que versement est un Map
                if (versement is! Map) {
                  return <String, dynamic>{
                    'date': 'Date inconnue',
                    'montant': '0',
                    'monnaie': '0',
                    'methode_paiement': 'Inconnu',
                    'date_formatted': 'Date inconnue',
                  };
                }

                // Convertir en Map<String, dynamic> pour éviter les problèmes de type
                final Map<String, dynamic> versementMap =
                    Map<String, dynamic>.from(versement);

                // Formater la date si elle existe
                String formattedDate = versementMap['date']?.toString() ?? '';
                if (formattedDate.isNotEmpty) {
                  try {
                    final DateTime date = DateTime.parse(formattedDate);
                    formattedDate =
                        '${date.day.toString().padLeft(2, '0')}/${date.month.toString().padLeft(2, '0')}/${date.year}';
                    versementMap['date_formatted'] = formattedDate;
                  } catch (e) {
                    print('Erreur lors du formatage de la date: $e');
                    versementMap['date_formatted'] = formattedDate;
                  }
                } else {
                  versementMap['date_formatted'] = 'Date inconnue';
                }

                return versementMap;
              }).toList();

          return {
            'success': true,
            'versements': formattedVersements, // Liste formatée, jamais null
          };
        } catch (e) {
          print('Erreur lors du décodage de la réponse: $e');
          return {
            'success': false,
            'message': 'Format de réponse invalide: $e',
            'versements': [], // Toujours retourner une liste vide, jamais null
          };
        }
      } else {
        print('Erreur API: ${response.statusCode}');
        return {
          'success': false,
          'message':
              'Erreur lors de la récupération des versements (${response.statusCode}).',
          'versements': [], // Toujours retourner une liste vide, jamais null
        };
      }
    } catch (e) {
      print('Exception lors de la récupération des versements: $e');
      return {
        'success': false,
        'message': 'Erreur de connexion: $e',
        'versements': [], // Toujours retourner une liste vide, jamais null
      };
    }
  }

  // Effectuer un versement avec Wave
  static Future<Map<String, dynamic>> addVersementWithWave({
    required BuildContext context,
    required String? commandeId,
    required double montant,
    String? commandeKey,
    double? monnaie = 0,
    required String itemId,
  }) async {
    // Vérifier si le contexte est toujours monté
    if (!context.mounted) {
      return {'success': false, 'message': 'Contexte non disponible'};
    }
    try {
      // Récupérer les données utilisateur
      final userData = await AuthService.getUserData();
      final token = await AuthService.getAuthToken();

      if (userData == null || token == null) {
        return {
          'success': false,
          'message': 'Vous devez être connecté pour effectuer un versement.',
        };
      }

      final clientId = userData['id_client'].toString();
      final customerName =
          '${userData['nom'] ?? ''} ${userData['prenom'] ?? ''}'.trim();
      final customerEmail = userData['email']?.toString() ?? '';

      // Récupérer le numéro de téléphone avec plusieurs tentatives
      String? customerPhone = userData['telephone']?.toString();
      if (customerPhone == null || customerPhone.isEmpty) {
        customerPhone = userData['phone']?.toString();
      }
      if (customerPhone == null || customerPhone.isEmpty) {
        customerPhone = userData['numero_telephone']?.toString();
      }
      if (customerPhone == null || customerPhone.isEmpty) {
        customerPhone = userData['tel']?.toString();
      }
      if (customerPhone == null || customerPhone.isEmpty) {
        customerPhone = userData['telephone_client']?.toString();
      }

      print('📞 Numéro de téléphone récupéré: "$customerPhone"');
      print('👤 Données utilisateur disponibles: ${userData.keys.toList()}');

      // Validation du numéro de téléphone
      if (customerPhone == null || customerPhone.isEmpty) {
        return {
          'success': false,
          'message':
              'Numéro de téléphone requis pour effectuer un paiement. Veuillez mettre à jour votre profil.',
        };
      }

      print('Initialisation du paiement CinetPay pour la commande $commandeId');

      // Vérifier le contexte avant l'appel async
      if (!context.mounted) {
        return {'success': false, 'message': 'Contexte non disponible'};
      }

      final Map<String, dynamic> requestBody = {
        'amount': montant,
        'phone': customerPhone,
        'command_id': commandeId,
        'client_id': clientId,
        'item_id': itemId,
      };

      final response = await http.post(
        Uri.parse("${ApiConfig.baseUrl}/paiement/init_wave.php"),
        body: json.encode(requestBody),
        headers: {'Authorization': token, 'Content-Type': 'application/json'},
      );
      print(
        'Réponse de l\'API d\'initialisation de paiement: ${response.body}',
      );

      if (response.statusCode == 200) {
        String responseBody = response.body.trim();
        if (!responseBody.startsWith('{') && !responseBody.startsWith('[')) {
          print('Erreur serveur: $responseBody');
          return {'success': false, 'message': 'Erreur serveur: $responseBody'};
        }

        try {
          final Map<String, dynamic> responseData = jsonDecode(responseBody);

          if (responseData.containsKey('wave_launch_url')) {
            final waveLaunchUrl =
                responseData['wave_launch_url']?.toString() ?? '';
            final paymentId = responseData['id']?.toString() ?? '';

            if (paymentId.isNotEmpty) {
              saveData("payment_code", paymentId);
              saveData("commande_id", commandeId!);
              saveData("client_id", clientId);
              saveData("pending_montant", montant.toString());
              saveData("pending_monnaie", monnaie.toString());
            }

            if (waveLaunchUrl.isNotEmpty) {
              await _launchPaymentUrl(waveLaunchUrl, context);
            }

            return {
              'success': true,
              'message': 'Redirection vers Wave effectuée',
              'waveLaunchUrl': waveLaunchUrl,
              'paymentId': paymentId,
            };
          } else {
            return {
              'success': false,
              'message': 'URL de lancement de Wave non trouvée',
            };
          }
        } catch (e) {
          print('Erreur parsing JSON: $e');
          return {
            'success': false,
            'message': 'Format de réponse invalide: $e',
          };
        }
      } else if (response.statusCode == 401) {
        return {
          'success': false,
          'message': 'Session expirée. Veuillez vous reconnecter.',
        };
      } else {
        try {
          final errorData = jsonDecode(response.body);
          return {
            'success': false,
            'message': errorData['message'] ?? 'Erreur serveur',
          };
        } catch (e) {
          return {'success': false, 'message': 'Erreur serveur'};
        }
      }
    } catch (error) {
      print("Erreur lors du paiement Wave: $error");
      return {'success': false, 'message': 'Erreur lors du paiement: $error'};
    } finally {
      // ignore: control_flow_in_finally
      return {'success': true, 'message': 'Redirection vers Wave effectuée'};
    }
  }

  static Future<void> _launchPaymentUrl(
    String url,
    BuildContext context,
  ) async {
    try {
      await launchUrl(Uri.parse(url), mode: LaunchMode.externalApplication);
    } catch (e) {
      print("Erreur: $e");
      await Clipboard.setData(ClipboardData(text: url));
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('URL copiée dans le presse-papier: $url'),
          backgroundColor: Colors.blue,
        ),
      );
    }
  }

  static Future<String?> readData(String name) async {
    SharedPreferences prefs = await SharedPreferences.getInstance();
    String? token = prefs.getString(name);
    return token;
  }

  static Future<void> deleteData(String name) async {
    print('Suppression de la donnée locale: $name');
    SharedPreferences prefs = await SharedPreferences.getInstance();
    await prefs.remove(name);
  }

  static Future<void> saveData(String name, String data) async {
    SharedPreferences prefs = await SharedPreferences.getInstance();
    await prefs.setString(name, data);
  }
}
