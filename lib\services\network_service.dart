import 'dart:async';
import 'dart:convert';
import 'dart:io';
import 'package:http/http.dart' as http;
import 'package:connectivity_plus/connectivity_plus.dart';
import 'package:callitris/services/smart_cache_service.dart';
import 'package:callitris/services/error_handling_service.dart';

/// Service réseau optimisé avec cache intelligent, retry et gestion d'erreurs
class NetworkService {
  static const String _tag = '[NetworkService]';
  static NetworkService? _instance;
  static NetworkService get instance => _instance ??= NetworkService._();

  NetworkService._();

  // Configuration des timeouts optimisés
  static const Duration _connectTimeout = Duration(seconds: 5);
  static const Duration _receiveTimeout = Duration(seconds: 8);
  static const Duration _sendTimeout = Duration(seconds: 8);

  // Client HTTP réutilisable
  late final http.Client _httpClient;
  final SmartCacheService _cache = SmartCacheService.instance;

  /// Initialise le service réseau
  Future<void> initialize() async {
    _httpClient = http.Client();
    print('$_tag: Service réseau initialisé');
  }

  /// Effectue une requête GET optimisée avec cache
  Future<Map<String, dynamic>> get(
    String url, {
    Map<String, String>? headers,
    Duration? cacheTtl,
    bool useCache = true,
    bool forceRefresh = false,
    String? cacheKey,
  }) async {
    final key = cacheKey ?? _generateCacheKey('GET', url, headers);

    if (useCache && !forceRefresh) {
      try {
        final cachedResponse = await _cache.getOrFetch<Map<String, dynamic>>(
          key,
          () => _performGet(url, headers),
          ttl: cacheTtl ?? const Duration(minutes: 5),
          useStaleWhileRevalidate: true,
        );
        return cachedResponse;
      } catch (e) {
        print('$_tag: Erreur cache pour GET $url: $e');
      }
    }

    return await _performGet(url, headers);
  }

  /// Effectue une requête POST optimisée
  Future<Map<String, dynamic>> post(
    String url, {
    Map<String, String>? headers,
    dynamic body,
    bool useCache = false,
    Duration? cacheTtl,
    String? cacheKey,
  }) async {
    if (useCache) {
      final key = cacheKey ?? _generateCacheKey('POST', url, headers, body);
      return await _cache.getOrFetch<Map<String, dynamic>>(
        key,
        () => _performPost(url, headers, body),
        ttl: cacheTtl ?? const Duration(minutes: 2),
      );
    }

    return await _performPost(url, headers, body);
  }

  /// Effectue plusieurs requêtes en parallèle
  Future<List<Map<String, dynamic>>> parallel(
    List<Future<Map<String, dynamic>> Function()> requests, {
    int? concurrency,
  }) async {
    if (concurrency != null && concurrency < requests.length) {
      // Limiter la concurrence
      final results = <Map<String, dynamic>>[];
      for (int i = 0; i < requests.length; i += concurrency) {
        final batch = requests.skip(i).take(concurrency);
        final batchResults = await Future.wait(
          batch.map((request) => request()),
        );
        results.addAll(batchResults);
      }
      return results;
    }

    return await Future.wait(requests.map((request) => request()));
  }

  /// Vérifie la connectivité réseau
  Future<bool> isConnected() async {
    try {
      final connectivityResults = await Connectivity().checkConnectivity();
      return connectivityResults.isNotEmpty &&
          !connectivityResults.every(
            (result) => result == ConnectivityResult.none,
          );
    } catch (e) {
      print('$_tag: Erreur vérification connectivité: $e');
      return false;
    }
  }

  /// Effectue une requête GET avec retry intelligent
  Future<Map<String, dynamic>> _performGet(
    String url,
    Map<String, String>? headers,
  ) async {
    return await _executeWithRetry(() async {
      final response = await _httpClient
          .get(Uri.parse(url), headers: headers)
          .timeout(_receiveTimeout);

      return _processResponse(response, url);
    });
  }

  /// Effectue une requête POST avec retry intelligent
  Future<Map<String, dynamic>> _performPost(
    String url,
    Map<String, String>? headers,
    dynamic body,
  ) async {
    return await _executeWithRetry(() async {
      final response = await _httpClient
          .post(
            Uri.parse(url),
            headers: headers,
            body: body is String ? body : jsonEncode(body),
          )
          .timeout(_sendTimeout);

      return _processResponse(response, url);
    });
  }

  /// Exécute une requête avec retry intelligent
  Future<T> _executeWithRetry<T>(
    Future<T> Function() operation, {
    int maxRetries = 3,
    Duration initialDelay = const Duration(milliseconds: 300),
  }) async {
    int attempt = 0;
    Duration delay = initialDelay;

    while (attempt < maxRetries) {
      try {
        return await operation();
      } catch (e) {
        attempt++;

        // Ne pas retry pour certaines erreurs
        if (_shouldNotRetry(e) || attempt >= maxRetries) {
          rethrow;
        }

        // Délai exponentiel avec jitter
        final jitter = Duration(
          milliseconds:
              (delay.inMilliseconds *
                      0.2 *
                      (DateTime.now().millisecondsSinceEpoch % 100) /
                      100)
                  .round(),
        );

        print(
          '$_tag: Tentative $attempt échouée, retry dans ${delay.inMilliseconds}ms',
        );
        await Future.delayed(delay + jitter);
        delay = Duration(milliseconds: (delay.inMilliseconds * 1.5).round());
      }
    }

    throw Exception('Échec après $maxRetries tentatives');
  }

  /// Traite la réponse HTTP
  Map<String, dynamic> _processResponse(http.Response response, String url) {
    if (response.statusCode >= 200 && response.statusCode < 300) {
      try {
        final data = jsonDecode(response.body);
        return data is Map<String, dynamic> ? data : {'data': data};
      } catch (e) {
        return {'data': response.body};
      }
    } else {
      throw HttpException(
        'Erreur HTTP ${response.statusCode} pour $url: ${response.body}',
        uri: Uri.parse(url),
      );
    }
  }

  /// Détermine si une erreur ne doit pas être retryée
  bool _shouldNotRetry(dynamic error) {
    if (error is HttpException) {
      final statusCode = int.tryParse(error.message.split(' ')[2]) ?? 0;
      // Ne pas retry pour les erreurs client (4xx)
      return statusCode >= 400 && statusCode < 500;
    }
    return false;
  }

  /// Génère une clé de cache unique
  String _generateCacheKey(
    String method,
    String url, [
    Map<String, String>? headers,
    dynamic body,
  ]) {
    final buffer =
        StringBuffer()
          ..write(method)
          ..write('_')
          ..write(url.hashCode);

    if (headers != null && headers.isNotEmpty) {
      buffer.write('_${headers.hashCode}');
    }

    if (body != null) {
      buffer.write('_${body.hashCode}');
    }

    return buffer.toString();
  }

  /// Nettoie les ressources
  void dispose() {
    _httpClient.close();
  }
}
