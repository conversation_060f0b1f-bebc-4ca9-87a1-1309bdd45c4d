import 'package:flutter/material.dart';
import 'package:rxdart/rxdart.dart';

/// StreamBuilder optimisé qui évite les rebuilds inutiles
class OptimizedStreamBuilder<T> extends StatefulWidget {
  final Stream<T> stream;
  final T? initialData;
  final Widget Function(BuildContext context, T data) builder;
  final Widget Function(BuildContext context, Object error)? errorBuilder;
  final Widget Function(BuildContext context)? loadingBuilder;
  final bool Function(T? previous, T current)? shouldRebuild;
  final Duration? debounceTime;

  const OptimizedStreamBuilder({
    super.key,
    required this.stream,
    required this.builder,
    this.initialData,
    this.errorBuilder,
    this.loadingBuilder,
    this.shouldRebuild,
    this.debounceTime,
  });

  @override
  State<OptimizedStreamBuilder<T>> createState() => _OptimizedStreamBuilderState<T>();
}

class _OptimizedStreamBuilderState<T> extends State<OptimizedStreamBuilder<T>> {
  late Stream<T> _optimizedStream;
  T? _lastData;
  Object? _lastError;
  bool _hasData = false;

  @override
  void initState() {
    super.initState();
    _setupOptimizedStream();
    
    if (widget.initialData != null) {
      _lastData = widget.initialData;
      _hasData = true;
    }
  }

  void _setupOptimizedStream() {
    _optimizedStream = widget.stream;

    // Appliquer le debouncing si spécifié
    if (widget.debounceTime != null) {
      _optimizedStream = _optimizedStream.debounceTime(widget.debounceTime!);
    }

    // Appliquer la logique de shouldRebuild si spécifiée
    if (widget.shouldRebuild != null) {
      _optimizedStream = _optimizedStream.distinct((previous, current) {
        return !widget.shouldRebuild!(previous, current);
      });
    } else {
      // Par défaut, éviter les doublons
      _optimizedStream = _optimizedStream.distinct();
    }
  }

  @override
  void didUpdateWidget(OptimizedStreamBuilder<T> oldWidget) {
    super.didUpdateWidget(oldWidget);
    
    if (oldWidget.stream != widget.stream ||
        oldWidget.debounceTime != widget.debounceTime ||
        oldWidget.shouldRebuild != widget.shouldRebuild) {
      _setupOptimizedStream();
    }
  }

  @override
  Widget build(BuildContext context) {
    return StreamBuilder<T>(
      stream: _optimizedStream,
      initialData: widget.initialData,
      builder: (context, snapshot) {
        // Gestion des erreurs
        if (snapshot.hasError) {
          _lastError = snapshot.error;
          if (widget.errorBuilder != null) {
            return widget.errorBuilder!(context, snapshot.error!);
          }
          return _buildDefaultError(snapshot.error!);
        }

        // Gestion du chargement
        if (!snapshot.hasData && !_hasData) {
          if (widget.loadingBuilder != null) {
            return widget.loadingBuilder!(context);
          }
          return _buildDefaultLoading();
        }

        // Mise à jour des données
        if (snapshot.hasData) {
          _lastData = snapshot.data;
          _hasData = true;
          _lastError = null;
        }

        return widget.builder(context, _lastData as T);
      },
    );
  }

  Widget _buildDefaultLoading() {
    return const Center(
      child: SizedBox(
        width: 24,
        height: 24,
        child: CircularProgressIndicator(strokeWidth: 2),
      ),
    );
  }

  Widget _buildDefaultError(Object error) {
    return Center(
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          const Icon(
            Icons.error_outline,
            color: Colors.red,
            size: 32,
          ),
          const SizedBox(height: 8),
          Text(
            'Erreur: ${error.toString()}',
            style: const TextStyle(color: Colors.red),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }
}

/// Builder optimisé pour plusieurs streams
class OptimizedMultiStreamBuilder extends StatefulWidget {
  final List<Stream> streams;
  final Widget Function(BuildContext context, List<dynamic> data) builder;
  final Widget Function(BuildContext context, Object error)? errorBuilder;
  final Widget Function(BuildContext context)? loadingBuilder;
  final Duration? debounceTime;

  const OptimizedMultiStreamBuilder({
    super.key,
    required this.streams,
    required this.builder,
    this.errorBuilder,
    this.loadingBuilder,
    this.debounceTime,
  });

  @override
  State<OptimizedMultiStreamBuilder> createState() => _OptimizedMultiStreamBuilderState();
}

class _OptimizedMultiStreamBuilderState extends State<OptimizedMultiStreamBuilder> {
  late Stream<List<dynamic>> _combinedStream;

  @override
  void initState() {
    super.initState();
    _setupCombinedStream();
  }

  void _setupCombinedStream() {
    if (widget.streams.isEmpty) {
      _combinedStream = Stream.value([]);
      return;
    }

    if (widget.streams.length == 1) {
      _combinedStream = widget.streams.first.map((data) => [data]);
    } else {
      _combinedStream = CombineLatestStream.list(widget.streams);
    }

    // Appliquer le debouncing si spécifié
    if (widget.debounceTime != null) {
      _combinedStream = _combinedStream.debounceTime(widget.debounceTime!);
    }

    // Éviter les doublons
    _combinedStream = _combinedStream.distinct();
  }

  @override
  void didUpdateWidget(OptimizedMultiStreamBuilder oldWidget) {
    super.didUpdateWidget(oldWidget);
    
    if (oldWidget.streams != widget.streams ||
        oldWidget.debounceTime != widget.debounceTime) {
      _setupCombinedStream();
    }
  }

  @override
  Widget build(BuildContext context) {
    return StreamBuilder<List<dynamic>>(
      stream: _combinedStream,
      builder: (context, snapshot) {
        if (snapshot.hasError) {
          if (widget.errorBuilder != null) {
            return widget.errorBuilder!(context, snapshot.error!);
          }
          return _buildDefaultError(snapshot.error!);
        }

        if (!snapshot.hasData) {
          if (widget.loadingBuilder != null) {
            return widget.loadingBuilder!(context);
          }
          return _buildDefaultLoading();
        }

        return widget.builder(context, snapshot.data!);
      },
    );
  }

  Widget _buildDefaultLoading() {
    return const Center(
      child: SizedBox(
        width: 24,
        height: 24,
        child: CircularProgressIndicator(strokeWidth: 2),
      ),
    );
  }

  Widget _buildDefaultError(Object error) {
    return Center(
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          const Icon(
            Icons.error_outline,
            color: Colors.red,
            size: 32,
          ),
          const SizedBox(height: 8),
          Text(
            'Erreur: ${error.toString()}',
            style: const TextStyle(color: Colors.red),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }
}

/// Mixin pour optimiser les widgets avec des streams
mixin StreamOptimizationMixin<T extends StatefulWidget> on State<T> {
  final Map<String, dynamic> _streamCache = {};
  
  /// Cache une valeur de stream pour éviter les rebuilds inutiles
  bool cacheStreamValue<V>(String key, V value) {
    final previousValue = _streamCache[key];
    if (previousValue != value) {
      _streamCache[key] = value;
      return true; // Valeur changée, rebuild nécessaire
    }
    return false; // Valeur inchangée, pas de rebuild
  }
  
  /// Récupère une valeur cachée
  V? getCachedValue<V>(String key) {
    return _streamCache[key] as V?;
  }
  
  /// Nettoie le cache
  void clearStreamCache() {
    _streamCache.clear();
  }
  
  @override
  void dispose() {
    clearStreamCache();
    super.dispose();
  }
}
