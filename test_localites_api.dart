/// Test pour vérifier la structure de l'API des localités
/// Ce fichier peut être supprimé après les tests

import 'package:flutter/material.dart';
import 'package:callitris/config/api_config.dart';
import 'package:http/http.dart' as http;
import 'dart:convert';

void main() {
  runApp(const TestLocalitesApp());
}

class TestLocalitesApp extends StatelessWidget {
  const TestLocalitesApp({super.key});

  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      title: 'Test Localités API',
      home: const TestLocalitesScreen(),
    );
  }
}

class TestLocalitesScreen extends StatefulWidget {
  const TestLocalitesScreen({super.key});

  @override
  State<TestLocalitesScreen> createState() => _TestLocalitesScreenState();
}

class _TestLocalitesScreenState extends State<TestLocalitesScreen> {
  bool _isLoading = false;
  String _rawResponse = '';
  String _errorMessage = '';
  List<Map<String, dynamic>> _localites = [];

  @override
  void initState() {
    super.initState();
    _testAPI();
  }

  Future<void> _testAPI() async {
    setState(() {
      _isLoading = true;
      _rawResponse = '';
      _errorMessage = '';
      _localites = [];
    });

    try {
      print('🔄 Test de l\'API des localités...');
      print('URL: ${ApiConfig.baseUrl}/local/get_localites.php');
      
      final response = await http.get(
        Uri.parse('${ApiConfig.baseUrl}/local/get_localites.php'),
      );

      print('📡 Statut de la réponse: ${response.statusCode}');
      print('📄 Réponse brute: ${response.body}');

      setState(() {
        _rawResponse = response.body;
      });

      if (response.statusCode == 200) {
        final dynamic jsonResponse = json.decode(response.body);
        print('🔍 Type de la réponse: ${jsonResponse.runtimeType}');
        print('📊 Réponse décodée: $jsonResponse');

        List<dynamic> data;

        // Logique de parsing identique à RegisterScreen
        if (jsonResponse is List) {
          data = jsonResponse;
          print('✅ Réponse directe en tableau');
        } else if (jsonResponse is Map<String, dynamic>) {
          print('🗂️ Réponse en objet, recherche du tableau...');
          print('🔑 Clés disponibles: ${jsonResponse.keys.toList()}');
          
          if (jsonResponse.containsKey('data')) {
            data = jsonResponse['data'] as List<dynamic>;
            print('✅ Tableau trouvé dans "data"');
          } else if (jsonResponse.containsKey('localites')) {
            data = jsonResponse['localites'] as List<dynamic>;
            print('✅ Tableau trouvé dans "localites"');
          } else if (jsonResponse.containsKey('results')) {
            data = jsonResponse['results'] as List<dynamic>;
            print('✅ Tableau trouvé dans "results"');
          } else {
            // Chercher la première valeur qui est une liste
            final listValue = jsonResponse.values.firstWhere(
              (value) => value is List,
              orElse: () => [],
            );
            data = listValue as List<dynamic>;
            print('✅ Tableau trouvé automatiquement');
          }
        } else {
          throw Exception('Format de réponse inattendu: ${jsonResponse.runtimeType}');
        }

        print('📋 Données extraites: $data');
        print('📊 Nombre d\'éléments: ${data.length}');

        if (data.isNotEmpty) {
          print('🔍 Premier élément: ${data.first}');
          print('🔑 Clés du premier élément: ${(data.first as Map).keys.toList()}');
        }

        final localites = data.map((item) => {
          'id': item['id']?.toString() ?? '',
          'nom': item['nom']?.toString() ?? '',
        }).toList();

        setState(() {
          _localites = localites;
          _isLoading = false;
        });

        print('✅ Localités traitées: $_localites');
        print('🎉 Test réussi !');

      } else {
        throw Exception('Erreur HTTP ${response.statusCode}: ${response.body}');
      }
    } catch (e) {
      setState(() {
        _isLoading = false;
        _errorMessage = e.toString();
      });
      print('❌ Erreur: $e');
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Test API Localités'),
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: _testAPI,
          ),
        ],
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // État du test
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'État du Test',
                      style: Theme.of(context).textTheme.titleLarge,
                    ),
                    const SizedBox(height: 8),
                    if (_isLoading) ...[
                      const Row(
                        children: [
                          SizedBox(
                            width: 16,
                            height: 16,
                            child: CircularProgressIndicator(strokeWidth: 2),
                          ),
                          SizedBox(width: 8),
                          Text('Test en cours...'),
                        ],
                      ),
                    ] else if (_errorMessage.isNotEmpty) ...[
                      Row(
                        children: [
                          const Icon(Icons.error, color: Colors.red),
                          const SizedBox(width: 8),
                          Expanded(
                            child: Text(
                              'Erreur: $_errorMessage',
                              style: const TextStyle(color: Colors.red),
                            ),
                          ),
                        ],
                      ),
                    ] else if (_localites.isNotEmpty) ...[
                      Row(
                        children: [
                          const Icon(Icons.check_circle, color: Colors.green),
                          const SizedBox(width: 8),
                          Text('Succès: ${_localites.length} localités trouvées'),
                        ],
                      ),
                    ],
                  ],
                ),
              ),
            ),

            const SizedBox(height: 16),

            // Réponse brute
            if (_rawResponse.isNotEmpty) ...[
              Card(
                child: Padding(
                  padding: const EdgeInsets.all(16),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'Réponse Brute de l\'API',
                        style: Theme.of(context).textTheme.titleMedium,
                      ),
                      const SizedBox(height: 8),
                      Container(
                        width: double.infinity,
                        padding: const EdgeInsets.all(12),
                        decoration: BoxDecoration(
                          color: Colors.grey[100],
                          borderRadius: BorderRadius.circular(8),
                        ),
                        child: Text(
                          _rawResponse,
                          style: const TextStyle(
                            fontFamily: 'monospace',
                            fontSize: 12,
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              ),
              const SizedBox(height: 16),
            ],

            // Localités traitées
            if (_localites.isNotEmpty) ...[
              Card(
                child: Padding(
                  padding: const EdgeInsets.all(16),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'Localités Traitées (${_localites.length})',
                        style: Theme.of(context).textTheme.titleMedium,
                      ),
                      const SizedBox(height: 8),
                      ...(_localites.take(10).map((localite) => ListTile(
                        dense: true,
                        leading: CircleAvatar(
                          radius: 12,
                          child: Text(
                            localite['id'] ?? '?',
                            style: const TextStyle(fontSize: 10),
                          ),
                        ),
                        title: Text(localite['nom'] ?? 'Sans nom'),
                        subtitle: Text('ID: ${localite['id']}'),
                      ))),
                      if (_localites.length > 10) ...[
                        const Divider(),
                        Text('... et ${_localites.length - 10} autres'),
                      ],
                    ],
                  ),
                ),
              ),
            ],
          ],
        ),
      ),
    );
  }
}
