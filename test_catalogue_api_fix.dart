import 'dart:convert';

/// Script de test pour vérifier la correction de l'API catalogue
void main() {
  print('🧪 Test de la correction de l\'API catalogue...\n');
  
  // Simulation de la réponse API réelle que vous avez fournie
  final String apiResponse = '''
[
  [
    {
      "id_kit": 354,
      "duree_id": 31,
      "option_kit": "pack 1",
      "nom_produit": null,
      "cout_journalier_kit": 1000,
      "montant_total_kit": 150000,
      "prix_achat_kit": 100000,
      "benefice_kit": 50000,
      "total_prod_kit": 10,
      "photo_kit": "app/public/img/kit/30ce0a32e0dc2b824310d18966ccb83478d39e0cbague.jpg",
      "description_kit": "",
      "date_ajout_kit": "2025-06-29",
      "heure_ajout_kit": "22:09:56"
    }
  ]
]
  ''';
  
  print('📥 Réponse API reçue:');
  print(apiResponse);
  print('');
  
  try {
    // Décoder la réponse JSON
    final jsonResponse = jsonDecode(apiResponse);
    
    print('🔍 Analyse de la structure:');
    print('Type de la réponse: ${jsonResponse.runtimeType}');
    print('Est-ce une List? ${jsonResponse is List}');
    print('Nombre d\'éléments au niveau racine: ${jsonResponse.length}');
    print('');
    
    // Appliquer la logique de correction
    final List<Map<String, dynamic>> products = [];
    
    if (jsonResponse is List && jsonResponse.isNotEmpty) {
      for (var outerItem in jsonResponse) {
        print('📦 Traitement de l\'élément externe: ${outerItem.runtimeType}');
        
        if (outerItem is List) {
          print('   └─ C\'est un tableau imbriqué avec ${outerItem.length} éléments');
          
          for (var innerItem in outerItem) {
            print('   📋 Élément interne: ${innerItem.runtimeType}');
            
            if (innerItem is Map<String, dynamic>) {
              products.add(innerItem);
              print('   ✅ Produit ajouté: ${innerItem['option_kit']} (ID: ${innerItem['id_kit']})');
            } else if (innerItem is Map) {
              final convertedItem = Map<String, dynamic>.from(innerItem);
              products.add(convertedItem);
              print('   ✅ Produit converti et ajouté: ${convertedItem['option_kit']}');
            }
          }
        }
      }
    }
    
    print('');
    print('📊 Résultats:');
    print('Nombre total de produits extraits: ${products.length}');
    print('');
    
    if (products.isNotEmpty) {
      print('🎯 Détails du premier produit:');
      final firstProduct = products.first;
      firstProduct.forEach((key, value) {
        print('   $key: $value');
      });
      
      print('');
      print('💰 Informations de prix:');
      print('   Prix total: ${firstProduct['montant_total_kit']} FCFA');
      print('   Prix journalier: ${firstProduct['cout_journalier_kit']} FCFA/jour');
      print('   Prix d\'achat: ${firstProduct['prix_achat_kit']} FCFA');
      print('   Bénéfice: ${firstProduct['benefice_kit']} FCFA');
      
      print('');
      print('📸 Image:');
      print('   Chemin: ${firstProduct['photo_kit']}');
      print('   URL complète: https://dev-mani.io/${firstProduct['photo_kit']}');
    }
    
    print('');
    print('✅ CORRECTION RÉUSSIE !');
    print('   • Structure imbriquée [[{...}]] gérée correctement');
    print('   • Paramètre URL changé de carnet_id vers livret_id');
    print('   • Extraction des produits fonctionnelle');
    
  } catch (e) {
    print('❌ Erreur lors du test: $e');
  }
  
  print('\n🏁 Test terminé.');
}
