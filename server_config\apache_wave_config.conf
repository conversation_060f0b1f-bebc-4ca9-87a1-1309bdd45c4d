# Configuration Apache pour les callbacks Wave - Callitris
# 
# Ce fichier doit être inclus dans votre configuration Apache
# ou ajouté dans le .htaccess de votre domaine
#
# Domaine: https://dev-mani.io/teams/client-api.callitris-distribution.com

<IfModule mod_rewrite.c>
    RewriteEngine On
    
    # Redirection des callbacks Wave vers le gestionnaire PHP
    # Pattern: /callback/wave/{type}
    # Types: success, failure, cancel, notify, return
    
    # Callback de succès Wave
    RewriteRule ^callback/wave/success/?$ wave_callback_handler.php [L,QSA]
    
    # Callback d'échec Wave
    RewriteRule ^callback/wave/failure/?$ wave_callback_handler.php [L,QSA]
    
    # Callback d'annulation Wave
    RewriteRule ^callback/wave/cancel/?$ wave_callback_handler.php [L,QSA]
    
    # Callback de notification Wave (webhook)
    RewriteRule ^callback/wave/notify/?$ wave_callback_handler.php [L,QSA]
    
    # Callback de retour général Wave
    RewriteRule ^callback/wave/return/?$ wave_callback_handler.php [L,QSA]
    
    # Redirection pour les anciens callbacks CinetPay (compatibilité)
    RewriteRule ^cinetpay/return/?$ cinetpay_callback_handler.php [L,QSA]
    RewriteRule ^cinetpay/notify/?$ cinetpay_callback_handler.php [L,QSA]
    RewriteRule ^cinetpay/cancel/?$ cinetpay_callback_handler.php [L,QSA]
</IfModule>

# Configuration des headers pour CORS
<IfModule mod_headers.c>
    # Permettre les requêtes cross-origin pour les callbacks
    Header always set Access-Control-Allow-Origin "*"
    Header always set Access-Control-Allow-Methods "GET, POST, OPTIONS"
    Header always set Access-Control-Allow-Headers "Content-Type, Authorization"
    Header always set Access-Control-Max-Age "3600"
    
    # Headers de sécurité
    Header always set X-Content-Type-Options "nosniff"
    Header always set X-Frame-Options "DENY"
    Header always set X-XSS-Protection "1; mode=block"
    
    # Headers pour les callbacks Wave
    <FilesMatch "wave_callback_handler\.php">
        Header always set Content-Type "application/json"
        Header always set Cache-Control "no-cache, no-store, must-revalidate"
        Header always set Pragma "no-cache"
        Header always set Expires "0"
    </FilesMatch>
</IfModule>

# Configuration PHP pour les callbacks
<IfModule mod_php.c>
    # Augmenter les limites pour les callbacks
    php_value max_execution_time 60
    php_value max_input_time 60
    php_value memory_limit 128M
    php_value post_max_size 10M
    php_value upload_max_filesize 10M
    
    # Activer l'affichage des erreurs en développement
    # À désactiver en production
    php_flag display_errors On
    php_flag log_errors On
    php_value error_log "/var/log/php/callitris_errors.log"
</IfModule>

# Protection des fichiers sensibles
<FilesMatch "\.(log|txt|md)$">
    Order Allow,Deny
    Deny from all
</FilesMatch>

# Protection du dossier logs
<Directory "logs">
    Order Allow,Deny
    Deny from all
</Directory>

# Configuration pour les méthodes HTTP
<IfModule mod_rewrite.c>
    # Permettre les méthodes OPTIONS pour CORS
    RewriteCond %{REQUEST_METHOD} OPTIONS
    RewriteRule ^(.*)$ $1 [R=200,L]
</IfModule>

# Configuration de compression pour optimiser les réponses
<IfModule mod_deflate.c>
    AddOutputFilterByType DEFLATE application/json
    AddOutputFilterByType DEFLATE text/html
    AddOutputFilterByType DEFLATE text/css
    AddOutputFilterByType DEFLATE text/javascript
    AddOutputFilterByType DEFLATE application/javascript
</IfModule>

# Configuration du cache pour les ressources statiques
<IfModule mod_expires.c>
    ExpiresActive On
    
    # Pas de cache pour les callbacks
    <FilesMatch "wave_callback_handler\.php">
        ExpiresDefault "access plus 0 seconds"
    </FilesMatch>
    
    # Cache pour les autres ressources
    ExpiresByType text/css "access plus 1 month"
    ExpiresByType application/javascript "access plus 1 month"
    ExpiresByType image/png "access plus 1 month"
    ExpiresByType image/jpg "access plus 1 month"
    ExpiresByType image/jpeg "access plus 1 month"
    ExpiresByType image/gif "access plus 1 month"
</IfModule>

# Configuration de sécurité supplémentaire
<IfModule mod_rewrite.c>
    # Bloquer les tentatives d'accès aux fichiers système
    RewriteCond %{THE_REQUEST} /\.(env|git|htaccess|htpasswd) [NC]
    RewriteRule .* - [F]
    
    # Bloquer les user agents suspects
    RewriteCond %{HTTP_USER_AGENT} (bot|crawler|spider) [NC]
    RewriteCond %{REQUEST_URI} callback [NC]
    RewriteRule .* - [F]
</IfModule>

# Logging personnalisé pour les callbacks
<IfModule mod_log_config.c>
    LogFormat "%h %l %u %t \"%r\" %>s %O \"%{Referer}i\" \"%{User-Agent}i\" %D" combined_with_time
    
    # Log spécifique pour les callbacks Wave
    <FilesMatch "wave_callback_handler\.php">
        CustomLog /var/log/apache2/callitris_wave_callbacks.log combined_with_time
    </FilesMatch>
</IfModule>
