import 'package:flutter/material.dart';
import 'package:callitris/services/order_service.dart';
import 'package:callitris/services/auth_service.dart';

/// Widget réactif pour afficher les commandes
/// Se met à jour automatiquement quand les données changent
class ReactiveOrdersWidget extends StatefulWidget {
  final Widget Function(BuildContext context, List<Map<String, dynamic>> orders, bool isLoading, String? error) builder;
  final Widget? loadingWidget;
  final Widget? errorWidget;
  final bool showRefreshButton;
  
  const ReactiveOrdersWidget({
    Key? key,
    required this.builder,
    this.loadingWidget,
    this.errorWidget,
    this.showRefreshButton = true,
  }) : super(key: key);

  @override
  State<ReactiveOrdersWidget> createState() => _ReactiveOrdersWidgetState();
}

class _ReactiveOrdersWidgetState extends State<ReactiveOrdersWidget> {
  @override
  void initState() {
    super.initState();
    // Initialiser le service si pas déjà fait
    _initializeIfNeeded();
  }
  
  void _initializeIfNeeded() async {
    if (AuthService.isLoggedInValue && OrderService.orders.isEmpty) {
      await OrderService.initialize();
    }
  }

  @override
  Widget build(BuildContext context) {
    return StreamBuilder<bool>(
      stream: AuthService.isLoggedInStream,
      builder: (context, authSnapshot) {
        if (!authSnapshot.hasData || !authSnapshot.data!) {
          return const Center(
            child: Text('Vous devez être connecté pour voir vos commandes'),
          );
        }
        
        return StreamBuilder<List<Map<String, dynamic>>>(
          stream: OrderService.ordersStream,
          builder: (context, ordersSnapshot) {
            return StreamBuilder<bool>(
              stream: OrderService.isLoadingOrdersStream,
              builder: (context, loadingSnapshot) {
                return StreamBuilder<String?>(
                  stream: OrderService.ordersErrorStream,
                  builder: (context, errorSnapshot) {
                    final orders = ordersSnapshot.data ?? [];
                    final isLoading = loadingSnapshot.data ?? false;
                    final error = errorSnapshot.data;
                    
                    // Afficher le widget d'erreur personnalisé si fourni
                    if (error != null && widget.errorWidget != null) {
                      return Column(
                        children: [
                          widget.errorWidget!,
                          if (widget.showRefreshButton)
                            ElevatedButton(
                              onPressed: () => OrderService.refreshOrders(),
                              child: const Text('Réessayer'),
                            ),
                        ],
                      );
                    }
                    
                    // Afficher le widget de chargement personnalisé si fourni
                    if (isLoading && orders.isEmpty && widget.loadingWidget != null) {
                      return widget.loadingWidget!;
                    }
                    
                    return widget.builder(context, orders, isLoading, error);
                  },
                );
              },
            );
          },
        );
      },
    );
  }
}

/// Widget réactif simple pour afficher la liste des commandes
class ReactiveOrdersList extends StatelessWidget {
  final Widget Function(BuildContext context, Map<String, dynamic> order)? itemBuilder;
  final Widget? emptyWidget;
  final bool showRefreshButton;
  final EdgeInsetsGeometry? padding;
  
  const ReactiveOrdersList({
    Key? key,
    this.itemBuilder,
    this.emptyWidget,
    this.showRefreshButton = true,
    this.padding,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return ReactiveOrdersWidget(
      showRefreshButton: showRefreshButton,
      loadingWidget: const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            CircularProgressIndicator(),
            SizedBox(height: 16),
            Text('Chargement des commandes...'),
          ],
        ),
      ),
      errorWidget: const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.error_outline, size: 64, color: Colors.red),
            SizedBox(height: 16),
            Text('Erreur lors du chargement des commandes'),
          ],
        ),
      ),
      builder: (context, orders, isLoading, error) {
        if (orders.isEmpty && !isLoading) {
          return emptyWidget ?? const Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(Icons.shopping_cart_outlined, size: 64, color: Colors.grey),
                SizedBox(height: 16),
                Text('Aucune commande trouvée'),
                SizedBox(height: 8),
                Text(
                  'Vos commandes apparaîtront ici',
                  style: TextStyle(color: Colors.grey),
                ),
              ],
            ),
          );
        }
        
        return RefreshIndicator(
          onRefresh: () => OrderService.refreshOrders(),
          child: ListView.builder(
            padding: padding ?? const EdgeInsets.all(16),
            itemCount: orders.length + (isLoading ? 1 : 0),
            itemBuilder: (context, index) {
              // Afficher un indicateur de chargement à la fin si en cours de chargement
              if (index == orders.length && isLoading) {
                return const Padding(
                  padding: EdgeInsets.all(16),
                  child: Center(
                    child: CircularProgressIndicator(),
                  ),
                );
              }
              
              final order = orders[index];
              
              // Utiliser le builder personnalisé si fourni
              if (itemBuilder != null) {
                return itemBuilder!(context, order);
              }
              
              // Builder par défaut
              return Card(
                margin: const EdgeInsets.only(bottom: 8),
                child: ListTile(
                  title: Text('Commande #${order['id'] ?? 'N/A'}'),
                  subtitle: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text('Montant: ${order['montant'] ?? 'N/A'} FCFA'),
                      Text('Reste: ${order['reste'] ?? 'N/A'} FCFA'),
                      Text('Date: ${order['date_commande'] ?? 'N/A'}'),
                    ],
                  ),
                  trailing: _buildStatusChip(order),
                  onTap: () {
                    // Ici vous pouvez naviguer vers les détails de la commande
                    // Navigator.push(context, MaterialPageRoute(
                    //   builder: (context) => OrderDetailsScreen(order: order),
                    // ));
                  },
                ),
              );
            },
          ),
        );
      },
    );
  }
  
  Widget _buildStatusChip(Map<String, dynamic> order) {
    final reste = double.tryParse(order['reste']?.toString() ?? '0') ?? 0;
    final montant = double.tryParse(order['montant']?.toString() ?? '0') ?? 0;
    
    Color chipColor;
    String statusText;
    
    if (reste <= 0) {
      chipColor = Colors.green;
      statusText = 'Terminée';
    } else if (reste == montant) {
      chipColor = Colors.orange;
      statusText = 'En attente';
    } else {
      chipColor = Colors.blue;
      statusText = 'En cours';
    }
    
    return Chip(
      label: Text(
        statusText,
        style: const TextStyle(
          color: Colors.white,
          fontSize: 12,
        ),
      ),
      backgroundColor: chipColor,
    );
  }
}

/// Widget réactif pour afficher le nombre de commandes
class ReactiveOrdersCount extends StatelessWidget {
  final TextStyle? textStyle;
  final String prefix;
  final String suffix;
  
  const ReactiveOrdersCount({
    Key? key,
    this.textStyle,
    this.prefix = '',
    this.suffix = ' commandes',
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return StreamBuilder<List<Map<String, dynamic>>>(
      stream: OrderService.ordersStream,
      builder: (context, snapshot) {
        final count = snapshot.data?.length ?? 0;
        return Text(
          '$prefix$count$suffix',
          style: textStyle,
        );
      },
    );
  }
}

/// Widget réactif pour afficher le statut de chargement des commandes
class ReactiveOrdersLoadingIndicator extends StatelessWidget {
  final Widget? child;
  
  const ReactiveOrdersLoadingIndicator({
    Key? key,
    this.child,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return StreamBuilder<bool>(
      stream: OrderService.isLoadingOrdersStream,
      builder: (context, snapshot) {
        final isLoading = snapshot.data ?? false;
        
        if (isLoading) {
          return child ?? const LinearProgressIndicator();
        }
        
        return const SizedBox.shrink();
      },
    );
  }
}
