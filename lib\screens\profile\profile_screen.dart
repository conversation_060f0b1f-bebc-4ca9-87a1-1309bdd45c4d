import 'package:flutter/material.dart';
import 'package:callitris/utils/appTheme.dart';
import 'package:callitris/services/auth_service.dart';
import 'package:callitris/screens/auth/login_screen.dart';
import 'package:callitris/screens/profile/personal_info_screen.dart';
import 'package:callitris/utils/image_utils.dart';
import 'package:callitris/screens/profile/help_support_screen.dart';
import 'package:callitris/screens/profile/about_screen.dart';
import 'package:callitris/screens/profile/security_screen.dart';
import 'package:callitris/screens/profile/notifications_screen.dart';
import 'package:qr_flutter/qr_flutter.dart';
import 'dart:convert';

import '../../utils/navigation_service.dart';
import '../../widgets/navigation_menu_button.dart';

class ProfileScreen extends StatefulWidget {
  const ProfileScreen({super.key});

  @override
  State<ProfileScreen> createState() => _ProfileScreenState();
}

class _ProfileScreenState extends State<ProfileScreen>
    with SingleTickerProviderStateMixin {
  Map<String, dynamic> _userData = {};
  bool _isLoading = true;
  late AnimationController _animationController;
  late Animation<double> _fadeAnimation;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 800),
    );
    _fadeAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _animationController, curve: Curves.easeIn),
    );
    _loadUserData();
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  Future<void> _loadUserData() async {
    final userData = await AuthService.getUserData();

    if (userData != null) {
      setState(() {
        _userData = userData;
        _isLoading = false;
      });
      _animationController.forward();
    } else {
      // Fallback pour les anciennes installations qui n'ont pas userData
      final phoneNumber = await AuthService.getLoggedInPhoneNumber();
      setState(() {
        _userData = {'telephone_client': phoneNumber};
        _isLoading = false;
      });
      _animationController.forward();
    }
  }

  // Méthode pour formater les valeurs nulles ou vides
  String _formatValue(dynamic value) {
    if (value == null ||
        value.toString().trim().isEmpty ||
        value.toString().trim() == ' ') {
      return 'Non renseigné';
    }
    return value.toString();
  }

  Future<void> _logout() async {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder:
          (context) => AlertDialog(
            title: Text(
              'Déconnexion',
              style: TextStyle(
                color: AppTheme.color.primaryColor,
                fontWeight: FontWeight.bold,
              ),
            ),
            content: const Text('Êtes-vous sûr de vouloir vous déconnecter ?'),
            actions: [
              TextButton(
                onPressed: () {
                  Navigator.pop(context);
                },
                child: Text(
                  'Annuler',
                  style: TextStyle(color: AppTheme.color.brunGris),
                ),
              ),
              ElevatedButton(
                onPressed: () async {
                  // Fermer la boîte de dialogue
                  Navigator.pop(context);

                  // Afficher un indicateur de chargement
                  showDialog(
                    context: context,
                    barrierDismissible: false,
                    builder: (BuildContext context) {
                      return Dialog(
                        backgroundColor: Colors.white,
                        child: Padding(
                          padding: const EdgeInsets.symmetric(vertical: 20),
                          child: Column(
                            mainAxisSize: MainAxisSize.min,
                            children: [
                              const CircularProgressIndicator(),
                              const SizedBox(height: 16),
                              Text(
                                'Déconnexion en cours...',
                                style: TextStyle(
                                  color: AppTheme.color.textColor,
                                  fontSize: 16,
                                ),
                              ),
                            ],
                          ),
                        ),
                      );
                    },
                  );

                  try {
                    // Effectuer la déconnexion
                    await AuthService.logout();

                    if (mounted) {
                      // Fermer le dialogue de chargement et rediriger vers l'écran de connexion
                      Navigator.of(context).pop();

                      // Utiliser le service de navigation pour une redirection plus fiable
                      routeAndRemoveUntil(context, const LoginScreen());
                    }
                  } catch (e) {
                    if (mounted) {
                      Navigator.of(
                        context,
                      ).pop(); // Fermer le dialogue de chargement

                      ScaffoldMessenger.of(context).showSnackBar(
                        SnackBar(
                          content: const Text(
                            'Erreur lors de la déconnexion. Veuillez réessayer.',
                          ),
                          backgroundColor: AppTheme.color.redColor,
                        ),
                      );
                    }
                  }
                },
                style: ElevatedButton.styleFrom(
                  backgroundColor: AppTheme.color.redColor,
                  foregroundColor: Colors.white,
                ),
                child: const Text('Déconnecter'),
              ),
            ],
          ),
    );
  }

  // Méthode pour obtenir les initiales du nom
  String _getInitials() {
    String prenom = _userData['prenom_client']?.toString() ?? '';
    String nom = _userData['nom_client']?.toString() ?? '';

    if (prenom.isEmpty && nom.isEmpty) {
      return 'U';
    }

    String initials = '';
    if (prenom.isNotEmpty) {
      initials += prenom[0];
    }
    if (nom.isNotEmpty) {
      initials += nom[0];
    }

    return initials.toUpperCase();
  }

  // Méthode pour formater le nom complet
  String _formatUserName() {
    String prenom = _userData['prenom_client']?.toString() ?? '';
    String nom = _userData['nom_client']?.toString() ?? '';

    if (prenom.isEmpty && nom.isEmpty) {
      return 'Utilisateur';
    }

    return '$prenom $nom'.trim();
  }

  // Méthode pour formater la date
  String _formatDate(dynamic dateStr) {
    if (dateStr == null || dateStr.toString().isEmpty) {
      return DateTime.now().year.toString();
    }

    try {
      final parts = dateStr.toString().split('-');
      if (parts.length == 3) {
        return parts[0]; // Année
      }
    } catch (e) {
      print('Erreur lors du formatage de la date: $e');
    }

    return DateTime.now().year.toString();
  }

  void _showQrCodeDialog() {
    final String codeClient = _userData['code_client']?.toString() ?? '';

    if (codeClient.isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: const Text('Code client non disponible'),
          backgroundColor: AppTheme.color.redColor,
        ),
      );
      return;
    }

    // Créer un JSON avec les informations essentielles
    final Map<String, dynamic> qrData = {
      'code_client': codeClient,
      'telephone': _userData['telephone_client']?.toString() ?? '',
      'nom': _userData['nom_client']?.toString() ?? '',
      'prenom': _userData['prenom_client']?.toString() ?? '',
      'personnel_id': _userData['personnel_id']?.toString() ?? '',
      'type': 'callitris_client',
    };

    // Convertir en JSON
    final String qrDataString = jsonEncode(qrData);

    showDialog(
      context: context,
      barrierDismissible: false,
      builder:
          (context) => Dialog(
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(20),
            ),
            child: Padding(
              padding: const EdgeInsets.all(24),
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Text(
                    'Mon QR Code',
                    style: TextStyle(
                      fontSize: 20,
                      fontWeight: FontWeight.bold,
                      color: AppTheme.color.primaryColor,
                    ),
                  ),
                  const SizedBox(height: 8),
                  Text(
                    'Scannez ce code pour partager vos informations',
                    textAlign: TextAlign.center,
                    style: TextStyle(
                      fontSize: 14,
                      color: AppTheme.color.brunGris,
                    ),
                  ),
                  const SizedBox(height: 24),
                  Container(
                    padding: const EdgeInsets.all(16),
                    decoration: BoxDecoration(
                      color: Colors.white,
                      borderRadius: BorderRadius.circular(16),
                      boxShadow: [
                        BoxShadow(
                          color: Colors.black.withOpacity(0.05),
                          blurRadius: 10,
                          offset: const Offset(0, 5),
                        ),
                      ],
                    ),
                    child: QrImageView(
                      data: qrDataString,
                      version: QrVersions.auto,
                      size: 200,
                      backgroundColor: Colors.white,
                      foregroundColor: AppTheme.color.primaryColor,
                      embeddedImage: const AssetImage(
                        'assets/images/logo_small.png',
                      ),
                      embeddedImageStyle: QrEmbeddedImageStyle(
                        size: const Size(40, 40),
                      ),
                    ),
                  ),
                  const SizedBox(height: 16),
                  Text(
                    'Code client: $codeClient',
                    style: const TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                  const SizedBox(height: 8),
                  Text(
                    _formatUserName(),
                    style: TextStyle(
                      fontSize: 14,
                      color: AppTheme.color.brunGris,
                    ),
                  ),
                  const SizedBox(height: 24),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      OutlinedButton(
                        onPressed: () {
                          Navigator.pop(context);
                        },
                        style: OutlinedButton.styleFrom(
                          foregroundColor: AppTheme.color.brunGris,
                          side: BorderSide(color: AppTheme.color.brunGris),
                          padding: const EdgeInsets.symmetric(
                            horizontal: 24,
                            vertical: 12,
                          ),
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(12),
                          ),
                        ),
                        child: const Text('Fermer'),
                      ),
                      const SizedBox(width: 16),
                      ElevatedButton.icon(
                        onPressed: () {
                          // Fonctionnalité de partage à implémenter ultérieurement
                          ScaffoldMessenger.of(context).showSnackBar(
                            const SnackBar(
                              content: Text(
                                'Fonctionnalité de partage à venir',
                              ),
                            ),
                          );
                        },
                        icon: const Icon(Icons.share),
                        label: const Text('Partager'),
                        style: ElevatedButton.styleFrom(
                          backgroundColor: AppTheme.color.primaryColor,
                          foregroundColor: Colors.white,
                          padding: const EdgeInsets.symmetric(
                            horizontal: 24,
                            vertical: 12,
                          ),
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(12),
                          ),
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.grey[50],
      body:
          _isLoading
              ? const Center(child: CircularProgressIndicator())
              : FadeTransition(
                opacity: _fadeAnimation,
                child: CustomScrollView(
                  slivers: [
                    // AppBar avec gradient
                    SliverAppBar(
                      expandedHeight: 260,
                      floating: false,
                      pinned: true,
                      backgroundColor: AppTheme.color.primaryColor,
                      elevation: 0,
                      flexibleSpace: FlexibleSpaceBar(
                        background: Container(
                          decoration: BoxDecoration(
                            gradient: LinearGradient(
                              begin: Alignment.topCenter,
                              end: Alignment.bottomCenter,
                              colors: [
                                AppTheme.color.primaryColor,
                                AppTheme.color.primaryColor.withValues(
                                  alpha: 0.8,
                                ),
                              ],
                            ),
                          ),
                          child: SafeArea(
                            child: Column(
                              mainAxisAlignment: MainAxisAlignment.center,
                              mainAxisSize: MainAxisSize.min,
                              children: [
                                const SizedBox(height: 20),
                                // Avatar avec effet de bordure
                                Container(
                                  padding: const EdgeInsets.all(3),
                                  decoration: BoxDecoration(
                                    shape: BoxShape.circle,
                                    border: Border.all(
                                      color: Colors.white,
                                      width: 2,
                                    ),
                                    boxShadow: [
                                      BoxShadow(
                                        color: Colors.black.withValues(
                                          alpha: 0.2,
                                        ),
                                        blurRadius: 10,
                                        offset: const Offset(0, 3),
                                      ),
                                    ],
                                  ),
                                  child:
                                      _userData['photo'] != null &&
                                              _userData['photo']
                                                  .toString()
                                                  .isNotEmpty
                                          ? CircleAvatar(
                                            radius: 45,
                                            backgroundColor: Colors.white,
                                            child: ClipRRect(
                                              borderRadius:
                                                  BorderRadius.circular(45),
                                              child:
                                                  ImageUtils.networkImageWithErrorHandling(
                                                    imageUrl:
                                                        _userData['photo']
                                                            .toString(),
                                                    width: 90,
                                                    height: 90,
                                                    fit: BoxFit.cover,
                                                    placeholderColor:
                                                        Colors.white,
                                                  ),
                                            ),
                                          )
                                          : CircleAvatar(
                                            radius: 45,
                                            backgroundColor: Colors.white,
                                            child: Image.asset(
                                              'assets/images/logo.png',
                                              width: 90,
                                              height: 90,
                                              fit: BoxFit.cover,
                                            ),
                                          ),
                                ),
                                const SizedBox(height: 12),
                                // Nom utilisateur
                                Text(
                                  _formatUserName(),
                                  style: const TextStyle(
                                    fontSize: 22,
                                    fontWeight: FontWeight.bold,
                                    color: Colors.white,
                                  ),
                                  textAlign: TextAlign.center,
                                ),
                                const SizedBox(height: 6),
                                // Téléphone
                                Row(
                                  mainAxisAlignment: MainAxisAlignment.center,
                                  children: [
                                    const Icon(
                                      Icons.phone_android,
                                      color: Colors.white,
                                      size: 14,
                                    ),
                                    const SizedBox(width: 6),
                                    Text(
                                      _userData['telephone_client'] ??
                                          'Numéro non disponible',
                                      style: const TextStyle(
                                        fontSize: 15,
                                        color: Colors.white,
                                      ),
                                    ),
                                  ],
                                ),
                                const SizedBox(height: 8),
                                // Code client avec badge
                                Container(
                                  padding: const EdgeInsets.symmetric(
                                    horizontal: 12,
                                    vertical: 6,
                                  ),
                                  decoration: BoxDecoration(
                                    color: Colors.white.withValues(alpha: 0.2),
                                    borderRadius: BorderRadius.circular(16),
                                  ),
                                  child: Row(
                                    mainAxisSize: MainAxisSize.min,
                                    children: [
                                      const Icon(
                                        Icons.verified_user,
                                        color: Colors.white,
                                        size: 14,
                                      ),
                                      const SizedBox(width: 6),
                                      Text(
                                        'Code: ${_userData['code_client'] ?? 'N/A'}',
                                        style: const TextStyle(
                                          fontSize: 13,
                                          fontWeight: FontWeight.w600,
                                          color: Colors.white,
                                        ),
                                      ),
                                    ],
                                  ),
                                ),
                                const SizedBox(height: 10),
                              ],
                            ),
                          ),
                        ),
                      ),
                      actions: [
                        NavigationMenuButton(iconColor: Colors.white),
                        IconButton(
                          icon: const Icon(Icons.qr_code, color: Colors.white),
                          onPressed: _showQrCodeDialog,
                        ),
                      ],
                    ),

                    // Contenu principal
                    SliverToBoxAdapter(
                      child: Padding(
                        padding: const EdgeInsets.all(16),
                        child: Column(
                          children: [
                            // Statistiques rapides
                            _buildStatsCard(),
                            const SizedBox(height: 24),

                            // Section Compte
                            _buildSectionTitle('Mon Compte'),
                            const SizedBox(height: 12),
                            _buildModernProfileOption(
                              icon: Icons.person_outline,
                              title: 'Informations personnelles',
                              subtitle: 'Gérer vos données personnelles',
                              color: Colors.blue,
                              onTap: () {
                                Navigator.push(
                                  context,
                                  MaterialPageRoute(
                                    builder:
                                        (context) => const PersonalInfoScreen(),
                                  ),
                                );
                              },
                            ),
                            _buildModernProfileOption(
                              icon: Icons.qr_code_scanner,
                              title: 'Mon QR Code',
                              subtitle: 'Partager vos informations',
                              color: Colors.green,
                              onTap: _showQrCodeDialog,
                            ),

                            const SizedBox(height: 24),

                            // Section Préférences
                            _buildSectionTitle('Préférences'),
                            const SizedBox(height: 12),
                            _buildModernProfileOption(
                              icon: Icons.support_agent,
                              title: 'Service Client',
                              subtitle:
                                  'Appelez pour avoir plus d\'informations',
                              color: Colors.orange,
                              onTap: () {
                                Navigator.push(
                                  context,
                                  MaterialPageRoute(
                                    builder:
                                        (context) =>
                                            const NotificationsScreen(),
                                  ),
                                );
                              },
                            ),
                            _buildModernProfileOption(
                              icon: Icons.security_outlined,
                              title: 'Sécurité',
                              subtitle: 'Paramètres de sécurité',
                              color: Colors.red,
                              onTap: () {
                                Navigator.push(
                                  context,
                                  MaterialPageRoute(
                                    builder:
                                        (context) => const SecurityScreen(),
                                  ),
                                );
                              },
                            ),

                            const SizedBox(height: 24),

                            // Section Support
                            _buildSectionTitle('Support'),
                            const SizedBox(height: 12),
                            _buildModernProfileOption(
                              icon: Icons.help_outline,
                              title: 'Aide et support',
                              subtitle: 'Besoin d\'aide ?',
                              color: Colors.purple,
                              onTap: () {
                                Navigator.push(
                                  context,
                                  MaterialPageRoute(
                                    builder:
                                        (context) => const HelpSupportScreen(),
                                  ),
                                );
                              },
                            ),
                            _buildModernProfileOption(
                              icon: Icons.info_outline,
                              title: 'À propos',
                              subtitle: 'Informations sur l\'application',
                              color: Colors.teal,
                              onTap: () {
                                Navigator.push(
                                  context,
                                  MaterialPageRoute(
                                    builder: (context) => const AboutScreen(),
                                  ),
                                );
                              },
                            ),

                            const SizedBox(height: 32),

                            // Bouton de déconnexion moderne
                            _buildLogoutButton(),

                            const SizedBox(height: 32),
                          ],
                        ),
                      ),
                    ),
                  ],
                ),
              ),
    );
  }

  Widget _buildProfileHeader() {
    return Container(
      margin: const EdgeInsets.all(16),
      padding: const EdgeInsets.all(24),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [
            AppTheme.color.primaryColor,
            Color.lerp(AppTheme.color.primaryColor, Colors.black, 0.3) ??
                AppTheme.color.primaryColor,
          ],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        borderRadius: BorderRadius.circular(20),
        boxShadow: [
          BoxShadow(
            color: AppTheme.color.primaryColor.withOpacity(0.3),
            blurRadius: 15,
            offset: const Offset(0, 5),
          ),
        ],
      ),
      child: Column(
        children: [
          Row(
            children: [
              _userData['photo'] != null &&
                      _userData['photo'].toString().isNotEmpty
                  ? Container(
                    padding: const EdgeInsets.all(3),
                    decoration: const BoxDecoration(
                      color: Colors.white,
                      shape: BoxShape.circle,
                    ),
                    child: ClipRRect(
                      borderRadius: BorderRadius.circular(40),
                      child: ImageUtils.networkImageWithErrorHandling(
                        imageUrl: _userData['photo'].toString(),
                        width: 80,
                        height: 80,
                        fit: BoxFit.cover,
                        placeholderColor: Colors.white,
                      ),
                    ),
                  )
                  : Container(
                    padding: const EdgeInsets.all(3),
                    decoration: const BoxDecoration(
                      color: Colors.white,
                      shape: BoxShape.circle,
                    ),
                    child: CircleAvatar(
                      radius: 40,
                      backgroundColor: Colors.white,
                      child: Text(
                        _getInitials(),
                        style: TextStyle(
                          fontSize: 28,
                          fontWeight: FontWeight.bold,
                          color: AppTheme.color.primaryColor,
                        ),
                      ),
                    ),
                  ),
              const SizedBox(width: 20),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      '${_formatValue(_userData['prenom_client'])} ${_formatValue(_userData['nom_client'])}',
                      style: const TextStyle(
                        fontSize: 22,
                        fontWeight: FontWeight.bold,
                        color: Colors.white,
                      ),
                    ),
                    const SizedBox(height: 8),
                    Row(
                      children: [
                        Icon(
                          Icons.phone_android,
                          color: Colors.white.withOpacity(0.8),
                          size: 16,
                        ),
                        const SizedBox(width: 8),
                        Text(
                          _formatValue(_userData['telephone_client']),
                          style: TextStyle(
                            fontSize: 16,
                            color: Colors.white.withOpacity(0.9),
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 4),
                    Row(
                      children: [
                        Icon(
                          Icons.work_outline,
                          color: Colors.white.withOpacity(0.8),
                          size: 16,
                        ),
                        const SizedBox(width: 8),
                        Text(
                          _formatValue(_userData['fonction_client']),
                          style: TextStyle(
                            fontSize: 14,
                            color: Colors.white.withOpacity(0.9),
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            ],
          ),
          const SizedBox(height: 20),
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 10),
            decoration: BoxDecoration(
              color: Colors.white.withOpacity(0.2),
              borderRadius: BorderRadius.circular(10),
            ),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                const Icon(Icons.verified_user, color: Colors.white, size: 18),
                const SizedBox(width: 8),
                Text(
                  'Code client: ${_formatValue(_userData['code_client'])}',
                  style: const TextStyle(
                    fontSize: 14,
                    fontWeight: FontWeight.w500,
                    color: Colors.white,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildStatsCard() {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Row(
        children: [
          Expanded(
            child: Column(
              children: [
                Text(
                  'Membre depuis',
                  style: TextStyle(
                    fontSize: 12,
                    color: AppTheme.color.brunGris,
                    fontWeight: FontWeight.w500,
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  _formatDate(_userData['date_ajout']),
                  style: TextStyle(
                    fontSize: 20,
                    fontWeight: FontWeight.bold,
                    color: AppTheme.color.primaryColor,
                  ),
                ),
              ],
            ),
          ),
          Container(width: 1, height: 40, color: Colors.grey[300]),
          Expanded(
            child: Column(
              children: [
                Text(
                  'Statut',
                  style: TextStyle(
                    fontSize: 12,
                    color: AppTheme.color.brunGris,
                    fontWeight: FontWeight.w500,
                  ),
                ),
                const SizedBox(height: 4),
                Container(
                  padding: const EdgeInsets.symmetric(
                    horizontal: 12,
                    vertical: 4,
                  ),
                  decoration: BoxDecoration(
                    color: AppTheme.color.greenColor.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Text(
                    'Actif',
                    style: TextStyle(
                      fontSize: 12,
                      fontWeight: FontWeight.bold,
                      color: AppTheme.color.greenColor,
                    ),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSectionTitle(String title) {
    return Align(
      alignment: Alignment.centerLeft,
      child: Text(
        title,
        style: const TextStyle(
          fontSize: 18,
          fontWeight: FontWeight.bold,
          color: Colors.black87,
        ),
      ),
    );
  }

  Widget _buildModernProfileOption({
    required IconData icon,
    required String title,
    required String subtitle,
    required Color color,
    required VoidCallback onTap,
  }) {
    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.04),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          borderRadius: BorderRadius.circular(16),
          onTap: onTap,
          child: Padding(
            padding: const EdgeInsets.all(16),
            child: Row(
              children: [
                Container(
                  padding: const EdgeInsets.all(12),
                  decoration: BoxDecoration(
                    color: color.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Icon(icon, color: color, size: 24),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        title,
                        style: const TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.w600,
                          color: Colors.black87,
                        ),
                      ),
                      const SizedBox(height: 2),
                      Text(
                        subtitle,
                        style: TextStyle(
                          fontSize: 13,
                          color: AppTheme.color.brunGris,
                        ),
                      ),
                    ],
                  ),
                ),
                Icon(
                  Icons.arrow_forward_ios,
                  color: AppTheme.color.brunGris,
                  size: 16,
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildLogoutButton() {
    return Container(
      width: double.infinity,
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(16),
        gradient: LinearGradient(
          colors: [
            AppTheme.color.redColor.withValues(alpha: 0.1),
            AppTheme.color.redColor.withValues(alpha: 0.05),
          ],
        ),
        border: Border.all(
          color: AppTheme.color.redColor.withValues(alpha: 0.3),
          width: 1,
        ),
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          borderRadius: BorderRadius.circular(16),
          onTap: _logout,
          child: Padding(
            padding: const EdgeInsets.symmetric(vertical: 16),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(Icons.logout, color: AppTheme.color.redColor, size: 20),
                const SizedBox(width: 12),
                Text(
                  'Déconnexion',
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.w600,
                    color: AppTheme.color.redColor,
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
