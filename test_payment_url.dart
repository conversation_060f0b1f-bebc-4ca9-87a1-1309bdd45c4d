import 'dart:convert';
import 'dart:io';

/// Script de test pour vérifier l'accessibilité de l'URL de paiement CinetPay
void main() async {
  print('🧪 Test d\'accessibilité de l\'URL de paiement CinetPay...\n');
  
  // D'abord, obtenir une URL de paiement du backend
  const String backendUrl = 'https://dev-mani.io/client-api.callitris-distribution.com/paiement/init_cinetpay.php';
  
  final testData = {
    'amount': 1000.0,
    'phone': '+22501020304',
  };
  
  print('📤 1. Obtention de l\'URL de paiement...');
  
  try {
    final client = HttpClient();
    
    // Étape 1: Obtenir l'URL de paiement
    final request = await client.postUrl(Uri.parse(backendUrl));
    request.headers.set('Content-Type', 'application/json');
    request.headers.set('Accept', 'application/json');
    request.add(utf8.encode(jsonEncode(testData)));
    
    final response = await request.close();
    final responseBody = await response.transform(utf8.decoder).join();
    
    if (response.statusCode == 200) {
      final jsonResponse = jsonDecode(responseBody);
      
      if (jsonResponse['success'] == true && jsonResponse['payment_url'] != null) {
        final paymentUrl = jsonResponse['payment_url'] as String;
        print('✅ URL de paiement obtenue: $paymentUrl\n');
        
        // Étape 2: Tester l'accessibilité de l'URL de paiement
        print('📤 2. Test d\'accessibilité de l\'URL de paiement...');
        
        final paymentRequest = await client.getUrl(Uri.parse(paymentUrl));
        paymentRequest.headers.set('User-Agent', 'Mozilla/5.0 (Linux; Android 10; SM-G973F) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.120 Mobile Safari/537.36');
        paymentRequest.headers.set('Accept', 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8');
        paymentRequest.headers.set('Accept-Language', 'fr-FR,fr;q=0.9,en;q=0.8');
        
        final paymentResponse = await paymentRequest.close();
        final paymentBody = await paymentResponse.transform(utf8.decoder).join();
        
        print('📥 Status Code: ${paymentResponse.statusCode}');
        print('📥 Content-Type: ${paymentResponse.headers.contentType}');
        print('📥 Content-Length: ${paymentBody.length} caractères');
        
        if (paymentResponse.statusCode == 200) {
          print('✅ URL de paiement accessible !');
          
          // Vérifier si c'est du HTML valide
          if (paymentBody.toLowerCase().contains('<html') && 
              paymentBody.toLowerCase().contains('</html>')) {
            print('✅ Contenu HTML valide détecté');
            
            // Chercher des éléments CinetPay
            if (paymentBody.toLowerCase().contains('cinetpay') ||
                paymentBody.toLowerCase().contains('checkout') ||
                paymentBody.toLowerCase().contains('payment')) {
              print('✅ Éléments de paiement CinetPay détectés');
            } else {
              print('⚠️  Aucun élément CinetPay évident trouvé');
            }
          } else {
            print('⚠️  Le contenu ne semble pas être du HTML valide');
            print('📄 Début du contenu:');
            print(paymentBody.substring(0, paymentBody.length > 500 ? 500 : paymentBody.length));
          }
        } else {
          print('❌ URL de paiement non accessible: ${paymentResponse.statusCode}');
          print('📄 Réponse: ${paymentBody.substring(0, paymentBody.length > 200 ? 200 : paymentBody.length)}');
        }
        
      } else {
        print('❌ Échec de l\'obtention de l\'URL de paiement');
        print('📄 Réponse: $responseBody');
      }
    } else {
      print('❌ Erreur backend: ${response.statusCode}');
      print('📄 Réponse: $responseBody');
    }
    
    client.close();
    
  } catch (e) {
    print('❌ Erreur: $e');
  }
  
  print('\n🏁 Test terminé.');
}
