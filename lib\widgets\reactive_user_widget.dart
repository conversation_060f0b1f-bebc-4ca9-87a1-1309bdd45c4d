import 'package:flutter/material.dart';
import 'package:callitris/services/auth_service.dart';
import 'package:callitris/services/app_state_service.dart';

/// Widget réactif pour afficher les informations utilisateur
/// Se met à jour automatiquement quand les données changent
class ReactiveUserWidget extends StatefulWidget {
  final Widget Function(BuildContext context, Map<String, dynamic>? userData, bool isLoggedIn) builder;
  final Widget? notLoggedInWidget;
  
  const ReactiveUserWidget({
    Key? key,
    required this.builder,
    this.notLoggedInWidget,
  }) : super(key: key);

  @override
  State<ReactiveUserWidget> createState() => _ReactiveUserWidgetState();
}

class _ReactiveUserWidgetState extends State<ReactiveUserWidget> {
  @override
  void initState() {
    super.initState();
    // Initialiser le service d'authentification si pas déjà fait
    _initializeIfNeeded();
  }
  
  void _initializeIfNeeded() async {
    if (!AuthService.isLoggedInValue) {
      await AuthService.initialize();
    }
  }

  @override
  Widget build(BuildContext context) {
    return StreamBuilder<bool>(
      stream: AuthService.isLoggedInStream,
      builder: (context, isLoggedInSnapshot) {
        final isLoggedIn = isLoggedInSnapshot.data ?? false;
        
        if (!isLoggedIn) {
          return widget.notLoggedInWidget ?? const Center(
            child: Text('Utilisateur non connecté'),
          );
        }
        
        return StreamBuilder<Map<String, dynamic>?>(
          stream: AuthService.userDataStream,
          builder: (context, userDataSnapshot) {
            final userData = userDataSnapshot.data;
            return widget.builder(context, userData, isLoggedIn);
          },
        );
      },
    );
  }
}

/// Widget réactif pour afficher le nom de l'utilisateur
class ReactiveUserName extends StatelessWidget {
  final TextStyle? textStyle;
  final String defaultText;
  final String prefix;
  final String suffix;
  
  const ReactiveUserName({
    Key? key,
    this.textStyle,
    this.defaultText = 'Utilisateur',
    this.prefix = '',
    this.suffix = '',
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return ReactiveUserWidget(
      builder: (context, userData, isLoggedIn) {
        String displayName = defaultText;
        
        if (userData != null) {
          final firstName = userData['prenom_client'] ?? userData['prenom'] ?? '';
          final lastName = userData['nom_client'] ?? userData['nom'] ?? '';
          
          if (firstName.isNotEmpty || lastName.isNotEmpty) {
            displayName = '$firstName $lastName'.trim();
          } else if (userData['pseudo_client'] != null) {
            displayName = userData['pseudo_client'];
          }
        }
        
        return Text(
          '$prefix$displayName$suffix',
          style: textStyle,
        );
      },
    );
  }
}

/// Widget réactif pour afficher l'avatar de l'utilisateur
class ReactiveUserAvatar extends StatelessWidget {
  final double radius;
  final Color? backgroundColor;
  final Widget? defaultChild;
  
  const ReactiveUserAvatar({
    Key? key,
    this.radius = 20,
    this.backgroundColor,
    this.defaultChild,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return ReactiveUserWidget(
      builder: (context, userData, isLoggedIn) {
        // Essayer d'afficher la photo de profil si disponible
        final photoUrl = userData?['photo_profil'] ?? userData?['photo'];
        
        if (photoUrl != null && photoUrl.isNotEmpty) {
          return CircleAvatar(
            radius: radius,
            backgroundColor: backgroundColor,
            backgroundImage: NetworkImage(photoUrl),
            onBackgroundImageError: (exception, stackTrace) {
              // En cas d'erreur de chargement, afficher l'avatar par défaut
            },
          );
        }
        
        // Avatar par défaut avec initiales
        String initials = '';
        if (userData != null) {
          final firstName = userData['prenom_client'] ?? userData['prenom'] ?? '';
          final lastName = userData['nom_client'] ?? userData['nom'] ?? '';
          
          if (firstName.isNotEmpty) {
            initials += firstName[0].toUpperCase();
          }
          if (lastName.isNotEmpty) {
            initials += lastName[0].toUpperCase();
          }
          
          if (initials.isEmpty && userData['pseudo_client'] != null) {
            final pseudo = userData['pseudo_client'].toString();
            if (pseudo.isNotEmpty) {
              initials = pseudo[0].toUpperCase();
            }
          }
        }
        
        return CircleAvatar(
          radius: radius,
          backgroundColor: backgroundColor ?? Theme.of(context).primaryColor,
          child: initials.isNotEmpty 
            ? Text(
                initials,
                style: TextStyle(
                  color: Colors.white,
                  fontSize: radius * 0.6,
                  fontWeight: FontWeight.bold,
                ),
              )
            : defaultChild ?? Icon(
                Icons.person,
                size: radius * 1.2,
                color: Colors.white,
              ),
        );
      },
    );
  }
}

/// Widget réactif pour afficher le solde de l'utilisateur
class ReactiveUserBalance extends StatelessWidget {
  final TextStyle? textStyle;
  final String prefix;
  final String suffix;
  final bool showCurrency;
  
  const ReactiveUserBalance({
    Key? key,
    this.textStyle,
    this.prefix = '',
    this.suffix = '',
    this.showCurrency = true,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return ReactiveUserWidget(
      builder: (context, userData, isLoggedIn) {
        final balance = userData?['solde'] ?? userData?['balance'] ?? '0';
        final displayBalance = showCurrency ? '$balance FCFA' : balance.toString();
        
        return Text(
          '$prefix$displayBalance$suffix',
          style: textStyle,
        );
      },
    );
  }
}

/// Widget réactif pour afficher le statut de connexion
class ReactiveConnectionStatus extends StatelessWidget {
  final Widget Function(bool isLoggedIn, bool isLoading)? builder;
  final Widget? loggedInWidget;
  final Widget? loggedOutWidget;
  final Widget? loadingWidget;
  
  const ReactiveConnectionStatus({
    Key? key,
    this.builder,
    this.loggedInWidget,
    this.loggedOutWidget,
    this.loadingWidget,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return StreamBuilder<bool>(
      stream: AuthService.isLoggedInStream,
      builder: (context, isLoggedInSnapshot) {
        return StreamBuilder<bool>(
          stream: AppStateService.instance.isLoadingStream,
          builder: (context, isLoadingSnapshot) {
            final isLoggedIn = isLoggedInSnapshot.data ?? false;
            final isLoading = isLoadingSnapshot.data ?? false;
            
            if (builder != null) {
              return builder!(isLoggedIn, isLoading);
            }
            
            if (isLoading) {
              return loadingWidget ?? const CircularProgressIndicator();
            }
            
            if (isLoggedIn) {
              return loggedInWidget ?? const Icon(Icons.check_circle, color: Colors.green);
            } else {
              return loggedOutWidget ?? const Icon(Icons.error, color: Colors.red);
            }
          },
        );
      },
    );
  }
}

/// Widget réactif pour afficher les informations de profil complètes
class ReactiveUserProfile extends StatelessWidget {
  final EdgeInsetsGeometry? padding;
  final bool showRefreshButton;
  
  const ReactiveUserProfile({
    Key? key,
    this.padding,
    this.showRefreshButton = true,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return ReactiveUserWidget(
      builder: (context, userData, isLoggedIn) {
        return Padding(
          padding: padding ?? const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // En-tête avec avatar et nom
              Row(
                children: [
                  const ReactiveUserAvatar(radius: 30),
                  const SizedBox(width: 16),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        ReactiveUserName(
                          textStyle: Theme.of(context).textTheme.headlineSmall,
                        ),
                        if (userData?['telephone'] != null)
                          Text(
                            userData!['telephone'],
                            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                              color: Colors.grey[600],
                            ),
                          ),
                      ],
                    ),
                  ),
                  if (showRefreshButton)
                    IconButton(
                      onPressed: () => AuthService.refreshUserData(),
                      icon: const Icon(Icons.refresh),
                      tooltip: 'Actualiser le profil',
                    ),
                ],
              ),
              
              const SizedBox(height: 24),
              
              // Informations détaillées
              if (userData != null) ...[
                _buildInfoRow('Code client', userData['code_client']),
                _buildInfoRow('Email', userData['email']),
                _buildInfoRow('Localité', userData['localite']),
                _buildInfoRow('Zone', userData['zone']),
                const SizedBox(height: 16),
                
                // Solde
                Card(
                  child: Padding(
                    padding: const EdgeInsets.all(16),
                    child: Row(
                      children: [
                        const Icon(Icons.account_balance_wallet, color: Colors.green),
                        const SizedBox(width: 12),
                        const Text(
                          'Solde: ',
                          style: TextStyle(fontWeight: FontWeight.bold),
                        ),
                        ReactiveUserBalance(
                          textStyle: const TextStyle(
                            fontSize: 18,
                            fontWeight: FontWeight.bold,
                            color: Colors.green,
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              ],
            ],
          ),
        );
      },
    );
  }
  
  Widget _buildInfoRow(String label, dynamic value) {
    if (value == null || value.toString().isEmpty) {
      return const SizedBox.shrink();
    }
    
    return Padding(
      padding: const EdgeInsets.only(bottom: 8),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 100,
            child: Text(
              '$label:',
              style: const TextStyle(
                fontWeight: FontWeight.w500,
                color: Colors.grey,
              ),
            ),
          ),
          Expanded(
            child: Text(value.toString()),
          ),
        ],
      ),
    );
  }
}
