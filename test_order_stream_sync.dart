/// Test pour vérifier la synchronisation des streams de commandes
/// Ce fichier peut être supprimé après les tests

import 'package:flutter/material.dart';
import 'package:callitris/services/order_service.dart';

void main() {
  runApp(const TestOrderStreamApp());
}

class TestOrderStreamApp extends StatelessWidget {
  const TestOrderStreamApp({super.key});

  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      title: 'Test Order Stream Sync',
      home: const TestOrderStreamScreen(),
    );
  }
}

class TestOrderStreamScreen extends StatefulWidget {
  const TestOrderStreamScreen({super.key});

  @override
  State<TestOrderStreamScreen> createState() => _TestOrderStreamScreenState();
}

class _TestOrderStreamScreenState extends State<TestOrderStreamScreen> {
  List<Map<String, dynamic>> _orders = [];
  bool _isLoading = false;
  String? _error;

  @override
  void initState() {
    super.initState();
    _setupStreams();
    _loadInitialData();
  }

  void _setupStreams() {
    // Écouter les changements de commandes
    OrderService.ordersStream.listen((orders) {
      if (mounted) {
        setState(() {
          _orders = orders;
        });
        print('📦 Stream mis à jour: ${orders.length} commandes');
      }
    });

    // Écouter les changements d'état de chargement
    OrderService.isLoadingOrdersStream.listen((isLoading) {
      if (mounted) {
        setState(() {
          _isLoading = isLoading;
        });
        print('⏳ État de chargement: $isLoading');
      }
    });

    // Écouter les erreurs
    OrderService.ordersErrorStream.listen((error) {
      if (mounted) {
        setState(() {
          _error = error;
        });
        if (error != null) {
          print('❌ Erreur: $error');
        }
      }
    });
  }

  Future<void> _loadInitialData() async {
    print('🔄 Chargement initial des données...');
    await OrderService.refreshOrders();
  }

  Future<void> _simulateVersement() async {
    print('💰 Simulation d\'un versement...');
    // Simuler un délai de versement
    setState(() {
      _isLoading = true;
    });
    
    await Future.delayed(const Duration(seconds: 2));
    
    // Rafraîchir les commandes comme le ferait un vrai versement
    await OrderService.refreshOrders();
    
    setState(() {
      _isLoading = false;
    });
    
    print('✅ Versement simulé terminé');
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Test Order Stream Sync'),
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: _loadInitialData,
            tooltip: 'Rafraîchir',
          ),
        ],
      ),
      body: Column(
        children: [
          // Indicateurs d'état
          Container(
            padding: const EdgeInsets.all(16),
            color: Colors.grey[100],
            child: Row(
              children: [
                if (_isLoading) ...[
                  const SizedBox(
                    width: 16,
                    height: 16,
                    child: CircularProgressIndicator(strokeWidth: 2),
                  ),
                  const SizedBox(width: 8),
                  const Text('Chargement...'),
                ] else ...[
                  const Icon(Icons.check_circle, color: Colors.green, size: 16),
                  const SizedBox(width: 8),
                  Text('${_orders.length} commandes'),
                ],
                const Spacer(),
                if (_error != null) ...[
                  const Icon(Icons.error, color: Colors.red, size: 16),
                  const SizedBox(width: 4),
                  Text('Erreur', style: TextStyle(color: Colors.red)),
                ],
              ],
            ),
          ),
          
          // Liste des commandes
          Expanded(
            child: _orders.isEmpty
                ? const Center(
                    child: Text('Aucune commande trouvée'),
                  )
                : ListView.builder(
                    itemCount: _orders.length,
                    itemBuilder: (context, index) {
                      final order = _orders[index];
                      return ListTile(
                        title: Text('Commande ${order['id'] ?? 'N/A'}'),
                        subtitle: Text(
                          'Statut: ${order['status'] ?? 'N/A'} - '
                          'Reste: ${order['reste'] ?? 'N/A'} jours',
                        ),
                        trailing: Text(
                          'Payé: ${order['paye'] ?? 0}/${order['jour'] ?? 0}',
                        ),
                      );
                    },
                  ),
          ),
        ],
      ),
      floatingActionButton: FloatingActionButton(
        onPressed: _simulateVersement,
        tooltip: 'Simuler un versement',
        child: const Icon(Icons.payment),
      ),
    );
  }
}
