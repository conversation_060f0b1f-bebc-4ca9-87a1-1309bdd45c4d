# Configuration Nginx pour les callbacks Wave - Callitris
# 
# Ce fichier doit être inclus dans votre configuration Nginx
# pour le domaine: dev-mani.io
#
# Exemple d'inclusion dans le server block:
# include /path/to/nginx_wave_config.conf;

# Configuration des callbacks Wave
location ~ ^/teams/client-api\.callitris-distribution\.com/callback/wave/(success|failure|cancel|notify|return)/?$ {
    # Rediriger vers le gestionnaire PHP
    try_files $uri /teams/client-api.callitris-distribution.com/wave_callback_handler.php?$query_string;
    
    # Configuration PHP-FPM
    fastcgi_pass unix:/var/run/php/php8.1-fpm.sock; # Ajustez selon votre version PHP
    fastcgi_index index.php;
    fastcgi_param SCRIPT_FILENAME $document_root/teams/client-api.callitris-distribution.com/wave_callback_handler.php;
    include fastcgi_params;
    
    # Headers CORS
    add_header 'Access-Control-Allow-Origin' '*' always;
    add_header 'Access-Control-Allow-Methods' 'GET, POST, OPTIONS' always;
    add_header 'Access-Control-Allow-Headers' 'Content-Type, Authorization' always;
    add_header 'Access-Control-Max-Age' '3600' always;
    
    # Headers de sécurité
    add_header 'X-Content-Type-Options' 'nosniff' always;
    add_header 'X-Frame-Options' 'DENY' always;
    add_header 'X-XSS-Protection' '1; mode=block' always;
    
    # Headers pour les callbacks
    add_header 'Content-Type' 'application/json' always;
    add_header 'Cache-Control' 'no-cache, no-store, must-revalidate' always;
    add_header 'Pragma' 'no-cache' always;
    add_header 'Expires' '0' always;
    
    # Gestion des requêtes OPTIONS (CORS preflight)
    if ($request_method = 'OPTIONS') {
        add_header 'Access-Control-Allow-Origin' '*';
        add_header 'Access-Control-Allow-Methods' 'GET, POST, OPTIONS';
        add_header 'Access-Control-Allow-Headers' 'Content-Type, Authorization';
        add_header 'Access-Control-Max-Age' '3600';
        add_header 'Content-Type' 'text/plain; charset=utf-8';
        add_header 'Content-Length' '0';
        return 200;
    }
    
    # Logging spécifique pour les callbacks
    access_log /var/log/nginx/callitris_wave_callbacks.log combined;
    error_log /var/log/nginx/callitris_wave_errors.log;
}

# Configuration pour les anciens callbacks CinetPay (compatibilité)
location ~ ^/teams/client-api\.callitris-distribution\.com/cinetpay/(return|notify|cancel)/?$ {
    try_files $uri /teams/client-api.callitris-distribution.com/cinetpay_callback_handler.php?$query_string;
    
    fastcgi_pass unix:/var/run/php/php8.1-fpm.sock;
    fastcgi_index index.php;
    fastcgi_param SCRIPT_FILENAME $document_root/teams/client-api.callitris-distribution.com/cinetpay_callback_handler.php;
    include fastcgi_params;
    
    # Headers similaires aux callbacks Wave
    add_header 'Access-Control-Allow-Origin' '*' always;
    add_header 'Access-Control-Allow-Methods' 'GET, POST, OPTIONS' always;
    add_header 'Access-Control-Allow-Headers' 'Content-Type, Authorization' always;
}

# Protection des fichiers sensibles
location ~ /\.(env|git|htaccess|htpasswd) {
    deny all;
    return 404;
}

# Protection du dossier logs
location ~ ^/teams/client-api\.callitris-distribution\.com/logs/ {
    deny all;
    return 404;
}

# Protection contre les bots sur les callbacks
location ~ ^/teams/client-api\.callitris-distribution\.com/callback/ {
    # Bloquer les user agents suspects
    if ($http_user_agent ~* (bot|crawler|spider)) {
        return 403;
    }
}

# Configuration de rate limiting pour les callbacks
limit_req_zone $binary_remote_addr zone=callback_limit:10m rate=10r/m;

location ~ ^/teams/client-api\.callitris-distribution\.com/callback/ {
    limit_req zone=callback_limit burst=5 nodelay;
    limit_req_status 429;
}

# Configuration de compression
location ~ \.php$ {
    gzip on;
    gzip_types application/json text/html text/css text/javascript application/javascript;
    gzip_min_length 1000;
}

# Configuration de cache pour les ressources statiques
location ~* \.(css|js|png|jpg|jpeg|gif|ico|svg)$ {
    expires 1M;
    add_header Cache-Control "public, immutable";
    access_log off;
}

# Configuration spécifique pour les callbacks (pas de cache)
location ~ ^/teams/client-api\.callitris-distribution\.com/callback/.*\.php$ {
    expires -1;
    add_header Cache-Control "no-cache, no-store, must-revalidate";
    add_header Pragma "no-cache";
}

# Configuration de timeout pour les callbacks
location ~ ^/teams/client-api\.callitris-distribution\.com/callback/ {
    fastcgi_read_timeout 60s;
    fastcgi_send_timeout 60s;
    fastcgi_connect_timeout 60s;
}

# Configuration de buffer pour les gros payloads
location ~ ^/teams/client-api\.callitris-distribution\.com/callback/ {
    client_max_body_size 10M;
    client_body_buffer_size 128k;
    fastcgi_buffer_size 128k;
    fastcgi_buffers 4 256k;
    fastcgi_busy_buffers_size 256k;
}

# Logging détaillé pour le debugging
log_format callback_detailed '$remote_addr - $remote_user [$time_local] '
                             '"$request" $status $body_bytes_sent '
                             '"$http_referer" "$http_user_agent" '
                             '$request_time $upstream_response_time '
                             '$request_body';

# Appliquer le logging détaillé aux callbacks
location ~ ^/teams/client-api\.callitris-distribution\.com/callback/ {
    access_log /var/log/nginx/callitris_callbacks_detailed.log callback_detailed;
}

# Configuration SSL/TLS (recommandée pour la production)
# Décommentez et ajustez selon votre configuration SSL
#
# ssl_protocols TLSv1.2 TLSv1.3;
# ssl_ciphers ECDHE-RSA-AES256-GCM-SHA512:DHE-RSA-AES256-GCM-SHA512:ECDHE-RSA-AES256-GCM-SHA384:DHE-RSA-AES256-GCM-SHA384;
# ssl_prefer_server_ciphers off;
# ssl_session_cache shared:SSL:10m;
# ssl_session_timeout 10m;
#
# # HSTS (HTTP Strict Transport Security)
# add_header Strict-Transport-Security "max-age=31536000; includeSubDomains" always;

# Configuration de monitoring pour les callbacks
location = /teams/client-api.callitris-distribution.com/callback/health {
    access_log off;
    return 200 '{"status":"ok","service":"wave_callbacks","timestamp":"$time_iso8601"}';
    add_header Content-Type application/json;
}
