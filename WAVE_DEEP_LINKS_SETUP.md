# Configuration des Deep Links Wave pour Callitris

Ce guide explique comment configurer les deep links pour rediriger l'utilisateur vers l'application Callitris après un paiement Wave.

## 🎯 Vue d'ensemble

Après un paiement Wave, l'utilisateur sera automatiquement redirigé vers l'application mobile Callitris avec les informations du paiement.

### Flux de paiement :
1. **Utilisateur** → Initie un paiement dans l'app
2. **App** → Redirige vers Wave pour le paiement
3. **Wave** → Traite le paiement
4. **Wave** → Envoie un callback à votre backend
5. **Backend** → Traite le callback et redirige vers l'app
6. **App** → Affiche le résultat du paiement

## 📱 Configuration Mobile

### Android (AndroidManifest.xml)
Les intent-filters suivants ont été ajoutés :

```xml
<!-- Deep Links pour les retours de paiement Wave -->
<intent-filter android:autoVerify="true">
    <action android:name="android.intent.action.VIEW" />
    <category android:name="android.intent.category.DEFAULT" />
    <category android:name="android.intent.category.BROWSABLE" />
    <data android:scheme="callitris" android:host="payment" />
</intent-filter>

<!-- Deep Links pour les callbacks depuis votre backend -->
<intent-filter android:autoVerify="true">
    <action android:name="android.intent.action.VIEW" />
    <category android:name="android.intent.category.DEFAULT" />
    <category android:name="android.intent.category.BROWSABLE" />
    <data android:scheme="https" 
          android:host="dev-mani.io" 
          android:pathPrefix="/teams/client-api.callitris-distribution.com/callback" />
</intent-filter>
```

### iOS (Info.plist)
Les URL schemes suivants ont été ajoutés :

```xml
<key>CFBundleURLTypes</key>
<array>
    <dict>
        <key>CFBundleURLName</key>
        <string>callitris.payment</string>
        <key>CFBundleURLSchemes</key>
        <array>
            <string>callitris</string>
        </array>
    </dict>
</array>

<key>com.apple.developer.associated-domains</key>
<array>
    <string>applinks:dev-mani.io</string>
</array>
```

## 🔧 Configuration Backend

### 1. Fichiers PHP à déployer

Copiez ces fichiers sur votre serveur :

```
https://dev-mani.io/teams/client-api.callitris-distribution.com/
├── wave_callback_handler.php
├── logs/ (dossier pour les logs)
└── .htaccess (ou configuration Nginx)
```

### 2. URLs de callback Wave

Configurez ces URLs dans votre système de paiement Wave :

- **Success URL** : `https://dev-mani.io/teams/client-api.callitris-distribution.com/callback/wave/success`
- **Failure URL** : `https://dev-mani.io/teams/client-api.callitris-distribution.com/callback/wave/failure`
- **Cancel URL** : `https://dev-mani.io/teams/client-api.callitris-distribution.com/callback/wave/cancel`
- **Notify URL** : `https://dev-mani.io/teams/client-api.callitris-distribution.com/callback/wave/notify`

### 3. Configuration serveur web

#### Apache (.htaccess)
```apache
RewriteEngine On
RewriteRule ^callback/wave/success/?$ wave_callback_handler.php [L,QSA]
RewriteRule ^callback/wave/failure/?$ wave_callback_handler.php [L,QSA]
RewriteRule ^callback/wave/cancel/?$ wave_callback_handler.php [L,QSA]
RewriteRule ^callback/wave/notify/?$ wave_callback_handler.php [L,QSA]
```

#### Nginx
```nginx
location ~ ^/teams/client-api\.callitris-distribution\.com/callback/wave/(success|failure|cancel|notify)/?$ {
    try_files $uri /teams/client-api.callitris-distribution.com/wave_callback_handler.php?$query_string;
    # Configuration PHP-FPM...
}
```

## 🔗 Deep Links générés

L'application peut recevoir ces types de deep links :

### Succès de paiement
```
callitris://payment/success?transaction_id=12345&amount=1000&order_id=67890&status=success
```

### Échec de paiement
```
callitris://payment/failure?transaction_id=12345&error_message=Insufficient+funds&status=failed
```

### Annulation de paiement
```
callitris://payment/cancel?transaction_id=12345&status=cancelled
```

## 🛠️ Personnalisation du backend

### Mise à jour de la base de données

Modifiez la fonction `updateOrderStatus()` dans `wave_callback_handler.php` :

```php
function updateOrderStatus($transactionId, $status, $amount, $orderId = null) {
    try {
        // Votre logique de base de données ici
        $pdo = new PDO('mysql:host=localhost;dbname=callitris', $username, $password);
        
        $sql = "UPDATE commandes SET 
                payment_status = :status, 
                payment_transaction_id = :transaction_id,
                payment_amount = :amount,
                payment_date = NOW()
                WHERE id = :order_id";
        
        $stmt = $pdo->prepare($sql);
        return $stmt->execute([
            ':status' => $status,
            ':transaction_id' => $transactionId,
            ':amount' => $amount,
            ':order_id' => $orderId
        ]);
        
    } catch (Exception $e) {
        logMessage("Erreur DB: " . $e->getMessage());
        return false;
    }
}
```

### Configuration des credentials de base de données

Ajoutez vos credentials dans `wave_callback_handler.php` :

```php
// Configuration de base de données
define('DB_HOST', 'localhost');
define('DB_NAME', 'callitris');
define('DB_USER', 'votre_utilisateur');
define('DB_PASS', 'votre_mot_de_passe');
```

## 🧪 Test des deep links

### Test en développement

1. **Simuler un callback de succès** :
```bash
curl -X POST "https://dev-mani.io/teams/client-api.callitris-distribution.com/callback/wave/success" \
  -H "Content-Type: application/json" \
  -d '{"transaction_id":"test123","status":"success","amount":"1000","order_id":"456"}'
```

2. **Tester le deep link** :
```bash
adb shell am start -W -a android.intent.action.VIEW -d "callitris://payment/success?transaction_id=test123&amount=1000" com.callitris.app
```

### Test sur appareil

1. Ouvrez un navigateur sur votre téléphone
2. Tapez : `callitris://payment/success?transaction_id=test123&amount=1000`
3. L'application devrait s'ouvrir et afficher le dialog de succès

## 📊 Monitoring et logs

### Logs backend
Les logs sont sauvegardés dans :
- `logs/wave_callbacks.log` - Logs des callbacks Wave
- `/var/log/nginx/callitris_wave_callbacks.log` - Logs Nginx
- `/var/log/apache2/callitris_wave_callbacks.log` - Logs Apache

### Logs mobile
Les logs sont visibles dans la console Flutter :
```
[DeepLinkService]: Deep link reçu: callitris://payment/success?...
[PaymentReturnHandler]: Paiement réussi détecté
```

## 🔒 Sécurité

### Validation des callbacks
- Vérifiez l'origine des requêtes
- Validez les signatures Wave si disponibles
- Limitez les tentatives de callback (rate limiting)

### Protection des endpoints
- Utilisez HTTPS uniquement
- Implémentez un rate limiting
- Loggez toutes les tentatives d'accès

## 🚀 Déploiement

### Checklist de déploiement

- [ ] Fichiers PHP déployés sur le serveur
- [ ] Configuration serveur web (Apache/Nginx) appliquée
- [ ] URLs de callback configurées dans Wave
- [ ] Permissions de dossier `logs/` configurées (755)
- [ ] Credentials de base de données configurés
- [ ] Tests de deep links effectués
- [ ] Monitoring des logs activé

### URLs de production

Remplacez les URLs de test par les URLs de production dans :
- `lib/config/api_config.dart`
- Configuration Wave
- Configuration serveur web

## 🆘 Dépannage

### Problèmes courants

1. **Deep link ne s'ouvre pas** :
   - Vérifiez que l'app est installée
   - Vérifiez la configuration AndroidManifest.xml/Info.plist
   - Testez avec `adb shell am start`

2. **Callback non reçu** :
   - Vérifiez les logs serveur
   - Vérifiez la configuration des URLs Wave
   - Testez avec curl

3. **Erreur de base de données** :
   - Vérifiez les credentials
   - Vérifiez les permissions
   - Consultez les logs d'erreur

### Support

Pour obtenir de l'aide :
1. Consultez les logs (`logs/wave_callbacks.log`)
2. Vérifiez la configuration serveur
3. Testez les deep links manuellement
4. Contactez l'équipe de développement avec les logs d'erreur
