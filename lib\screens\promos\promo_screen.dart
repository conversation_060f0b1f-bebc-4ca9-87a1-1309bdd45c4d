import 'dart:convert';

import 'package:callitris/config/api_config.dart';
import 'package:callitris/screens/boutique/product_detail_screen.dart';
import 'package:callitris/services/auth_service.dart';
import 'package:callitris/utils/appTheme.dart';
import 'package:callitris/widgets/fullscreen_image_viewer.dart';
import 'package:flutter/material.dart';
import 'package:http/http.dart' as http;

class InfoScreen extends StatefulWidget {
  final String infoId;
  const InfoScreen({super.key, required this.infoId});

  @override
  State<InfoScreen> createState() => _InfoScreenState();
}

class _InfoScreenState extends State<InfoScreen> {
  Map<String, dynamic> _info = {};
  List<Map<String, dynamic>> _infoProducts = [];
  bool _isLoading = false;

  @override
  void initState() {
    super.initState();
    _loadInfo();
  }

  Future<void> _loadInfo() async {
    setState(() {
      _isLoading = true;
    });

    try {
      final token = await AuthService.getAuthToken();

      if (token == null) {
        print('❌ Token manquant pour récupérer les informations');
        setState(() {
          _info = {};
          _isLoading = false;
        });
        return;
      }

      final response = await http
          .get(
            Uri.parse(
              "${ApiConfig.baseUrl}/pubs/getPubById.php?id=${widget.infoId}",
            ),
            headers: {'Authorization': 'Bearer $token'},
          )
          .timeout(const Duration(seconds: 15));

      if (response.statusCode == 200) {
        final responseBody = response.body.trim();

        if (responseBody.isEmpty) {
          setState(() {
            _info = {};
            _isLoading = false;
          });
          return;
        }

        try {
          // Nettoyer le JSON avant le parsing pour éviter les problèmes d'encodage
          String cleanedJson = responseBody
              .replaceAll(r'\u00e9', 'é')
              .replaceAll(r'\u00e8', 'è')
              .replaceAll(r'\u00ea', 'ê')
              .replaceAll(r'\u00e0', 'à')
              .replaceAll(r'\u00f9', 'ù')
              .replaceAll(r'\u00e7', 'ç');

          print('📄 JSON nettoyé: $cleanedJson');

          final Map<String, dynamic> responseData = jsonDecode(cleanedJson);
          print('📊 Informations reçues: $responseData');

          String rawPath = responseData['imageUrl']?.toString() ?? '';
          String cleanedPath = rawPath.replaceAll(r'\/', '/');

          // Encoder correctement l'URL pour gérer les caractères spéciaux
          String fullUrl;

          try {
            // Séparer le chemin et le nom de fichier
            List<String> pathParts = cleanedPath.split('/');
            if (pathParts.isNotEmpty) {
              // Encoder seulement le nom de fichier (dernière partie)
              String fileName = pathParts.last;
              String encodedFileName = Uri.encodeComponent(fileName);
              pathParts[pathParts.length - 1] = encodedFileName;
              String encodedPath = pathParts.join('/');
              fullUrl = "${ApiConfig.baseUrl2}/$encodedPath";
            } else {
              fullUrl = "${ApiConfig.baseUrl2}/$cleanedPath";
            }
          } catch (e) {
            print('⚠️ Erreur d\'encodage URL: $e');
            fullUrl = "${ApiConfig.baseUrl2}/$cleanedPath";
          }

          setState(() {
            _info = responseData;
            _info['imageUrl'] = fullUrl;
          });

          // Charger les produits liés à cette information
          await _loadInfoProducts();
        } catch (jsonError) {
          print('❌ Erreur de parsing JSON: $jsonError');
          print('📄 Contenu reçu: $responseBody');
          setState(() {
            _info = {};
            _isLoading = false;
          });
        }
      } else {
        print('❌ Erreur HTTP: ${response.statusCode}');
        print('📄 Message d\'erreur: ${response.body}');
        setState(() {
          _info = {};
          _isLoading = false;
        });
      }
    } catch (e) {
      print('❌ Exception lors du chargement des informations: $e');
      setState(() {
        _info = {};
        _isLoading = false;
      });
    }
  }

  Future<void> _loadInfoProducts() async {
    try {
      final token = await AuthService.getAuthToken();

      if (token == null) {
        print('❌ Token manquant pour récupérer les produits');
        setState(() {
          _infoProducts = [];
          _isLoading = false;
        });
        return;
      }

      final response = await http
          .get(
            Uri.parse(
              "${ApiConfig.baseUrl}/pubs/getPromoProducts.php?id=${widget.infoId}",
            ),
            headers: {'Authorization': 'Bearer $token'},
          )
          .timeout(const Duration(seconds: 15));

      if (response.statusCode == 200) {
        final responseBody = response.body.trim();

        if (responseBody.isEmpty) {
          setState(() {
            _infoProducts = [];
            _isLoading = false;
          });
          return;
        }

        try {
          // Nettoyer le JSON avant le parsing pour éviter les problèmes d'encodage
          String cleanedJson = responseBody
              .replaceAll(r'\u00e9', 'é')
              .replaceAll(r'\u00e8', 'è')
              .replaceAll(r'\u00ea', 'ê')
              .replaceAll(r'\u00e0', 'à')
              .replaceAll(r'\u00f9', 'ù')
              .replaceAll(r'\u00e7', 'ç');

          print('📄 JSON produits nettoyé: $cleanedJson');

          final List<dynamic> responseData = jsonDecode(cleanedJson);
          print('📊 Nombre de produits reçus: ${responseData.length}');

          if (responseData.isEmpty) {
            print('⚠️ Aucun produit trouvé dans la réponse');
            setState(() {
              _infoProducts = [];
              _isLoading = false;
            });
            return;
          }

          final List<Map<String, dynamic>> products =
              responseData
                  .map((item) {
                    print('🔍 Traitement du produit: $item');

                    // Vérifier que l'item contient les champs nécessaires
                    if (item == null) {
                      print('⚠️ Item invalide: $item');
                      return null;
                    }

                    String rawPath = item['photo_kit']?.toString() ?? '';
                    String cleanedPath1 = rawPath.replaceAll(r'\/', '/');
                    String cleanedPath = cleanedPath1.replaceAll(
                      r'../../',
                      'app/',
                    );
                    // Encoder correctement l'URL pour gérer les caractères spéciaux
                    String fullUrl;
                    try {
                      if (cleanedPath.isNotEmpty) {
                        // Séparer le chemin et le nom de fichier
                        List<String> pathParts = cleanedPath.split('/');
                        if (pathParts.isNotEmpty) {
                          // Encoder seulement le nom de fichier (dernière partie)
                          String fileName = pathParts.last;
                          String encodedFileName = Uri.encodeComponent(
                            fileName,
                          );
                          pathParts[pathParts.length - 1] = encodedFileName;
                          String encodedPath = pathParts.join('/');
                          print('Encoded path: $encodedPath');
                          fullUrl = "${ApiConfig.baseUrl2}/$encodedPath";
                        } else {
                          fullUrl = "${ApiConfig.baseUrl2}/$cleanedPath";
                        }
                      } else {
                        fullUrl = 'assets/images/product_default.jpg';
                      }
                    } catch (e) {
                      print('⚠️ Erreur d\'encodage URL: $e');
                      fullUrl = "${ApiConfig.baseUrl2}/$cleanedPath";
                    }

                    print('🖼️ URL image générée: $fullUrl');

                    // Prix normal sans réduction
                    int currentPrice =
                        int.tryParse(
                          item['montant_total_kit']?.toString() ?? '0',
                        ) ??
                        0;

                    return {
                      'id': item['id_kit']?.toString() ?? '',
                      'name':
                          item['nom_produit']?.toString() ?? 'Produit sans nom',
                      'description':
                          item['description_kit']?.toString() ??
                          'Aucune description',
                      'price': currentPrice,
                      'imageUrl': fullUrl,
                      'category': item['category']?.toString() ?? 'Général',
                      'dailyPrice':
                          int.tryParse(
                            item['cout_journalier_kit']?.toString() ?? '0',
                          ) ??
                          0,
                      'inStock': item['inStock'] ?? true,
                    };
                  })
                  .where((item) => item != null)
                  .cast<Map<String, dynamic>>()
                  .toList();

          print('✅ ${products.length} produits traités avec succès');

          setState(() {
            _infoProducts = products;
            _isLoading = false;
          });
        } catch (jsonError) {
          print('❌ Erreur de parsing JSON: $jsonError');
          print('📄 Contenu reçu: $responseBody');
          setState(() {
            _infoProducts = [];
            _isLoading = false;
          });
        }
      } else {
        print('❌ Erreur HTTP: ${response.statusCode}');
        print('📄 Message d\'erreur: ${response.body}');
        setState(() {
          _infoProducts = [];
          _isLoading = false;
        });
      }
    } catch (e) {
      print('❌ Exception lors du chargement des produits: $e');
      setState(() {
        _infoProducts = [];
        _isLoading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: CustomScrollView(
        slivers: [
          _buildAppBar(),
          SliverToBoxAdapter(child: _buildInfoDetails()),
          SliverToBoxAdapter(child: _buildProductsList()),
        ],
      ),
    );
  }

  Widget _buildAppBar() {
    // Vérifier si l'URL de l'image est une URL complète ou un chemin relatif
    final String imageUrl = _info['imageUrl']?.toString() ?? '';
    final bool isNetworkImage =
        imageUrl.isNotEmpty &&
        (imageUrl.startsWith('http://') ||
            imageUrl.startsWith('https://') ||
            imageUrl.contains('public/img') ||
            imageUrl.startsWith('/'));

    return SliverAppBar(
      expandedHeight: 300.0,
      backgroundColor: Colors.white,
      elevation: 0,
      pinned: true,
      stretch: true,
      leading: Container(
        margin: const EdgeInsets.all(8),
        decoration: BoxDecoration(
          color: Colors.white.withValues(alpha: 0.9),
          shape: BoxShape.circle,
        ),
        child: IconButton(
          icon: const Icon(Icons.arrow_back, color: Colors.black),
          onPressed: () => Navigator.of(context).pop(),
        ),
      ),
      actions: [
        // Bouton de partage
        Container(
          margin: const EdgeInsets.all(8),
          decoration: BoxDecoration(
            color: Colors.white.withValues(alpha: 0.9),
            shape: BoxShape.circle,
          ),
          child: IconButton(
            icon: const Icon(Icons.share, color: Colors.black),
            onPressed: () {
              // Logique de partage
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(content: Text('Partage des informations...')),
              );
            },
            tooltip: 'Partager',
          ),
        ),
      ],
      flexibleSpace: FlexibleSpaceBar(
        background: GestureDetector(
          onTap: () {
            // Ouvrir l'image en plein écran
            Navigator.of(context).push(
              PageRouteBuilder(
                pageBuilder:
                    (context, animation, secondaryAnimation) =>
                        FullscreenImageViewer(
                          imageUrl: imageUrl,
                          heroTag:
                              'product-${_info['id']?.toString() ?? 'unknown'}',
                          isNetworkImage: isNetworkImage,
                        ),
                transitionsBuilder: (
                  context,
                  animation,
                  secondaryAnimation,
                  child,
                ) {
                  return FadeTransition(opacity: animation, child: child);
                },
                transitionDuration: const Duration(milliseconds: 300),
                opaque: false,
              ),
            );
          },
          child: Stack(
            fit: StackFit.expand,
            children: [
              Hero(
                tag: 'product-${_info['id']?.toString() ?? 'unknown'}',
                child:
                    imageUrl.isEmpty
                        ? Container(
                          color: Colors.grey[200],
                          child: Center(
                            child: Icon(
                              Icons.image_not_supported,
                              color: Colors.grey[400],
                              size: 60,
                            ),
                          ),
                        )
                        : isNetworkImage
                        ? Image.network(
                          imageUrl,
                          fit: BoxFit.cover,
                          errorBuilder: (context, error, stackTrace) {
                            return Container(
                              color: Colors.grey[200],
                              child: Center(
                                child: Icon(
                                  Icons.image_not_supported,
                                  color: Colors.grey[400],
                                  size: 60,
                                ),
                              ),
                            );
                          },
                        )
                        : Image.asset(
                          imageUrl,
                          fit: BoxFit.cover,
                          errorBuilder: (context, error, stackTrace) {
                            return Container(
                              color: Colors.grey[200],
                              child: Center(
                                child: Icon(
                                  Icons.image_not_supported,
                                  color: Colors.grey[400],
                                  size: 60,
                                ),
                              ),
                            );
                          },
                        ),
              ),

              // Indicateur visuel pour montrer que l'image est cliquable
              Positioned(
                bottom: 16,
                right: 16,
                child: Container(
                  padding: const EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    color: Colors.black.withValues(alpha: 0.6),
                    borderRadius: BorderRadius.circular(20),
                  ),
                  child: Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Icon(Icons.zoom_in, color: Colors.white, size: 16),
                      const SizedBox(width: 4),
                      Text(
                        'Voir en grand',
                        style: TextStyle(
                          color: Colors.white,
                          fontSize: 12,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildInfoDetails() {
    if (_info.isEmpty) {
      return Container(
        padding: const EdgeInsets.all(24),
        child: const Center(child: Text('Informations non disponibles')),
      );
    }

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Container(
          padding: const EdgeInsets.all(24),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  Container(
                    padding: const EdgeInsets.symmetric(
                      horizontal: 10,
                      vertical: 4,
                    ),
                    decoration: BoxDecoration(
                      color: AppTheme.color.primaryColor.withValues(alpha: 0.1),
                      borderRadius: BorderRadius.circular(20),
                    ),
                    child: Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Icon(
                          Icons.info_outline,
                          size: 14,
                          color: AppTheme.color.primaryColor,
                        ),
                        const SizedBox(width: 4),
                        Text(
                          'INFO',
                          style: TextStyle(
                            fontSize: 12,
                            fontWeight: FontWeight.bold,
                            color: AppTheme.color.primaryColor,
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 12),

              // Nom de l'information
              Text(
                _info['name'] ?? 'Information sans nom',
                style: const TextStyle(
                  fontSize: 24,
                  fontWeight: FontWeight.bold,
                  color: Colors.black,
                  height: 1.2,
                ),
              ),
              const SizedBox(height: 8),

              // Description de l'information
              if (_info['description'] != null &&
                  _info['description'].toString().isNotEmpty)
                Text(
                  _info['description'],
                  style: TextStyle(
                    fontSize: 16,
                    color: AppTheme.color.brunGris,
                    height: 1.4,
                  ),
                ),
              const SizedBox(height: 16),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildProductsList() {
    if (_isLoading) {
      return Container(
        padding: const EdgeInsets.all(24),
        child: const Center(child: CircularProgressIndicator()),
      );
    }

    if (_infoProducts.isEmpty) {
      return Container(
        padding: const EdgeInsets.all(24),
        child: Center(
          child: Column(
            children: [
              Icon(
                Icons.shopping_bag_outlined,
                size: 64,
                color: Colors.grey[400],
              ),
              const SizedBox(height: 16),
              Text(
                'Aucun produit disponible',
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.w500,
                  color: Colors.grey[600],
                ),
              ),
              const SizedBox(height: 8),
              Text(
                'Cette section ne contient pas encore de produits.',
                style: TextStyle(fontSize: 14, color: Colors.grey[500]),
                textAlign: TextAlign.center,
              ),
            ],
          ),
        ),
      );
    }

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Padding(
          padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 16),
          child: Row(
            children: [
              Text(
                'Produits associés',
                style: TextStyle(
                  fontSize: 20,
                  fontWeight: FontWeight.bold,
                  color: AppTheme.color.textColor,
                ),
              ),
              const SizedBox(width: 8),
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                decoration: BoxDecoration(
                  color: AppTheme.color.primaryColor.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Text(
                  '${_infoProducts.length}',
                  style: TextStyle(
                    fontSize: 12,
                    fontWeight: FontWeight.bold,
                    color: AppTheme.color.primaryColor,
                  ),
                ),
              ),
            ],
          ),
        ),
        Padding(
          padding: const EdgeInsets.symmetric(horizontal: 16),
          child: LayoutBuilder(
            builder: (context, constraints) {
              // Calculer la largeur de chaque carte
              final cardWidth =
                  (constraints.maxWidth - 12) / 2; // 12 = crossAxisSpacing

              return Wrap(
                spacing: 12, // Espacement horizontal
                runSpacing: 12, // Espacement vertical
                children:
                    _infoProducts.map((product) {
                      return SizedBox(
                        width: cardWidth,
                        child: _buildProductGridCard(product),
                      );
                    }).toList(),
              );
            },
          ),
        ),
        const SizedBox(height: 24),
      ],
    );
  }

  Widget _buildProductGridCard(Map<String, dynamic> product) {
    final String imageUrl = product['imageUrl'] ?? '';
    final bool isNetworkImage =
        imageUrl.isNotEmpty &&
        (imageUrl.startsWith('http') || imageUrl.contains('public/img'));
    final int price = product['price'] ?? 0;

    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 10,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          borderRadius: BorderRadius.circular(16),
          onTap: () {
            // Navigation vers ProductDetailScreen
            Navigator.push(
              context,
              MaterialPageRoute(
                builder:
                    (context) => ProductDetailScreen(
                      product: {
                        ...product,
                        'nombre_jour_cat': 150, // Valeur par défaut
                        'categoryName': 'Promotion',
                      },
                    ),
              ),
            );
          },
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisSize: MainAxisSize.min,
            children: [
              // Image du produit avec badges
              AspectRatio(
                aspectRatio: 1.2, // Ratio largeur/hauteur pour l'image
                child: Stack(
                  children: [
                    Container(
                      width: double.infinity,
                      decoration: BoxDecoration(
                        borderRadius: const BorderRadius.vertical(
                          top: Radius.circular(16),
                        ),
                        color: Colors.grey[100],
                      ),
                      child: ClipRRect(
                        borderRadius: const BorderRadius.vertical(
                          top: Radius.circular(16),
                        ),
                        child:
                            imageUrl.isEmpty
                                ? Center(
                                  child: Icon(
                                    Icons.image_not_supported_outlined,
                                    size: 40,
                                    color: Colors.grey[400],
                                  ),
                                )
                                : isNetworkImage
                                ? Image.network(
                                  imageUrl,
                                  fit: BoxFit.cover,
                                  errorBuilder: (context, error, stackTrace) {
                                    return Center(
                                      child: Icon(
                                        Icons.image_not_supported_outlined,
                                        size: 40,
                                        color: Colors.grey[400],
                                      ),
                                    );
                                  },
                                )
                                : Image.asset(
                                  imageUrl,
                                  fit: BoxFit.cover,
                                  errorBuilder: (context, error, stackTrace) {
                                    return Center(
                                      child: Icon(
                                        Icons.image_not_supported_outlined,
                                        size: 40,
                                        color: Colors.grey[400],
                                      ),
                                    );
                                  },
                                ),
                      ),
                    ),
                  ],
                ),
              ),

              // Informations du produit
              Padding(
                padding: const EdgeInsets.all(12),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    // Nom du produit
                    Text(
                      product['name'] ?? 'Produit sans nom',
                      style: const TextStyle(
                        fontSize: 14,
                        fontWeight: FontWeight.bold,
                        color: Colors.black,
                      ),
                      maxLines: 2,
                      overflow: TextOverflow.ellipsis,
                    ),
                    const SizedBox(height: 8),

                    // Prix
                    Text(
                      '${price.toString().replaceAllMapped(RegExp(r'(\d{1,3})(?=(\d{3})+(?!\d))'), (Match m) => '${m[1]} ')} FCFA',
                      style: TextStyle(
                        fontSize: 14,
                        fontWeight: FontWeight.bold,
                        color: AppTheme.color.primaryColor,
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
