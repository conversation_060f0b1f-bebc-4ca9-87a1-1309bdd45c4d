import 'package:flutter/material.dart';
import 'package:callitris/models/cinetpay_transaction.dart';
import 'package:callitris/services/transaction_history_service.dart';
import 'package:callitris/services/payment_retry_service.dart';
import 'package:callitris/utils/payment_error_handler.dart';
import 'package:callitris/widgets/transaction_status_widget.dart';

/// Widget pour afficher et gérer les transactions en attente
class PendingTransactionsWidget extends StatefulWidget {
  const PendingTransactionsWidget({super.key});

  @override
  State<PendingTransactionsWidget> createState() => _PendingTransactionsWidgetState();
}

class _PendingTransactionsWidgetState extends State<PendingTransactionsWidget> {
  List<CinetPayTransaction> _pendingTransactions = [];
  bool _isLoading = true;
  bool _isRefreshing = false;

  @override
  void initState() {
    super.initState();
    _loadPendingTransactions();
  }

  Future<void> _loadPendingTransactions() async {
    setState(() => _isLoading = true);
    
    try {
      final pending = await TransactionHistoryService.getTransactionsByStatus(
        TransactionStatus.pending,
      );
      
      final processing = await TransactionHistoryService.getTransactionsByStatus(
        TransactionStatus.processing,
      );
      
      setState(() {
        _pendingTransactions = [...pending, ...processing];
        _isLoading = false;
      });
    } catch (e) {
      setState(() => _isLoading = false);
      if (mounted) {
        PaymentErrorHandler.showErrorNotification(context, e.toString());
      }
    }
  }

  Future<void> _refreshTransactions() async {
    setState(() => _isRefreshing = true);
    
    try {
      // Vérifier le statut de toutes les transactions en attente
      final updatedTransactions = await PaymentRetryService.checkAllPendingTransactions();
      
      if (updatedTransactions.isNotEmpty && mounted) {
        PaymentErrorHandler.showInfoNotification(
          context,
          '${updatedTransactions.length} transaction(s) mise(s) à jour',
        );
      }
      
      // Recharger la liste
      await _loadPendingTransactions();
    } catch (e) {
      if (mounted) {
        PaymentErrorHandler.showErrorNotification(context, e.toString());
      }
    } finally {
      setState(() => _isRefreshing = false);
    }
  }

  Future<void> _retryTransaction(CinetPayTransaction transaction) async {
    try {
      PaymentErrorHandler.showLoadingDialog(context, message: 'Reprise du paiement...');
      
      final result = await PaymentRetryService.retryFailedTransaction(
        context: context,
        transactionId: transaction.id,
      );
      
      PaymentErrorHandler.hideLoadingDialog(context);
      
      if (result['success']) {
        PaymentErrorHandler.showSuccessNotification(
          context,
          'Paiement repris avec succès !',
        );
        _loadPendingTransactions();
      } else {
        PaymentErrorHandler.showErrorNotification(context, result['message']);
      }
    } catch (e) {
      PaymentErrorHandler.hideLoadingDialog(context);
      PaymentErrorHandler.showErrorNotification(context, e.toString());
    }
  }

  @override
  Widget build(BuildContext context) {
    if (_isLoading) {
      return const Center(
        child: CircularProgressIndicator(),
      );
    }

    if (_pendingTransactions.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.check_circle_outline,
              size: 64,
              color: Colors.green.shade300,
            ),
            const SizedBox(height: 16),
            const Text(
              'Aucune transaction en attente',
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.w500,
                color: Colors.grey,
              ),
            ),
            const SizedBox(height: 8),
            const Text(
              'Toutes vos transactions sont à jour',
              style: TextStyle(
                fontSize: 14,
                color: Colors.grey,
              ),
            ),
          ],
        ),
      );
    }

    return Column(
      children: [
        // Header avec bouton de rafraîchissement
        Padding(
          padding: const EdgeInsets.all(16),
          child: Row(
            children: [
              Icon(
                Icons.pending_actions,
                color: Colors.orange,
                size: 20,
              ),
              const SizedBox(width: 8),
              const Text(
                'Transactions en attente',
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.w600,
                ),
              ),
              const Spacer(),
              IconButton(
                icon: _isRefreshing
                    ? const SizedBox(
                        width: 20,
                        height: 20,
                        child: CircularProgressIndicator(strokeWidth: 2),
                      )
                    : const Icon(Icons.refresh),
                onPressed: _isRefreshing ? null : _refreshTransactions,
                tooltip: 'Vérifier le statut',
              ),
            ],
          ),
        ),
        
        // Liste des transactions
        Expanded(
          child: ListView.separated(
            padding: const EdgeInsets.symmetric(horizontal: 16),
            itemCount: _pendingTransactions.length,
            separatorBuilder: (context, index) => const SizedBox(height: 12),
            itemBuilder: (context, index) {
              final transaction = _pendingTransactions[index];
              return _buildTransactionCard(transaction);
            },
          ),
        ),
      ],
    );
  }

  Widget _buildTransactionCard(CinetPayTransaction transaction) {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      child: Column(
        children: [
          TransactionStatusWidget(
            transaction: transaction,
            showDetails: false,
          ),
          
          // Actions
          Padding(
            padding: const EdgeInsets.all(16),
            child: Row(
              children: [
                // Bouton de vérification du statut
                Expanded(
                  child: OutlinedButton.icon(
                    icon: const Icon(Icons.refresh, size: 16),
                    label: const Text('Vérifier'),
                    onPressed: () => _checkTransactionStatus(transaction),
                    style: OutlinedButton.styleFrom(
                      foregroundColor: Colors.blue,
                      side: const BorderSide(color: Colors.blue),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(8),
                      ),
                    ),
                  ),
                ),
                
                const SizedBox(width: 12),
                
                // Bouton de retry (seulement pour les transactions échouées)
                if (transaction.status == TransactionStatus.failed)
                  Expanded(
                    child: ElevatedButton.icon(
                      icon: const Icon(Icons.replay, size: 16),
                      label: const Text('Reprendre'),
                      onPressed: PaymentErrorHandler.isRetryableError(transaction.failureReason)
                          ? () => _retryTransaction(transaction)
                          : null,
                      style: ElevatedButton.styleFrom(
                        backgroundColor: const Color(0xFF4CAF50),
                        foregroundColor: Colors.white,
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(8),
                        ),
                      ),
                    ),
                  ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Future<void> _checkTransactionStatus(CinetPayTransaction transaction) async {
    try {
      PaymentErrorHandler.showLoadingDialog(context, message: 'Vérification du statut...');
      
      final result = await PaymentRetryService.checkPendingTransaction(transaction.id);
      
      PaymentErrorHandler.hideLoadingDialog(context);
      
      if (result['success']) {
        if (result['statusChanged'] == true) {
          PaymentErrorHandler.showInfoNotification(
            context,
            'Statut mis à jour !',
          );
          _loadPendingTransactions();
        } else {
          PaymentErrorHandler.showInfoNotification(
            context,
            'Aucun changement de statut',
          );
        }
      } else {
        PaymentErrorHandler.showErrorNotification(context, result['message']);
      }
    } catch (e) {
      PaymentErrorHandler.hideLoadingDialog(context);
      PaymentErrorHandler.showErrorNotification(context, e.toString());
    }
  }
}
