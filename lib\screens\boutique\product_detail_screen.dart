import 'dart:async';
import 'dart:convert';

import 'package:callitris/config/api_config.dart';
import 'package:callitris/screens/boutique/my_orders_screen.dart';
import 'package:callitris/services/order_service.dart';
import 'package:callitris/utils/appTheme.dart';
import 'package:callitris/widgets/fullscreen_image_viewer.dart';
import 'package:flutter/material.dart';
import 'package:http/http.dart' as http;
import 'package:shared_preferences/shared_preferences.dart';

import '../../services/auth_service.dart';
import '../../services/cinetpay_service.dart';
import '../../services/wallet_service.dart';
import '../../utils/image_utils.dart';
import '../../widgets/payment_method_selector.dart';
import '../../widgets/navigation_menu_button.dart';

class ProductDetailScreen extends StatefulWidget {
  final Map<String, dynamic> product;

  const ProductDetailScreen({super.key, required this.product});

  @override
  State<ProductDetailScreen> createState() => _ProductDetailScreenState();
}

class _ProductDetailScreenState extends State<ProductDetailScreen>
    with WidgetsBindingObserver {
  late int _totalPrice;
  late int _dailyPrice;
  late int _duration;
  final bool _isExpanded = true;
  // Supprimé: paiement unique, seulement paiement journalier
  String livraison = "indisponible";
  String delai_livraison = "indisponible";
  String garantie = "aucune";

  // Variables pour la gestion des paiements
  String? storageCommandeId;
  String? storageClientId;
  String? transactionCode;

  // Variables pour stocker les informations de transaction en cours
  double? _pendingMontant;
  double? _pendingMonnaie;
  String? _pendingTransactionCode;
  String? _pendingItemId; // Pour stocker l'ID de l'item en cours de commande

  // Variables pour contrôler les modals de chargement
  bool _isProcessingOperation = false;
  String _currentProcessingMessage = '';

  // Variable pour bloquer toutes les actions
  bool _isGlobalLoading = false;

  @override
  void initState() {
    super.initState();
    // Utiliser la durée définie dans le produit (nombre_jour_cat)
    _duration = widget.product['nombre_jour_cat'] ?? 150;
    _dailyPrice =
        widget.product['journalier_cat'] ?? widget.product['dailyPrice'] ?? 0;
    livraison = widget.product['livraison'] ?? 'indisponible';
    delai_livraison = widget.product['delai_livraison'] ?? 'pas de délai';
    garantie = widget.product['garantie'] ?? 'aucune';
    _calculateTotalPrice();

    // Ajouter l'observateur du cycle de vie de l'application
    WidgetsBinding.instance.addObserver(this);

    // Tester le formatage d'URL
    ImageUtils.testFormatImageUrl();

    // Initialiser l'application avec vérification des transactions en attente
    _initializeApp();
  }

  Future<void> _initializeApp() async {
    // Initialiser les préférences
    await _initPrefs();
  }

  @override
  void dispose() {
    // Marquer que le widget est en cours de destruction
    _isProcessingOperation = false;
    _isGlobalLoading = false;

    // Supprimer l'observateur du cycle de vie de l'application
    WidgetsBinding.instance.removeObserver(this);
    super.dispose();
  }

  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {
    super.didChangeAppLifecycleState(state);
    if (state == AppLifecycleState.resumed) {
      print(
        '📱 App revenue au premier plan - vérification immédiate des transactions',
      );
      _initializeApp();
      // Vérifier immédiatement les transactions en attente quand l'app revient au premier plan
      _checkPendingTransactionsOnResume();
    }
  }

  Future<void> _initPrefs() async {
    transactionCode = await OrderService.readData('payment_code');
    storageCommandeId = await OrderService.readData('commande_id');
    storageClientId = await OrderService.readData('client_id');

    try {
      if (transactionCode != null && transactionCode!.isNotEmpty) {
        await checkTransactionWaveState();
        await _checkCinetPayTransactionStatus(transactionCode!);
      }
    } catch (error) {
      print('Erreur lors de l\'initialisation des préférences: $error');
    }
  }

  /// Vérifie s'il y a des transactions en attente et les traite
  Future<void> _checkPendingTransactions() async {
    try {
      // Utiliser la méthode centralisée pour charger les données
      await _loadPendingTransactionFromStorage();

      if (_pendingMontant != null &&
          _pendingTransactionCode != null &&
          _pendingItemId != null) {
        print(
          'Transaction en attente trouvée: $_pendingTransactionCode, montant: $_pendingMontant, item: $_pendingItemId',
        );

        // Détecter le type de transaction basé sur le format du code
        await _checkCinetPayTransactionStatus(_pendingTransactionCode!);
        await _checkWaveTransactionStatus(_pendingTransactionCode!);
      } else {
        print('Aucune transaction en attente complète trouvée');
      }
    } catch (e) {
      print('Erreur lors de la vérification des transactions en attente: $e');
    }
  }

  /// Vérifie les transactions en attente quand l'app revient au premier plan
  Future<void> _checkPendingTransactionsOnResume() async {
    try {
      // Utiliser la méthode centralisée pour charger les données
      await _loadPendingTransactionFromStorage();
      print(
        '🔄 Transaction en attente détectée au retour: $_pendingTransactionCode, montant: $_pendingMontant, item: $_pendingItemId',
      );

      // Détecter le type de transaction et démarrer la vérification immédiate + périodique
      if (_isCinetPayTransactionId(_pendingTransactionCode!)) {
        print(
          '🏦 Transaction CinetPay - vérification immédiate et démarrage du monitoring...',
        );
        // Vérification immédiate
        await _checkCinetPayTransactionStatus(_pendingTransactionCode!);
        // Démarrer la vérification périodique si la transaction est toujours en attente
        if (_pendingTransactionCode != null) {
          _startCinetPayPeriodicStatusCheck();
        }
      } else {
        print(
          '🌊 Transaction Wave - vérification immédiate et démarrage du monitoring...',
        );
        // Vérification immédiate
        await _checkWaveTransactionStatus(_pendingTransactionCode!);
        // Démarrer la vérification périodique si la transaction est toujours en attente
        if (_pendingTransactionCode != null) {
          _startPeriodicStatusCheck();
        }
      }
    } catch (e) {
      print('❌ Erreur lors de la vérification des transactions au retour: $e');
    }
  }

  /// Stocke les informations d'une transaction en attente
  Future<void> _storePendingTransaction(
    double montant,
    double monnaie,
    String itemId,
  ) async {
    try {
      print(
        'Stockage de la transaction en attente: montant=$montant, monnaie=$monnaie, item=$itemId',
      );

      final prefs = await SharedPreferences.getInstance();
      await prefs.setDouble('pending_montant', montant);
      await prefs.setDouble('pending_monnaie', monnaie);
      await prefs.setString('pending_item_id', itemId);

      // Stocker dans les variables de classe
      _pendingMontant = montant;
      _pendingMonnaie = monnaie;
      _pendingItemId = itemId;

      print(
        'Transaction en attente stockée avec succès: montant=$_pendingMontant, monnaie=$_pendingMonnaie, item=$_pendingItemId',
      );

      // Vérification immédiate pour s'assurer que les données sont bien stockées
      final verification = await prefs.getDouble('pending_montant');
      print('Vérification stockage: montant récupéré = $verification');
    } catch (e) {
      print('Erreur lors du stockage de la transaction en attente: $e');
    }
  }

  /// Met à jour le code de transaction en attente
  Future<void> _updatePendingTransactionCode(String transactionCode) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setString('pending_transaction_code', transactionCode);

      // Stocker dans les variables de classe
      _pendingTransactionCode = transactionCode;

      print('Code de transaction en attente mis à jour: $transactionCode');
    } catch (e) {
      print('Erreur lors de la mise à jour du code de transaction: $e');
    }
  }

  /// Nettoie les données de transaction en attente
  Future<void> _clearPendingTransaction() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.remove('pending_montant');
      await prefs.remove('pending_monnaie');
      await prefs.remove('pending_transaction_code');
      await prefs.remove('pending_item_id');

      // Nettoyer les variables de classe
      _pendingMontant = null;
      _pendingMonnaie = null;
      _pendingTransactionCode = null;
      _pendingItemId = null;

      print('Données de transaction en attente nettoyées');
    } catch (e) {
      print('Erreur lors du nettoyage des données en attente: $e');
    }
  }

  /// Charge les données de transaction en attente depuis SharedPreferences
  Future<void> _loadPendingTransactionFromStorage() async {
    try {
      final prefs = await SharedPreferences.getInstance();

      final pendingMontant = prefs.getString('pending_montant');
      final pendingMonnaie = prefs.getString('pending_monnaie');
      final pendingTransactionCode = prefs.getString(
        'pending_transaction_code',
      );
      final pendingItemId = prefs.getString('pending_item_id');
      transactionCode = prefs.getString('payment_code');
      storageCommandeId = prefs.getString('commande_id');
      storageClientId = prefs.getString('client_id');

      if (pendingMontant != null && pendingItemId != null) {
        // Mettre à jour les variables de classe
        _pendingMontant = double.tryParse(pendingMontant!) ?? 0.0;
        _pendingMonnaie = double.tryParse(pendingMonnaie!) ?? 0.0;
        _pendingTransactionCode = transactionCode ?? pendingTransactionCode;
        _pendingItemId = pendingItemId;

        print(
          'Données de transaction récupérées: montant=$_pendingMontant, monnaie=$_pendingMonnaie, item=$_pendingItemId, transaction=$_pendingTransactionCode',
        );
      } else {
        print(
          'Aucune donnée de transaction en attente trouvée dans le stockage',
        );
      }
    } catch (e) {
      print('Erreur lors du chargement des données en attente: $e');
    }
  }

  /// Vérifie le statut d'une transaction Wave spécifique
  Future<void> _checkWaveTransactionStatus(String transactionCode) async {
    try {
      final token = await AuthService.getAuthToken();

      if (token == null) {
        print('Token manquant pour vérification Wave');
        return;
      }

      final response = await http.post(
        Uri.parse("${ApiConfig.baseUrl}/paiement/wave_pay_chek.php"),
        body: {'transaction_code': transactionCode},
        headers: {'Authorization': 'Bearer $token'},
      );

      if (response.statusCode == 200) {
        final Map<String, dynamic> responseData = jsonDecode(response.body);
        print('Wave transaction check: $responseData');

        if (responseData['payment_status'] == 'succeeded') {
          _showPaymentSuccess('succeeded');
        } else if (responseData['payment_status'] == 'processing') {
          _showPaymentSuccess('processing');
          print('Transaction encore en cours de traitement');
        } else {
          _showPaymentSuccess('failed');
        }
      } else {
        print('Erreur Wave check : ${response.statusCode}');
      }
    } catch (error) {
      print('Erreur lors de la vérification Wave : $error');
    }
  }

  Future<void> checkTransactionWaveState() async {
    if (transactionCode != null) {
      await _checkWaveTransactionStatus(transactionCode!);
    }
  }

  /// Traite un paiement réussi en effectuant la commande
  Future<void> _processSuccessfulPayment() async {
    try {
      // Si les variables de classe sont nulles, essayer de les récupérer depuis SharedPreferences
      if (_pendingMontant == null || _pendingItemId == null) {
        print(
          'Variables de classe nulles, récupération depuis SharedPreferences...',
        );
        await _loadPendingTransactionFromStorage();
      }

      // Vérifier à nouveau après la récupération
      if (_pendingMontant == null || _pendingItemId == null) {
        print('Aucune transaction en attente à traiter après récupération');
        print(
          '_pendingMontant: $_pendingMontant, _pendingItemId: $_pendingItemId',
        );

        // Afficher un message d'erreur à l'utilisateur
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text(
              'Aucune transaction en attente trouvée. Veuillez réessayer.',
            ),
            backgroundColor: Colors.orange,
          ),
        );
        return;
      }

      print(
        'Traitement du paiement réussi: montant=$_pendingMontant, item=$_pendingItemId',
      );

      // Effectuer la commande via l'API
      final result = await OrderService.placeOrder(
        _pendingItemId!,
        isCarnet: true,
      );

      if (result['success']) {
        // Commande réussie
        print('Commande effectuée avec succès');
        print("message : ${result['message']}");

        // Enregistrer le versement après le succès de la commande
        try {
          final commandeId = result['commande_id']?.toString();
          if (commandeId != null && _pendingMontant != null) {
            print(
              'Enregistrement du versement: commandeId=$commandeId, montant=$_pendingMontant',
            );

            final versementResult = await OrderService.addVersement(
              commandeId,
              _pendingMontant!,
              monnaie: _pendingMonnaie ?? 0.0,
              transactionCode: transactionCode,
            );

            if (versementResult['success']) {
              print('Versement enregistré avec succès');

              // Nettoyer les données en attente
              await _clearPendingTransaction();
              await _clearPaymentCodeAndReload();

              // Afficher le succès avec informations de vérification
              if (!mounted) return;
              _showOrderSuccess(
                '${result['message'] ?? 'Commande enregistrée avec succès !'}\n\n✓ Commande confirmée\n✓ Versement enregistré',
              );
            } else {
              print(
                'Erreur lors de l\'enregistrement du versement: ${versementResult['message']}',
              );
              // Afficher l'erreur de versement
              _showOrderError(
                versementResult['message'] ??
                    'Erreur lors de l\'enregistrement du versement',
              );
            }
          } else {
            print(
              "Commande ID invalide : $commandeId ou montant manquant : $_pendingMontant",
            );
          }
        } catch (e) {
          print('Erreur lors de l\'enregistrement du versement: $e');
        }
      } else {
        print(
          'Erreur lors de l\'enregistrement de la commande: ${result['message']}',
        );
        _showOrderError(
          result['message'] ??
              'Erreur lors de l\'enregistrement de la commande',
        );
      }
    } catch (e) {
      print('Erreur lors du traitement du paiement réussi: $e');
      _showOrderError('Erreur lors du traitement du paiement: $e');
    }
  }

  Future<void> _clearPaymentCodeAndReload() async {
    SharedPreferences prefs = await SharedPreferences.getInstance();
    await prefs.remove('payment_code');
    await prefs.remove('commande_id');
    await prefs.remove('client_id');
    // Nettoyer les variables de classe
    if (!mounted) return;
    setState(() {
      transactionCode = null;
    });
  }

  void _calculateTotalPrice() {
    // Prix total pour paiement journalier uniquement
    _totalPrice = _dailyPrice * _duration;
  }

  @override
  Widget build(BuildContext context) {
    return Stack(
      children: [
        Scaffold(
          backgroundColor: Colors.grey.shade50,
          body: CustomScrollView(
            slivers: [
              _buildModernAppBar(),
              SliverToBoxAdapter(child: _buildModernProductInfo()),
            ],
          ),
          bottomNavigationBar: _buildModernBottomBar(),
        ),
        // Overlay de chargement global
        if (_isGlobalLoading) _buildGlobalLoadingOverlay(),
      ],
    );
  }

  Widget _buildPaymentOption({
    required IconData icon,
    required String title,
    required String subtitle,
    required String price,
    required bool isRecommended,
    required bool isSelected,
    required VoidCallback onSelect,
  }) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color:
            isSelected
                ? AppTheme.color.primaryColor.withValues(alpha: 0.05)
                : Colors.white,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: isSelected ? AppTheme.color.primaryColor : Colors.grey[300]!,
          width: isSelected ? 2 : 1,
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color:
                      isSelected
                          ? AppTheme.color.primaryColor.withValues(alpha: 0.1)
                          : Colors.grey[100],
                  shape: BoxShape.circle,
                ),
                child: Icon(
                  icon,
                  color:
                      isSelected
                          ? AppTheme.color.primaryColor
                          : Colors.grey[700],
                  size: 20,
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      title,
                      style: TextStyle(
                        fontWeight: FontWeight.bold,
                        fontSize: 16,
                        color:
                            isSelected
                                ? AppTheme.color.primaryColor
                                : Colors.black,
                      ),
                    ),
                    Text(
                      subtitle,
                      style: TextStyle(fontSize: 13, color: Colors.grey[600]),
                    ),
                  ],
                ),
              ),
              if (isRecommended)
                Container(
                  padding: const EdgeInsets.symmetric(
                    horizontal: 8,
                    vertical: 4,
                  ),
                  decoration: BoxDecoration(
                    color: AppTheme.color.greenColor.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(4),
                  ),
                  child: Text(
                    'Recommandé',
                    style: TextStyle(
                      fontSize: 10,
                      fontWeight: FontWeight.bold,
                      color: AppTheme.color.greenColor,
                    ),
                  ),
                ),
            ],
          ),
          const SizedBox(height: 12),
          Text(
            price,
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: isSelected ? AppTheme.color.primaryColor : Colors.black87,
            ),
          ),

          const SizedBox(height: 12),

          // Bouton de sélection
          ElevatedButton(
            onPressed: onSelect,
            style: ElevatedButton.styleFrom(
              backgroundColor:
                  isSelected ? AppTheme.color.primaryColor : Colors.grey[200],
              foregroundColor: isSelected ? Colors.white : Colors.black87,
              elevation: 0,
              minimumSize: const Size(double.infinity, 44),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(12),
              ),
            ),
            child: Text(
              isSelected ? 'Sélectionné' : 'Choisir cette option',
              style: TextStyle(fontWeight: FontWeight.w600, fontSize: 14),
            ),
          ),
        ],
      ),
    );
  }

  void _showOrderConfirmationDialog() {
    // Bloquer si une opération est en cours
    if (_isGlobalLoading) return;

    final int totalAmount = _totalPrice; // Utiliser le prix total déjà calculé

    // Extraire les informations du produit pour le résumé
    final String name = widget.product['name'] ?? 'Produit sans nom';
    final String description =
        'Obligation du souscripteur, délai de paiement et pénalité.';
    final String description2 = 'Votre cotisation n’est pas remboursable.';
    final String description3 =
        'Vous devez obligatoirement solder votre cotisation avant d’obtenir votre pack choisi en fin de campagne.  En cas de fluctuation de prix d’un article contenu dans un pack, le client doit supporter un changement du  cout de l’article si ce changement est connu sur le plan national. Un délai de collecte vous est exigé. Les clients qui n’ayant pas pu respecter le délai indiqué auront un délai  de rigueur d’une semaine. Passé ce délai, ils seront reconduits pour la campagne suivante avec une pénalité de';
    final String description4 = '35%';
    final String description5 = 'qui sera déduite de leur collecte.';
    final String description6 =
        'Les clients n’ayant pas pu soldé  leur pack dans un délais de un (01) an devront changer de carnet avec  une pénalité de -35%.';
    final String imageUrl = widget.product['imageUrl'] ?? '';
    final String livraison = widget.product['livraison'] ?? 'Produit sans nom';
    final String delai_livraison =
        widget.product['delai_livraison'] ?? 'Produit sans nom';
    final String garantie = widget.product['garantie'] ?? 'Produit sans nom';
    final String price = _formatPrice(totalAmount);
    // Paiement journalier uniquement

    // Afficher le dialogue de confirmation avec résumé du produit
    showDialog(
      context: context,
      barrierDismissible: false,
      builder:
          (context) => AlertDialog(
            title: Text(
              'Confirmer la commande',
              style: TextStyle(color: AppTheme.color.primaryColor),
            ),
            content: SingleChildScrollView(
              child: Column(
                mainAxisSize: MainAxisSize.min,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Image du produit
                  if (imageUrl.isNotEmpty)
                    Container(
                      height: 150,
                      width: double.infinity,
                      decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(12),
                        color: Colors.grey[100],
                      ),
                      child: ClipRRect(
                        borderRadius: BorderRadius.circular(12),
                        child: Image.network(
                          imageUrl,
                          fit: BoxFit.cover,
                          errorBuilder: (context, error, stackTrace) {
                            return Center(
                              child: Icon(
                                Icons.image_not_supported_outlined,
                                size: 40,
                                color: Colors.grey,
                              ),
                            );
                          },
                        ),
                      ),
                    ),

                  const SizedBox(height: 16),

                  // Nom du produit
                  Text(
                    name,
                    style: const TextStyle(
                      fontWeight: FontWeight.bold,
                      fontSize: 18,
                    ),
                  ),

                  const SizedBox(height: 8),

                  // Prix
                  Row(
                    children: [
                      Icon(
                        Icons.monetization_on,
                        size: 18,
                        color: AppTheme.color.primaryColor,
                      ),
                      const SizedBox(width: 8),
                      Text(
                        '$price FCFA',
                        style: TextStyle(
                          fontWeight: FontWeight.bold,
                          fontSize: 16,
                          color: AppTheme.color.primaryColor,
                        ),
                      ),
                    ],
                  ),

                  const SizedBox(height: 8),

                  // Modalité de paiement
                  Row(
                    children: [
                      Icon(
                        Icons.calendar_today,
                        size: 18,
                        color: AppTheme.color.orangeColor,
                      ),
                      const SizedBox(width: 8),
                      Expanded(
                        child: Text(
                          '${_formatPrice(_dailyPrice)} FCFA par jour pendant $_duration jours',
                          style: TextStyle(
                            fontSize: 14,
                            color: AppTheme.color.orangeColor,
                          ),
                        ),
                      ),
                    ],
                  ),

                  const SizedBox(height: 12),

                  // Informations de livraison
                  Container(
                    padding: const EdgeInsets.all(12),
                    decoration: BoxDecoration(
                      color: Colors.grey[50],
                      borderRadius: BorderRadius.circular(8),
                      border: Border.all(color: Colors.grey[200]!),
                    ),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          'Informations de livraison:',
                          style: TextStyle(
                            fontWeight: FontWeight.bold,
                            fontSize: 14,
                            color: AppTheme.color.textColor,
                          ),
                        ),
                        const SizedBox(height: 8),
                        Row(
                          children: [
                            Icon(
                              Icons.local_shipping_outlined,
                              size: 16,
                              color: AppTheme.color.brunGris,
                            ),
                            const SizedBox(width: 8),
                            Text(
                              livraison,
                              style: TextStyle(
                                fontSize: 14,
                                color: AppTheme.color.brunGris,
                              ),
                            ),
                          ],
                        ),
                        const SizedBox(height: 4),
                        Row(
                          children: [
                            Icon(
                              Icons.access_time,
                              size: 16,
                              color: AppTheme.color.brunGris,
                            ),
                            const SizedBox(width: 8),
                            Expanded(
                              child: Text(
                                delai_livraison,
                                style: TextStyle(
                                  fontSize: 14,
                                  color: AppTheme.color.brunGris,
                                ),
                                overflow: TextOverflow.ellipsis,
                              ),
                            ),
                          ],
                        ),
                      ],
                    ),
                  ),

                  const SizedBox(height: 16),

                  // Note
                  Text(
                    'En confirmant cette commande, vous acceptez l\'article important lu récemment.',
                    style: TextStyle(
                      fontSize: 12,
                      fontStyle: FontStyle.italic,
                      color: AppTheme.color.brunGris,
                    ),
                  ),
                ],
              ),
            ),
            actions: [
              TextButton(
                onPressed: () {
                  Navigator.of(context).pop();
                },
                child: Text(
                  'Annuler',
                  style: TextStyle(color: AppTheme.color.brunGris),
                ),
              ),
              ElevatedButton(
                onPressed: () {
                  Navigator.of(context).pop();
                  _showPaymentMethodDialog(widget.product);
                },
                style: ElevatedButton.styleFrom(
                  backgroundColor: AppTheme.color.primaryColor,
                  foregroundColor: Colors.white,
                ),
                child: const Text('Confirmer'),
              ),
            ],
          ),
    );
  }

  void _showCitySelectionDialog(int totalAmount) {
    final TextEditingController cityController = TextEditingController();
    final List<String> availableCities = [
      'Abidjan',
      'Bouaké',
      'Daloa',
      'Yamoussoukro',
      'Korhogo',
      'San-Pédro',
      'Man',
      'Gagnoa',
      'Divo',
      'Abengourou',
    ];
    String? selectedCity;

    showDialog(
      context: context,
      barrierDismissible: false,
      builder:
          (context) => StatefulBuilder(
            builder: (context, setState) {
              return AlertDialog(
                title: Text(
                  'Sélectionnez votre ville',
                  style: TextStyle(color: AppTheme.color.primaryColor),
                ),
                content: Column(
                  mainAxisSize: MainAxisSize.min,
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text(
                      'Pour finaliser votre commande, veuillez sélectionner votre ville de livraison :',
                      style: TextStyle(fontSize: 14),
                    ),
                    const SizedBox(height: 16),
                    Container(
                      padding: const EdgeInsets.symmetric(horizontal: 12),
                      decoration: BoxDecoration(
                        border: Border.all(color: Colors.grey[300]!),
                        borderRadius: BorderRadius.circular(12),
                      ),
                      child: DropdownButtonHideUnderline(
                        child: DropdownButton<String>(
                          isExpanded: true,
                          hint: const Text('Sélectionnez une ville'),
                          value: selectedCity,
                          items:
                              availableCities.map((String city) {
                                return DropdownMenuItem<String>(
                                  value: city,
                                  child: Text(city),
                                );
                              }).toList(),
                          onChanged: (String? newValue) {
                            setState(() {
                              selectedCity = newValue;
                            });
                          },
                        ),
                      ),
                    ),
                    const SizedBox(height: 16),
                    const Text(
                      'Ou entrez une autre ville :',
                      style: TextStyle(fontSize: 14),
                    ),
                    const SizedBox(height: 8),
                    TextField(
                      controller: cityController,
                      decoration: InputDecoration(
                        hintText: 'Entrez le nom de votre ville',
                        border: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(12),
                        ),
                        contentPadding: const EdgeInsets.symmetric(
                          horizontal: 12,
                          vertical: 8,
                        ),
                      ),
                    ),
                    const SizedBox(height: 16),
                    Row(
                      children: [
                        Icon(
                          Icons.info_outline,
                          size: 16,
                          color: AppTheme.color.brunGris,
                        ),
                        const SizedBox(width: 8),
                        Expanded(
                          child: Text(
                            'Cette information sera utilisée pour la livraison de votre commande.',
                            style: TextStyle(
                              fontSize: 12,
                              color: AppTheme.color.brunGris,
                              fontStyle: FontStyle.italic,
                            ),
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
                actions: [
                  TextButton(
                    onPressed: () {
                      Navigator.of(context).pop();
                    },
                    child: Text(
                      'Annuler',
                      style: TextStyle(color: AppTheme.color.brunGris),
                    ),
                  ),
                  ElevatedButton(
                    onPressed: () {
                      // Déterminer la ville sélectionnée (soit depuis la liste déroulante, soit depuis le champ texte)
                      final String city =
                          selectedCity ?? cityController.text.trim();

                      if (city.isEmpty) {
                        ScaffoldMessenger.of(context).showSnackBar(
                          const SnackBar(
                            content: Text(
                              'Veuillez sélectionner ou entrer une ville',
                            ),
                            backgroundColor: Colors.red,
                          ),
                        );
                        return;
                      }

                      Navigator.of(context).pop();
                      _showFinalOrderConfirmationDialog(
                        totalAmount,
                        false, // Toujours paiement journalier
                        city,
                      );

                      // Enregistrer la ville dans le profil utilisateur pour les futures commandes
                      // TODO: Implémenter la sauvegarde de la ville dans le profil utilisateur
                    },
                    style: ElevatedButton.styleFrom(
                      backgroundColor: AppTheme.color.primaryColor,
                      foregroundColor: Colors.white,
                    ),
                    child: const Text('Continuer'),
                  ),
                ],
              );
            },
          ),
    );
  }

  void _processOrder() async {
    // Bloquer si une opération est déjà en cours
    if (_isGlobalLoading) return;

    _startGlobalLoading('Traitement de votre commande...');

    try {
      // Récupérer l'ID du produit
      final String itemId = widget.product['id'].toString();

      // Passer la commande
      final result = await OrderService.placeOrder(itemId);

      // Arrêter le chargement global
      _stopGlobalLoading();

      if (result['success']) {
        // Afficher un message de succès
        showDialog(
          context: context,
          barrierDismissible: false,
          builder:
              (context) => AlertDialog(
                title: Text(
                  'Commande confirmée',
                  style: TextStyle(
                    color: AppTheme.color.primaryColor,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                content: Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Icon(
                      Icons.check_circle,
                      color: AppTheme.color.greenColor,
                      size: 64,
                    ),
                    const SizedBox(height: 16),
                    Text(
                      result['message'] ??
                          'Votre commande a été enregistrée avec succès !',
                      textAlign: TextAlign.center,
                    ),
                    const SizedBox(height: 8),
                    const Text(
                      'Vous pouvez suivre l\'état de votre commande dans la section "Mes commandes".',
                      textAlign: TextAlign.center,
                      style: TextStyle(fontSize: 12),
                    ),
                  ],
                ),
                actions: [
                  TextButton(
                    onPressed: () {
                      Navigator.pop(context);
                    },
                    child: const Text('Fermer'),
                  ),
                  ElevatedButton(
                    onPressed: () {
                      Navigator.pop(context);
                      Navigator.push(
                        context,
                        MaterialPageRoute(
                          builder: (context) => const MyOrdersScreen(),
                        ),
                      );
                    },
                    style: ElevatedButton.styleFrom(
                      backgroundColor: AppTheme.color.primaryColor,
                      foregroundColor: Colors.white,
                    ),
                    child: const Text('Voir mes commandes'),
                  ),
                ],
              ),
        );
      } else {
        // Afficher un message d'erreur
        showDialog(
          context: context,
          barrierDismissible: false,
          builder:
              (context) => AlertDialog(
                title: const Text(
                  'Erreur',
                  style: TextStyle(
                    color: Colors.red,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                content: Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    const Icon(
                      Icons.error_outline,
                      color: Colors.red,
                      size: 64,
                    ),
                    const SizedBox(height: 16),
                    Text(
                      result['message'] ??
                          'Une erreur est survenue lors du traitement de votre commande.',
                      textAlign: TextAlign.center,
                    ),
                  ],
                ),
                actions: [
                  ElevatedButton(
                    onPressed: () {
                      Navigator.pop(context);
                    },
                    style: ElevatedButton.styleFrom(
                      backgroundColor: AppTheme.color.primaryColor,
                      foregroundColor: Colors.white,
                    ),
                    child: const Text('OK'),
                  ),
                ],
              ),
        );
      }
    } catch (e) {
      // Arrêter le chargement global
      _stopGlobalLoading();

      // Afficher un message d'erreur
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('Erreur: $e'), backgroundColor: Colors.red),
      );
    }
  }

  void _showPaymentMethodDialog(Map<String, dynamic> item) async {
    // Bloquer si une opération est en cours
    if (_isGlobalLoading) return;

    // Récupérer le prix total et journalier
    final Map<String, dynamic>? user = await AuthService.getUserData();
    double totalPrice = 0.0;
    double dailyPrice = 0.0;

    if (item.containsKey('price') && item['price'] != null) {
      totalPrice = double.tryParse(item['price'].toString()) ?? 0.0;
    } else if (item.containsKey('dailyPrice') && item['dailyPrice'] != null) {
      totalPrice = double.tryParse(item['dailyPrice'].toString()) ?? 0.0;
    } else if (item.containsKey('price') && item['price'] != null) {
      totalPrice = double.tryParse(item['price'].toString()) ?? 0.0;
    }

    if (item.containsKey('dailyPrice') && item['dailyPrice'] != null) {
      dailyPrice = double.tryParse(item['dailyPrice'].toString()) ?? 0.0;
    }

    if (totalPrice <= 0 || dailyPrice <= 0) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Prix du produit invalide'),
          backgroundColor: Colors.red,
        ),
      );
      return;
    }

    // Charger la monnaie disponible
    double monnaieDisponible = 0.0;
    try {
      final walletData = await WalletService.getUserMonnaie();
      if (walletData['success']) {
        monnaieDisponible = (walletData['montant'] ?? 0).toDouble();
      }
    } catch (e) {
      print('Erreur lors du chargement de la monnaie: $e');
    }

    PaymentMethod selectedMethod = PaymentMethod.wave;
    double customAmount = dailyPrice; // Montant par défaut = prix journalier

    final TextEditingController amountController = TextEditingController();
    final TextEditingController phoneController = TextEditingController();
    amountController.text = customAmount.toInt().toString();
    phoneController.text = user?['telephone_client'] ?? '010101010101';

    showDialog(
      context: context,
      barrierDismissible: false,
      builder:
          (context) => StatefulBuilder(
            builder: (context, setState) {
              // Fonction pour valider le montant
              bool isValidAmount(double amount) {
                return amount >= dailyPrice &&
                    amount <= totalPrice &&
                    amount % 5 == 0;
              }

              return AlertDialog(
                title: Text(
                  'Paiement personnalisé',
                  style: TextStyle(
                    color: AppTheme.color.primaryColor,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                content: SingleChildScrollView(
                  child: Column(
                    mainAxisSize: MainAxisSize.min,
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      // Informations sur le produit
                      Container(
                        padding: const EdgeInsets.all(12),
                        decoration: BoxDecoration(
                          color: AppTheme.color.primaryColor.withOpacity(0.1),
                          borderRadius: BorderRadius.circular(8),
                          border: Border.all(
                            color: AppTheme.color.primaryColor.withOpacity(0.3),
                          ),
                        ),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              item['option_kit'] ??
                                  item['nom'] ??
                                  'Pack sans nom',
                              style: const TextStyle(
                                fontWeight: FontWeight.bold,
                                fontSize: 16,
                              ),
                            ),
                            const SizedBox(height: 8),
                            Text(
                              'Prix journalier: ${_formatPrice(dailyPrice.toInt())} FCFA',
                              style: TextStyle(
                                fontSize: 14,
                                color: AppTheme.color.primaryColor,
                                fontWeight: FontWeight.w600,
                              ),
                            ),
                            Text(
                              'Prix total: ${_formatPrice(totalPrice.toInt())} FCFA',
                              style: TextStyle(
                                fontSize: 14,
                                color: AppTheme.color.brunGris,
                              ),
                            ),
                          ],
                        ),
                      ),

                      const SizedBox(height: 16),

                      // Champ de saisie du montant
                      Text(
                        'Montant à payer (FCFA)',
                        style: TextStyle(
                          fontWeight: FontWeight.w600,
                          fontSize: 16,
                          color: AppTheme.color.textColor,
                        ),
                      ),
                      const SizedBox(height: 8),
                      TextField(
                        controller: amountController,
                        keyboardType: TextInputType.number,
                        decoration: InputDecoration(
                          hintText: 'Ex: ${_formatPrice(dailyPrice.toInt())}',
                          prefixIcon: Icon(
                            Icons.payments,
                            color: AppTheme.color.primaryColor,
                          ),
                          border: OutlineInputBorder(
                            borderRadius: BorderRadius.circular(8),
                          ),
                          focusedBorder: OutlineInputBorder(
                            borderRadius: BorderRadius.circular(8),
                            borderSide: BorderSide(
                              color: AppTheme.color.primaryColor,
                            ),
                          ),
                        ),
                        onChanged: (value) {
                          final amount = double.tryParse(value) ?? 0.0;
                          setState(() {
                            customAmount = amount;
                          });
                        },
                      ),

                      const SizedBox(height: 12),

                      // Validation du montant
                      if (amountController.text.isNotEmpty)
                        Container(
                          padding: const EdgeInsets.all(8),
                          decoration: BoxDecoration(
                            color:
                                isValidAmount(customAmount)
                                    ? Colors.green.withOpacity(0.1)
                                    : Colors.red.withOpacity(0.1),
                            borderRadius: BorderRadius.circular(6),
                            border: Border.all(
                              color:
                                  isValidAmount(customAmount)
                                      ? Colors.green.withOpacity(0.3)
                                      : Colors.red.withOpacity(0.3),
                            ),
                          ),
                          child: Text(
                            _getAmountValidationMessage(
                              customAmount,
                              dailyPrice,
                              totalPrice,
                            ),
                            style: TextStyle(
                              color:
                                  isValidAmount(customAmount)
                                      ? Colors.green
                                      : Colors.red,
                              fontSize: 12,
                              fontWeight: FontWeight.w500,
                            ),
                          ),
                        ),

                      const SizedBox(height: 16),

                      PaymentMethodSelector(
                        selectedMethod: selectedMethod,
                        onMethodChanged: (method) {
                          setState(() {
                            selectedMethod = method;
                          });
                        },
                        montantVerse: customAmount,
                        monnaieDisponible: monnaieDisponible,
                        enabled: true,
                      ),
                    ],
                  ),
                ),
                actions: [
                  TextButton(
                    onPressed: () {
                      Navigator.of(context).pop();
                    },
                    child: Text(
                      'Annuler',
                      style: TextStyle(color: AppTheme.color.brunGris),
                    ),
                  ),
                  ElevatedButton(
                    onPressed:
                        !_isGlobalLoading &&
                                isValidAmount(customAmount) &&
                                (!selectedMethod.usesCinetPay ||
                                    (phoneController.text.trim().isNotEmpty &&
                                        _isValidPhoneNumber(
                                          phoneController.text.trim(),
                                        )))
                            ? () {
                              _processPaymentAndOrder(
                                item,
                                selectedMethod,
                                customAmount,
                                phoneNumber: phoneController.text.trim(),
                              );
                              Navigator.of(context).pop();
                            }
                            : null,
                    style: ElevatedButton.styleFrom(
                      backgroundColor: AppTheme.color.primaryColor,
                      foregroundColor: Colors.white,
                    ),
                    child: const Text('Payer maintenant'),
                  ),
                ],
              );
            },
          ),
    );
  }

  void _processPaymentAndOrder(
    Map<String, dynamic> product,
    PaymentMethod paymentMethod,
    double totalPrice, {
    String phoneNumber = '',
  }) async {
    // Bloquer si une opération est déjà en cours
    if (_isGlobalLoading) return;

    final String productId = product['id']?.toString() ?? '';

    if (productId.isEmpty) {
      _showSafeSnackBar(
        'Impossible d\'identifier le produit. Veuillez réessayer.',
      );
      return;
    }

    // Activer le chargement global
    _startGlobalLoading('Initialisation du paiement...');

    try {
      // Traitement selon la méthode de paiement sélectionnée
      if (paymentMethod.usesCinetPay) {
        await _processCinetPayPayment(product, totalPrice, phoneNumber);
      } else if (paymentMethod == PaymentMethod.wave) {
        await _processWavePayment(product, totalPrice);
      } else if (paymentMethod == PaymentMethod.monnaie) {
        await _processMonnaiePayment(product, totalPrice);
      }
    } catch (e) {
      // Arrêter le chargement global
      _stopGlobalLoading();

      _showSafeSnackBar('Erreur: $e');
    }
  }

  Widget _buildPaymentMethodOption({
    required PaymentMethod method,
    required String title,
    required IconData icon,
    required Color color,
    required PaymentMethod selectedMethod,
    required Function(PaymentMethod) onChanged,
  }) {
    final bool isSelected = selectedMethod == method;

    return Container(
      decoration: BoxDecoration(
        border: Border.all(
          color:
              isSelected ? AppTheme.color.primaryColor : Colors.grey.shade300,
          width: isSelected ? 2 : 1,
        ),
        borderRadius: BorderRadius.circular(8),
        color:
            isSelected
                ? AppTheme.color.primaryColor.withValues(alpha: 0.05)
                : Colors.white,
      ),
      child: RadioListTile<PaymentMethod>(
        value: method,
        groupValue: selectedMethod,
        onChanged: (PaymentMethod? value) {
          if (value != null) onChanged(value);
        },
        title: Row(
          children: [
            Container(
              padding: const EdgeInsets.all(8),
              decoration: BoxDecoration(
                color: color.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(6),
              ),
              child: Icon(icon, color: color, size: 20),
            ),
            const SizedBox(width: 12),
            Text(
              title,
              style: TextStyle(
                fontWeight: isSelected ? FontWeight.w600 : FontWeight.normal,
                color:
                    isSelected ? AppTheme.color.primaryColor : Colors.black87,
              ),
            ),
          ],
        ),
        activeColor: AppTheme.color.primaryColor,
      ),
    );
  }

  /// Active le chargement global et bloque toutes les actions
  void _startGlobalLoading(String message) {
    if (!mounted) return;

    setState(() {
      _isGlobalLoading = true;
      _isProcessingOperation = true;
      _currentProcessingMessage = message;
    });
  }

  /// Désactive le chargement global et débloque les actions
  void _stopGlobalLoading() {
    if (!mounted) return;

    setState(() {
      _isGlobalLoading = false;
      _isProcessingOperation = false;
      _currentProcessingMessage = '';
    });
  }

  /// Met à jour le message de chargement global
  void _updateGlobalLoadingMessage(String message) {
    if (!mounted || !_isGlobalLoading) return;

    setState(() {
      _currentProcessingMessage = message;
    });
  }

  void _showProcessingDialog(String message) {
    if (_isProcessingOperation || !mounted)
      return; // Éviter les doublons et vérifier mounted

    _startGlobalLoading(message);

    showDialog(
      context: context,
      barrierDismissible: false,
      barrierColor: Colors.black.withOpacity(0.6),
      builder: (BuildContext context) {
        return WillPopScope(
          onWillPop:
              () async => false, // Empêcher la fermeture avec le bouton retour
          child: Dialog(
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(20),
            ),
            elevation: 0,
            backgroundColor: Colors.transparent,
            child: Container(
              padding: const EdgeInsets.all(24),
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(20),
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withOpacity(0.1),
                    blurRadius: 20,
                    offset: const Offset(0, 10),
                  ),
                ],
              ),
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Container(
                    padding: const EdgeInsets.all(16),
                    decoration: BoxDecoration(
                      color: AppTheme.color.primaryColor.withOpacity(0.1),
                      shape: BoxShape.circle,
                    ),
                    child: CircularProgressIndicator(
                      valueColor: AlwaysStoppedAnimation<Color>(
                        AppTheme.color.primaryColor,
                      ),
                      strokeWidth: 3,
                    ),
                  ),
                  const SizedBox(height: 20),
                  Text(
                    message,
                    style: TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.w500,
                      color: AppTheme.color.textColor,
                    ),
                    textAlign: TextAlign.center,
                  ),
                  const SizedBox(height: 8),
                  Text(
                    'Veuillez patienter...',
                    style: TextStyle(
                      fontSize: 14,
                      color: AppTheme.color.brunGris,
                    ),
                    textAlign: TextAlign.center,
                  ),
                ],
              ),
            ),
          ),
        );
      },
    );
  }

  /// Ferme le modal de chargement
  void _hideProcessingDialog() {
    if (_isProcessingOperation && mounted) {
      _stopGlobalLoading();
      try {
        Navigator.of(context).pop();
      } catch (e) {
        print('Erreur lors de la fermeture du modal: $e');
      }
    }
  }

  /// Affiche un message d'erreur de manière sécurisée
  void _showSafeSnackBar(String message, {Color? backgroundColor}) {
    if (mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(message),
          backgroundColor: backgroundColor ?? Colors.red,
        ),
      );
    }
  }

  /// Construit l'overlay de chargement global qui bloque toute l'interface
  Widget _buildGlobalLoadingOverlay() {
    return Container(
      color: Colors.black.withOpacity(0.7),
      child: Center(
        child: Container(
          padding: const EdgeInsets.all(32),
          margin: const EdgeInsets.symmetric(horizontal: 40),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(20),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withOpacity(0.2),
                blurRadius: 20,
                offset: const Offset(0, 10),
              ),
            ],
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Container(
                padding: const EdgeInsets.all(20),
                decoration: BoxDecoration(
                  color: AppTheme.color.primaryColor.withOpacity(0.1),
                  shape: BoxShape.circle,
                ),
                child: CircularProgressIndicator(
                  valueColor: AlwaysStoppedAnimation<Color>(
                    AppTheme.color.primaryColor,
                  ),
                  strokeWidth: 4,
                ),
              ),
              const SizedBox(height: 24),
              Text(
                _currentProcessingMessage,
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.w600,
                  color: AppTheme.color.textColor,
                ),
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 12),
              Text(
                'Veuillez patienter et ne pas fermer l\'application...',
                style: TextStyle(fontSize: 14, color: AppTheme.color.brunGris),
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 16),
              Container(
                padding: const EdgeInsets.symmetric(
                  horizontal: 16,
                  vertical: 8,
                ),
                decoration: BoxDecoration(
                  color: Colors.orange.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(12),
                  border: Border.all(
                    color: Colors.orange.withOpacity(0.3),
                    width: 1,
                  ),
                ),
                child: Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Icon(Icons.block, color: Colors.orange, size: 16),
                    const SizedBox(width: 8),
                    Text(
                      'Actions bloquées',
                      style: TextStyle(
                        fontSize: 12,
                        fontWeight: FontWeight.w500,
                        color: Colors.orange.shade700,
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Future<void> _processCinetPayPayment(
    Map<String, dynamic> product,
    double totalPrice,
    String phoneNumber,
  ) async {
    // Vérifier si le widget est encore monté et qu'aucune opération n'est en cours
    if (!mounted) return;

    try {
      final String productId = product['id']?.toString() ?? '';
      final String productName = product['name'] ?? 'Produit';
      final user = await AuthService.getUserData();

      // Stocker les informations de transaction en attente pour CinetPay
      await _storePendingTransaction(totalPrice, 0.0, productId);

      // Vérifier à nouveau après l'opération async
      if (!mounted) {
        _stopGlobalLoading();
        return;
      }

      // Mettre à jour le message de traitement
      _updateGlobalLoadingMessage('Initialisation du paiement CinetPay...');

      // Vérifier le contexte avant l'appel async
      if (!mounted) {
        _stopGlobalLoading();
        return;
      }

      // Initialiser le paiement CinetPay
      final initResult = await CinetPayService.initializePayment(
        context: context,
        amount: totalPrice,
        description: 'Commande $productName',
        customerName: user?['nom_client'] ?? 'Client Callitris',
        customerEmail: user?['email'] ?? '<EMAIL>',
        customerPhone: user?['telephone_client'] ?? '0101010101',
        metadata: {
          'type': 'product_order',
          'productId': productId,
          'productName': productName,
          'isCarnet': false,
        },
      );

      if (!initResult['success']) {
        _stopGlobalLoading();
        await _clearPendingTransaction();
        _showSafeSnackBar(initResult['message'] ?? 'Erreur d\'initialisation');
        return;
      }

      // Mettre à jour le message de traitement
      _updateGlobalLoadingMessage('Traitement du paiement...');

      // Vérifier le contexte avant le deuxième appel async
      if (!mounted) {
        _stopGlobalLoading();
        return;
      }

      // Lancer le processus de paiement
      final paymentResult = await CinetPayService.processPayment(
        context: context,
        amount: totalPrice,
        transaction: initResult['transaction'],
        customerPhone: phoneNumber.isNotEmpty ? phoneNumber : '0101010101',
      );

      // Arrêter le chargement global
      _stopGlobalLoading();

      if (paymentResult['success']) {
        // Vérifier le statut de la transaction
        final status = paymentResult['status']?.toString().toUpperCase();
        if (status == 'ACCEPTED' || status == 'SUCCEEDED') {
          await _processSuccessfulPayment();
        } else {
          await _clearPendingTransaction();
          _showOrderError('Paiement non confirmé. Statut: $status');
        }
      } else {
        await _clearPendingTransaction();
        _showOrderError(
          paymentResult['message'] ?? 'Erreur lors du paiement CinetPay',
        );
      }
    } catch (e) {
      _stopGlobalLoading();
      await _clearPendingTransaction();
      print('Erreur CinetPay: $e');
      _showOrderError('Erreur lors du paiement CinetPay: $e');
    }
  }

  Future<void> _processWavePayment(
    Map<String, dynamic> product,
    double totalPrice,
  ) async {
    // Vérifier si le widget est encore monté et qu'aucune opération n'est en cours
    if (!mounted) return;

    try {
      final String productId = product['id']?.toString() ?? '';

      // Stocker les informations de transaction en attente
      await _storePendingTransaction(totalPrice, 0.0, productId);

      // Vérifier à nouveau après l'opération async
      if (!mounted) {
        _stopGlobalLoading();
        return;
      }

      // Mettre à jour le message de traitement
      _updateGlobalLoadingMessage('Initialisation du paiement Wave...');

      // Vérifier le contexte avant l'appel async
      if (!mounted) {
        _stopGlobalLoading();
        return;
      }

      // Initialiser le paiement Wave
      final result = await OrderService.addVersementWithWave(
        context: context,
        commandeId: '', // Pas encore de commande créée
        montant: totalPrice,
        monnaie: 0.0,
        itemId: productId,
      );

      // Arrêter le chargement global
      _stopGlobalLoading();

      if (result['success']) {
        if (result['paymentId'] != null) {
          await _updatePendingTransactionCode(result['paymentId']);
        }

        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(
                result['message'] ??
                    'Paiement Wave initié. Vérification en cours...',
              ),
              backgroundColor: Colors.orange,
            ),
          );
        }

        // Démarrer la vérification périodique du statut
        _startPeriodicStatusCheck();
      } else {
        // Nettoyer les données en attente en cas d'échec
        await _clearPendingTransaction();

        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(
                result['message'] ?? 'Erreur lors du paiement Wave',
              ),
              backgroundColor: Colors.red,
            ),
          );
        }
      }
    } catch (e) {
      _stopGlobalLoading();
      await _clearPendingTransaction();
      print('Erreur Wave: $e');
      _showOrderError('Erreur lors du paiement Wave: $e');
    }
  }

  Future<void> _processMonnaiePayment(
    Map<String, dynamic> product,
    double totalPrice,
  ) async {
    // Vérifier si le widget est encore monté et qu'aucune opération n'est en cours
    if (!mounted) return;

    try {
      final String productId = product['id']?.toString() ?? '';

      // Vérifier à nouveau après l'opération async
      if (!mounted) {
        _stopGlobalLoading();
        return;
      }

      // Mettre à jour le message de traitement
      _updateGlobalLoadingMessage('Traitement du paiement avec la monnaie...');

      // Effectuer le paiement avec la monnaie du portefeuille
      // Pour cela, on crée d'abord la commande puis on effectue le versement
      final orderResult = await OrderService.placeOrder(
        productId,
        isCarnet: false,
      );

      // Arrêter le chargement global
      _stopGlobalLoading();

      if (orderResult['success']) {
        // Commande créée avec succès, considérer le paiement comme réussi
        _showOrderSuccess(
          orderResult['message'] ?? 'Commande enregistrée avec succès !',
        );
      } else {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(
                orderResult['message'] ?? 'Erreur lors de la commande',
              ),
              backgroundColor: Colors.red,
            ),
          );
        }
      }
    } catch (e) {
      _stopGlobalLoading();
      print('Erreur paiement monnaie: $e');
      _showOrderError('Erreur lors du paiement avec la monnaie: $e');
    }
  }

  void _showOrderSuccess(String message) {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (BuildContext context) {
        return AlertDialog(
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(16),
          ),
          title: Row(
            children: [
              Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: Colors.green.shade100,
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Icon(
                  Icons.check_circle,
                  color: Colors.green.shade700,
                  size: 24,
                ),
              ),
              const SizedBox(width: 12),
              const Expanded(
                child: Text(
                  'Commande confirmée',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.w600,
                    color: Colors.black87,
                  ),
                ),
              ),
            ],
          ),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Text(
                message,
                style: const TextStyle(fontSize: 16),
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 16),
              const Text(
                'Vous pouvez suivre l\'état de votre commande dans la section "Mes commandes".',
                style: TextStyle(fontSize: 14, color: Colors.grey),
                textAlign: TextAlign.center,
              ),
            ],
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: const Text('Fermer'),
            ),
            ElevatedButton(
              onPressed: () {
                Navigator.of(context).pop();
                Navigator.push(
                  context,
                  MaterialPageRoute(
                    builder: (context) => const MyOrdersScreen(),
                  ),
                );
              },
              style: ElevatedButton.styleFrom(
                backgroundColor: AppTheme.color.primaryColor,
                foregroundColor: Colors.white,
              ),
              child: const Text('Voir mes commandes'),
            ),
          ],
        );
      },
    );
  }

  void _showOrderError(String message) {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (BuildContext context) {
        return AlertDialog(
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(16),
          ),
          title: Row(
            children: [
              Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: Colors.red.shade100,
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Icon(
                  Icons.error_outline,
                  color: Colors.red.shade700,
                  size: 24,
                ),
              ),
              const SizedBox(width: 12),
              const Expanded(
                child: Text(
                  'Erreur',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.w600,
                    color: Colors.black87,
                  ),
                ),
              ),
            ],
          ),
          content: Text(
            message,
            style: const TextStyle(fontSize: 16),
            textAlign: TextAlign.center,
          ),
          actions: [
            ElevatedButton(
              onPressed: () => Navigator.of(context).pop(),
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.red.shade600,
                foregroundColor: Colors.white,
              ),
              child: const Text('OK'),
            ),
          ],
        );
      },
    );
  }

  Timer? _statusCheckTimer;

  void _startPeriodicStatusCheck() {
    _statusCheckTimer?.cancel();
    _statusCheckTimer = Timer.periodic(const Duration(seconds: 10), (timer) {
      if (_pendingTransactionCode != null) {
        _checkWaveTransactionStatus(_pendingTransactionCode!);
      } else {
        timer.cancel();
      }
    });

    // Arrêter automatiquement après 5 minutes
    Timer(const Duration(minutes: 5), () {
      _statusCheckTimer?.cancel();
    });
  }

  String _formatPrice(int price) {
    return price.toString().replaceAllMapped(
      RegExp(r'(\d{1,3})(?=(\d{3})+(?!\d))'),
      (Match m) => '${m[1]} ',
    );
  }

  Widget _buildModernAppBar() {
    final String imageUrl = widget.product['imageUrl'];
    final bool isNetworkImage =
        imageUrl.startsWith('http://') ||
        imageUrl.startsWith('https://') ||
        imageUrl.contains('public/img') ||
        imageUrl.startsWith('/');

    return SliverAppBar(
      expandedHeight: 350.0,
      backgroundColor: Colors.white,
      elevation: 0,
      pinned: true,
      stretch: true,
      leading: Container(
        margin: const EdgeInsets.all(8),
        decoration: BoxDecoration(
          color: Colors.white.withValues(alpha: 0.95),
          shape: BoxShape.circle,
          boxShadow: [
            BoxShadow(
              color: Colors.black.withValues(alpha: 0.1),
              blurRadius: 10,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: IconButton(
          icon: const Icon(Icons.arrow_back_ios_rounded, color: Colors.black87),
          onPressed:
              _isGlobalLoading ? null : () => Navigator.of(context).pop(),
        ),
      ),
      actions: [
        Container(
          margin: const EdgeInsets.all(8),
          decoration: BoxDecoration(
            color: Colors.white.withValues(alpha: 0.95),
            shape: BoxShape.circle,
            boxShadow: [
              BoxShadow(
                color: Colors.black.withValues(alpha: 0.1),
                blurRadius: 10,
                offset: const Offset(0, 2),
              ),
            ],
          ),
          child: NavigationMenuButton(),
        ),
        Container(
          margin: const EdgeInsets.all(8),
          decoration: BoxDecoration(
            color: Colors.white.withValues(alpha: 0.95),
            shape: BoxShape.circle,
            boxShadow: [
              BoxShadow(
                color: Colors.black.withValues(alpha: 0.1),
                blurRadius: 10,
                offset: const Offset(0, 2),
              ),
            ],
          ),
          child: IconButton(
            icon: Icon(
              Icons.favorite_border_rounded,
              color: Colors.red.shade400,
            ),
            onPressed:
                _isGlobalLoading
                    ? null
                    : () {
                      ScaffoldMessenger.of(context).showSnackBar(
                        SnackBar(
                          content: const Text('Ajouté aux favoris'),
                          backgroundColor: Colors.red.shade400,
                          behavior: SnackBarBehavior.floating,
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(10),
                          ),
                        ),
                      );
                    },
            tooltip: 'Ajouter aux favoris',
          ),
        ),
        Container(
          margin: const EdgeInsets.all(8),
          decoration: BoxDecoration(
            color: Colors.white.withValues(alpha: 0.95),
            shape: BoxShape.circle,
            boxShadow: [
              BoxShadow(
                color: Colors.black.withValues(alpha: 0.1),
                blurRadius: 10,
                offset: const Offset(0, 2),
              ),
            ],
          ),
          child: IconButton(
            icon: const Icon(Icons.share_rounded, color: Colors.black87),
            onPressed:
                _isGlobalLoading
                    ? null
                    : () {
                      ScaffoldMessenger.of(context).showSnackBar(
                        SnackBar(
                          content: const Text('Partage du produit...'),
                          backgroundColor: AppTheme.color.primaryColor,
                          behavior: SnackBarBehavior.floating,
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(10),
                          ),
                        ),
                      );
                    },
            tooltip: 'Partager',
          ),
        ),
      ],
      flexibleSpace: FlexibleSpaceBar(
        background: GestureDetector(
          onTap:
              _isGlobalLoading
                  ? null
                  : () {
                    Navigator.of(context).push(
                      PageRouteBuilder(
                        pageBuilder:
                            (context, animation, secondaryAnimation) =>
                                FullscreenImageViewer(
                                  imageUrl: imageUrl,
                                  heroTag: 'product-${widget.product['id']}',
                                  isNetworkImage: isNetworkImage,
                                ),
                        transitionsBuilder: (
                          context,
                          animation,
                          secondaryAnimation,
                          child,
                        ) {
                          return FadeTransition(
                            opacity: animation,
                            child: ScaleTransition(
                              scale: Tween<double>(
                                begin: 0.8,
                                end: 1.0,
                              ).animate(
                                CurvedAnimation(
                                  parent: animation,
                                  curve: Curves.easeOutBack,
                                ),
                              ),
                              child: child,
                            ),
                          );
                        },
                        transitionDuration: const Duration(milliseconds: 400),
                        opaque: false,
                      ),
                    );
                  },
          child: Stack(
            fit: StackFit.expand,
            children: [
              Hero(
                tag: 'product-${widget.product['id']}',
                child: Container(
                  decoration: BoxDecoration(
                    gradient: LinearGradient(
                      begin: Alignment.topCenter,
                      end: Alignment.bottomCenter,
                      colors: [
                        Colors.black.withValues(alpha: 0.1),
                        Colors.transparent,
                        Colors.black.withValues(alpha: 0.3),
                      ],
                    ),
                  ),
                  child:
                      isNetworkImage
                          ? Image.network(
                            imageUrl,
                            fit: BoxFit.cover,
                            errorBuilder: (context, error, stackTrace) {
                              return Container(
                                decoration: BoxDecoration(
                                  gradient: LinearGradient(
                                    begin: Alignment.topLeft,
                                    end: Alignment.bottomRight,
                                    colors: [
                                      AppTheme.color.primaryColor.withValues(
                                        alpha: 0.3,
                                      ),
                                      AppTheme.color.primaryColor.withValues(
                                        alpha: 0.1,
                                      ),
                                    ],
                                  ),
                                ),
                                child: Center(
                                  child: Column(
                                    mainAxisAlignment: MainAxisAlignment.center,
                                    children: [
                                      Icon(
                                        Icons.image_not_supported_rounded,
                                        color: Colors.white,
                                        size: 80,
                                      ),
                                      const SizedBox(height: 16),
                                      Text(
                                        'Image non disponible',
                                        style: TextStyle(
                                          color: Colors.white,
                                          fontSize: 16,
                                          fontWeight: FontWeight.w500,
                                        ),
                                      ),
                                    ],
                                  ),
                                ),
                              );
                            },
                          )
                          : Image.asset(
                            imageUrl,
                            fit: BoxFit.cover,
                            errorBuilder: (context, error, stackTrace) {
                              return Container(
                                decoration: BoxDecoration(
                                  gradient: LinearGradient(
                                    begin: Alignment.topLeft,
                                    end: Alignment.bottomRight,
                                    colors: [
                                      AppTheme.color.primaryColor.withValues(
                                        alpha: 0.3,
                                      ),
                                      AppTheme.color.primaryColor.withValues(
                                        alpha: 0.1,
                                      ),
                                    ],
                                  ),
                                ),
                                child: Center(
                                  child: Icon(
                                    Icons.image_not_supported_rounded,
                                    color: Colors.white,
                                    size: 80,
                                  ),
                                ),
                              );
                            },
                          ),
                ),
              ),
              // Indicateur de zoom avec design moderne
              Positioned(
                bottom: 20,
                right: 20,
                child: Container(
                  padding: const EdgeInsets.symmetric(
                    horizontal: 12,
                    vertical: 8,
                  ),
                  decoration: BoxDecoration(
                    color: Colors.black.withValues(alpha: 0.7),
                    borderRadius: BorderRadius.circular(25),
                    border: Border.all(
                      color: Colors.white.withValues(alpha: 0.3),
                      width: 1,
                    ),
                  ),
                  child: Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Icon(
                        Icons.zoom_out_map_rounded,
                        color: Colors.white,
                        size: 18,
                      ),
                      const SizedBox(width: 6),
                      Text(
                        'Agrandir',
                        style: TextStyle(
                          color: Colors.white,
                          fontSize: 12,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildModernProductInfo() {
    final String categoryName = widget.product['category'] ?? 'Pack';
    final List<dynamic>? features =
        widget.product['features'] as List<dynamic>?;

    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: const BorderRadius.only(
          topLeft: Radius.circular(30),
          topRight: Radius.circular(30),
        ),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.1),
            blurRadius: 20,
            offset: const Offset(0, -5),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Handle bar pour indiquer que c'est scrollable
          Center(
            child: Container(
              margin: const EdgeInsets.only(top: 12),
              width: 40,
              height: 4,
              decoration: BoxDecoration(
                color: Colors.grey.shade300,
                borderRadius: BorderRadius.circular(2),
              ),
            ),
          ),

          Padding(
            padding: const EdgeInsets.all(24),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Badge de catégorie moderne
                Container(
                  padding: const EdgeInsets.symmetric(
                    horizontal: 12,
                    vertical: 6,
                  ),
                  decoration: BoxDecoration(
                    gradient: LinearGradient(
                      colors: [
                        AppTheme.color.primaryColor.withValues(alpha: 0.1),
                        AppTheme.color.primaryColor.withValues(alpha: 0.05),
                      ],
                    ),
                    borderRadius: BorderRadius.circular(20),
                  ),
                  child: Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Icon(
                        Icons.inventory_2_rounded,
                        size: 16,
                        color: AppTheme.color.primaryColor,
                      ),
                      const SizedBox(width: 6),
                      Text(
                        categoryName,
                        style: TextStyle(
                          fontSize: 12,
                          fontWeight: FontWeight.w600,
                          color: AppTheme.color.primaryColor,
                        ),
                      ),
                    ],
                  ),
                ),
                const SizedBox(height: 16),

                // Nom du produit avec style moderne
                Text(
                  widget.product['name'] ?? 'Produit sans nom',
                  style: TextStyle(
                    fontSize: 28,
                    fontWeight: FontWeight.bold,
                    color: AppTheme.color.textColor,
                    letterSpacing: -0.5,
                    height: 1.2,
                  ),
                ),
                const SizedBox(height: 20),

                // Section prix moderne
                _buildModernPriceSection(),
                const SizedBox(height: 24),

                // Information sur le paiement journalier
                _buildPaymentInfoSection(),
                const SizedBox(height: 24),

                // Description avec design moderne
                _buildModernDescriptionSection(),
                const SizedBox(height: 24),

                // Caractéristiques si disponibles
                if (features != null && features.isNotEmpty) ...[
                  _buildModernFeaturesSection(features),
                  const SizedBox(height: 24),
                ],

                // Informations de livraison modernes
                _buildModernDeliverySection(),
                const SizedBox(height: 100), // Espace pour le bottom bar
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildModernPriceSection() {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            AppTheme.color.primaryColor.withValues(alpha: 0.05),
            AppTheme.color.primaryColor.withValues(alpha: 0.02),
          ],
        ),
        borderRadius: BorderRadius.circular(20),
        border: Border.all(
          color: AppTheme.color.primaryColor.withValues(alpha: 0.1),
          width: 1,
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                padding: const EdgeInsets.all(10),
                decoration: BoxDecoration(
                  color: AppTheme.color.primaryColor.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Icon(
                  Icons.monetization_on_rounded,
                  color: AppTheme.color.primaryColor,
                  size: 24,
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Prix',
                      style: TextStyle(
                        fontSize: 14,
                        color: Colors.grey.shade600,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                    const SizedBox(height: 4),
                    Text(
                      '${_formatPrice(_totalPrice)} FCFA',
                      style: TextStyle(
                        fontSize: 24,
                        fontWeight: FontWeight.bold,
                        color: AppTheme.color.primaryColor,
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          Container(
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: AppTheme.color.orangeColor.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(12),
            ),
            child: Row(
              children: [
                Icon(
                  Icons.schedule_rounded,
                  color: AppTheme.color.orangeColor,
                  size: 20,
                ),
                const SizedBox(width: 8),
                Text(
                  '${_formatPrice(_dailyPrice)} FCFA/jour × $_duration jours',
                  style: TextStyle(
                    fontSize: 14,
                    fontWeight: FontWeight.w600,
                    color: AppTheme.color.orangeColor,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildPaymentInfoSection() {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            AppTheme.color.orangeColor.withValues(alpha: 0.1),
            AppTheme.color.orangeColor.withValues(alpha: 0.05),
          ],
        ),
        borderRadius: BorderRadius.circular(20),
        border: Border.all(
          color: AppTheme.color.orangeColor.withValues(alpha: 0.2),
          width: 1,
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                padding: const EdgeInsets.all(10),
                decoration: BoxDecoration(
                  color: AppTheme.color.orangeColor.withValues(alpha: 0.2),
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Icon(
                  Icons.schedule_rounded,
                  color: AppTheme.color.orangeColor,
                  size: 24,
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Paiement journalier',
                      style: TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                        color: AppTheme.color.textColor,
                      ),
                    ),
                    Text(
                      'Payez jour par jour selon votre utilisation',
                      style: TextStyle(
                        fontSize: 13,
                        color: Colors.grey.shade600,
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),

          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: Colors.white.withValues(alpha: 0.8),
              borderRadius: BorderRadius.circular(16),
            ),
            child: Row(
              children: [
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'Tarif journalier',
                        style: TextStyle(
                          fontSize: 12,
                          color: Colors.grey.shade600,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                      const SizedBox(height: 4),
                      Text(
                        '${_formatPrice(_dailyPrice)} FCFA',
                        style: TextStyle(
                          fontSize: 20,
                          fontWeight: FontWeight.bold,
                          color: AppTheme.color.orangeColor,
                        ),
                      ),
                    ],
                  ),
                ),
                Container(width: 1, height: 40, color: Colors.grey.shade300),
                const SizedBox(width: 16),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'Durée',
                        style: TextStyle(
                          fontSize: 12,
                          color: Colors.grey.shade600,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                      const SizedBox(height: 4),
                      Text(
                        '$_duration jours',
                        style: TextStyle(
                          fontSize: 20,
                          fontWeight: FontWeight.bold,
                          color: AppTheme.color.primaryColor,
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildModernDescriptionSection() {
    // Define description variables
    final String description =
        'Obligation du souscripteur, délai de paiement et pénalité.';
    final String description2 = 'Votre cotisation n\'est pas remboursable.';
    final String description3 =
        'Vous devez obligatoirement solder votre cotisation avant d\'obtenir votre pack choisi en fin de campagne.  En cas de fluctuation de prix d\'un article contenu dans un pack, le client doit supporter un changement du  cout de l\'article si ce changement est connu sur le plan national. Un délai de collecte vous est exigé. Les clients qui n\'ayant pas pu respecter le délai indiqué auront un délai  de rigueur d\'une semaine. Passé ce délai, ils seront reconduits pour la campagne suivante avec une pénalité de';
    final String description4 = '35%';
    final String description5 = 'qui sera déduite de leur collecte.';
    final String description6 =
        'Les clients n\'ayant pas pu soldé  leur pack dans un délais de un (01) an devront changer de carnet avec  une pénalité de -35%.';

    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(20),
        border: Border.all(color: Colors.grey.shade200, width: 1),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                Icons.description_rounded,
                color: AppTheme.color.primaryColor,
                size: 24,
              ),
              const SizedBox(width: 8),
              Text(
                'Article important',
                style: TextStyle(
                  fontSize: 19,
                  fontWeight: FontWeight.bold,
                  color: AppTheme.color.redColor,
                ),
              ),
            ],
          ),
          const SizedBox(height: 12),
          Text(
            description,
            style: TextStyle(
              fontSize: 16,
              color: AppTheme.color.textColor,
              height: 1.5,
            ),
          ),
          Text(
            description2,
            style: TextStyle(
              fontSize: 18,
              color: AppTheme.color.redColor,
              height: 1.5,
            ),
          ),
          RichText(
            maxLines: 20,
            text: TextSpan(
              style: TextStyle(
                fontSize: 16,
                color: AppTheme.color.textColor,
                height: 1.5,
              ),
              children: [
                TextSpan(text: description3 + ' '),
                TextSpan(
                  text: description4 + ' ',
                  style: TextStyle(color: AppTheme.color.redColor),
                ),
                TextSpan(text: description5),
              ],
            ),
          ),
          Text(
            description6,
            style: TextStyle(
              fontSize: 18,
              color: AppTheme.color.redColor,
              height: 1.5,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildModernFeaturesSection(List<dynamic> features) {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(20),
        border: Border.all(color: Colors.grey.shade200, width: 1),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                Icons.star_rounded,
                color: AppTheme.color.orangeColor,
                size: 24,
              ),
              const SizedBox(width: 8),
              Text(
                'Caractéristiques',
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                  color: AppTheme.color.textColor,
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          ...features.map((feature) {
            return Container(
              margin: const EdgeInsets.only(bottom: 12),
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: AppTheme.color.greenColor.withValues(alpha: 0.05),
                borderRadius: BorderRadius.circular(12),
              ),
              child: Row(
                children: [
                  Container(
                    padding: const EdgeInsets.all(4),
                    decoration: BoxDecoration(
                      color: AppTheme.color.greenColor.withValues(alpha: 0.2),
                      shape: BoxShape.circle,
                    ),
                    child: Icon(
                      Icons.check_rounded,
                      color: AppTheme.color.greenColor,
                      size: 16,
                    ),
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: Text(
                      feature.toString(),
                      style: TextStyle(
                        fontSize: 14,
                        color: AppTheme.color.textColor,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ),
                ],
              ),
            );
          }).toList(),
        ],
      ),
    );
  }

  Widget _buildModernDeliverySection() {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(20),
        border: Border.all(color: Colors.grey.shade200, width: 1),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                Icons.local_shipping_rounded,
                color: AppTheme.color.orangeColor,
                size: 24,
              ),
              const SizedBox(width: 8),
              Text(
                'Livraison & Garantie',
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                  color: AppTheme.color.textColor,
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),

          _buildModernInfoItem(
            icon: Icons.local_shipping_outlined,
            title: 'Livraison',
            value: livraison,
            color: AppTheme.color.primaryColor,
          ),
          const SizedBox(height: 12),

          _buildModernInfoItem(
            icon: Icons.schedule_rounded,
            title: 'Délai de livraison',
            value: delai_livraison,
            color: AppTheme.color.orangeColor,
          ),
          const SizedBox(height: 12),

          _buildModernInfoItem(
            icon: Icons.verified_user_rounded,
            title: 'Garantie',
            value: garantie,
            color: AppTheme.color.greenColor,
          ),
        ],
      ),
    );
  }

  Widget _buildModernInfoItem({
    required IconData icon,
    required String title,
    required String value,
    required Color color,
  }) {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.05),
        borderRadius: BorderRadius.circular(12),
      ),
      child: Row(
        children: [
          Container(
            padding: const EdgeInsets.all(6),
            decoration: BoxDecoration(
              color: color.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(8),
            ),
            child: Icon(icon, color: color, size: 18),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  title,
                  style: TextStyle(
                    fontSize: 12,
                    color: Colors.grey.shade600,
                    fontWeight: FontWeight.w500,
                  ),
                ),
                const SizedBox(height: 2),
                Text(
                  value,
                  style: TextStyle(
                    fontSize: 14,
                    fontWeight: FontWeight.w600,
                    color: AppTheme.color.textColor,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildModernBottomBar() {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: const BorderRadius.only(
          topLeft: Radius.circular(25),
          topRight: Radius.circular(25),
        ),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.1),
            blurRadius: 20,
            offset: const Offset(0, -5),
          ),
        ],
      ),
      child: SafeArea(
        child: Row(
          children: [
            Expanded(
              child: Column(
                mainAxisSize: MainAxisSize.min,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Prix total',
                    style: TextStyle(
                      color: Colors.grey.shade600,
                      fontSize: 14,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                  const SizedBox(height: 4),
                  Text(
                    '${_formatPrice(_totalPrice)} FCFA',
                    style: TextStyle(
                      fontSize: 22,
                      fontWeight: FontWeight.bold,
                      color: AppTheme.color.primaryColor,
                    ),
                  ),
                ],
              ),
            ),
            const SizedBox(width: 16),
            Container(
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  colors: [
                    AppTheme.color.primaryColor,
                    AppTheme.color.primaryColor.withValues(alpha: 0.8),
                  ],
                ),
                borderRadius: BorderRadius.circular(20),
                boxShadow: [
                  BoxShadow(
                    color: AppTheme.color.primaryColor.withValues(alpha: 0.3),
                    blurRadius: 15,
                    offset: const Offset(0, 5),
                  ),
                ],
              ),
              child: ElevatedButton(
                onPressed:
                    _isGlobalLoading
                        ? null
                        : () {
                          _showOrderConfirmationDialog();
                        },
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.transparent,
                  foregroundColor: Colors.white,
                  elevation: 0,
                  padding: const EdgeInsets.symmetric(
                    horizontal: 32,
                    vertical: 16,
                  ),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(20),
                  ),
                ),
                child: Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Icon(Icons.shopping_cart_rounded, size: 20),
                    const SizedBox(width: 8),
                    Text(
                      'Commander',
                      style: TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildInfoItem({
    required IconData icon,
    required String title,
    required String subtitle,
  }) {
    return Row(
      children: [
        Container(
          padding: const EdgeInsets.all(10),
          decoration: BoxDecoration(
            color: Colors.white,
            shape: BoxShape.circle,
            boxShadow: [
              BoxShadow(
                color: Colors.black.withValues(alpha: 0.05),
                blurRadius: 5,
                offset: const Offset(0, 2),
              ),
            ],
          ),
          child: Icon(icon, color: AppTheme.color.primaryColor, size: 20),
        ),
        const SizedBox(width: 16),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                title,
                style: const TextStyle(
                  fontWeight: FontWeight.bold,
                  fontSize: 14,
                ),
              ),
              Text(
                subtitle,
                style: TextStyle(color: Colors.grey[600], fontSize: 13),
              ),
            ],
          ),
        ),
      ],
    );
  }

  void _showFinalOrderConfirmationDialog(
    int totalAmount,
    bool isJournalierPayment, // Renommé pour clarté
    String city,
  ) {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder:
          (context) => AlertDialog(
            title: Text(
              'Confirmer la commande',
              style: TextStyle(color: AppTheme.color.primaryColor),
            ),
            content: Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  widget.product['name'],
                  style: const TextStyle(
                    fontWeight: FontWeight.bold,
                    fontSize: 16,
                  ),
                ),
                const SizedBox(height: 16),
                Text(
                  'Prix total: ${_formatPrice(totalAmount)} FCFA',
                  style: const TextStyle(fontWeight: FontWeight.bold),
                ),
                const SizedBox(height: 8),
                Text(
                  'Modalités de paiement:',
                  style: TextStyle(color: AppTheme.color.brunGris),
                ),
                const SizedBox(height: 4),
                Text(
                  '${_formatPrice(_dailyPrice)} FCFA par jour pendant $_duration jours',
                  style: TextStyle(color: AppTheme.color.orangeColor),
                ),
                const SizedBox(height: 8),
                Text(
                  'Durée de paiement: $_duration jours',
                  style: TextStyle(
                    fontWeight: FontWeight.w500,
                    color: AppTheme.color.primaryColor,
                  ),
                ),
                const SizedBox(height: 12),
                Row(
                  children: [
                    Icon(
                      Icons.location_on,
                      size: 18,
                      color: AppTheme.color.primaryColor,
                    ),
                    const SizedBox(width: 8),
                    Expanded(
                      child: Text(
                        'Livraison à $city',
                        style: const TextStyle(fontWeight: FontWeight.w500),
                      ),
                    ),
                    TextButton(
                      onPressed: () {
                        Navigator.pop(context);
                        _showCitySelectionDialog(totalAmount);
                      },
                      style: TextButton.styleFrom(
                        padding: EdgeInsets.zero,
                        minimumSize: const Size(0, 0),
                        tapTargetSize: MaterialTapTargetSize.shrinkWrap,
                      ),
                      child: Text(
                        'Modifier',
                        style: TextStyle(
                          fontSize: 12,
                          color: AppTheme.color.primaryColor,
                        ),
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 16),
                const Text(
                  'En confirmant cette commande, vous vous engagez à effectuer les versements selon les modalités indiquées.',
                  style: TextStyle(fontSize: 12, fontStyle: FontStyle.italic),
                ),
              ],
            ),
            actions: [
              TextButton(
                onPressed: () {
                  Navigator.of(context).pop();
                },
                child: Text(
                  'Annuler',
                  style: TextStyle(color: AppTheme.color.brunGris),
                ),
              ),
              ElevatedButton(
                onPressed: () {
                  Navigator.of(context).pop();
                  _processOrder();
                },
                style: ElevatedButton.styleFrom(
                  backgroundColor: AppTheme.color.primaryColor,
                  foregroundColor: Colors.white,
                ),
                child: const Text('Confirmer'),
              ),
            ],
          ),
    );
  }

  void _showPaymentSuccess(String status) {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (BuildContext context) {
        return AlertDialog(
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(20),
          ),
          content: _buildStatusCard(status),
        );
      },
    );
  }

  Widget _buildStatusCard(String status) {
    String title;
    String message;
    Color color;
    IconData icon;
    List<Widget> actions;

    switch (status) {
      case 'succeeded':
      case 'ACCEPTED':
        title = 'Paiement réussi !';
        message =
            'Votre paiement a été traité avec succès. Votre commande va être enregistrée.';
        color = Colors.green;
        icon = Icons.check_circle;
        actions = [
          ElevatedButton(
            onPressed: () {
              Navigator.of(context).pop();
              _processSuccessfulPayment();
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: color,
              foregroundColor: Colors.white,
            ),
            child: const Text('Continuer'),
          ),
        ];
        break;
      case 'processing':
      case 'PENDING':
      case 'failed':
      default:
        title = 'Paiement échoué';
        message = 'Votre paiement a échoué. Veuillez réessayer.';
        color = Colors.red;
        icon = Icons.error;
        actions = [
          ElevatedButton(
            onPressed: () async {
              Navigator.of(context).pop();
              await _clearPendingTransaction();
              await _clearPaymentCodeAndReload();
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: color,
              foregroundColor: Colors.white,
            ),
            child: const Text('Annuler'),
          ),
        ];
        break;
    }

    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        Icon(icon, size: 64, color: color),
        const SizedBox(height: 16),
        Text(
          title,
          style: TextStyle(
            fontSize: 20,
            fontWeight: FontWeight.bold,
            color: color,
          ),
          textAlign: TextAlign.center,
        ),
        const SizedBox(height: 12),
        Text(
          message,
          style: const TextStyle(fontSize: 16),
          textAlign: TextAlign.center,
        ),
        const SizedBox(height: 24),
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceEvenly,
          children: actions,
        ),
      ],
    );
  }

  Future<Map<String, dynamic>> _verifyOrderAndPayment(
    String commandeId,
    double montant,
  ) async {
    try {
      print(
        'Début de la vérification: commandeId=$commandeId, montant=$montant',
      );

      // Attendre un peu pour laisser le temps au backend de traiter
      await Future.delayed(const Duration(seconds: 2));

      // Vérifier que la commande existe
      final orderVerification = await _verifyOrderExists(commandeId);
      if (!orderVerification['success']) {
        return {
          'success': false,
          'message': 'Commande non trouvée: ${orderVerification['message']}',
        };
      }

      // Vérifier que le versement est enregistré
      final paymentVerification = await _verifyPaymentExists(
        commandeId,
        montant,
      );
      if (!paymentVerification['success']) {
        return {
          'success': false,
          'message': 'Versement non trouvé: ${paymentVerification['message']}',
        };
      }

      return {
        'success': true,
        'message': 'Commande et versement vérifiés avec succès',
        'order': orderVerification['data'],
        'payment': paymentVerification['data'],
      };
    } catch (e) {
      print('Erreur lors de la vérification: $e');
      return {'success': false, 'message': 'Erreur de vérification: $e'};
    }
  }

  /// Vérifie que la commande existe dans le système
  Future<Map<String, dynamic>> _verifyOrderExists(String commandeId) async {
    try {
      final token = await AuthService.getAuthToken();
      if (token == null) {
        return {'success': false, 'message': 'Token manquant'};
      }

      final response = await http
          .get(
            Uri.parse(
              "${ApiConfig.baseUrl}/commande/getCommande.php?id=$commandeId",
            ),
            headers: {'Authorization': 'Bearer $token'},
          )
          .timeout(const Duration(seconds: 10));

      if (response.statusCode == 200) {
        final data = jsonDecode(response.body);
        if (data != null && data is Map && data.isNotEmpty) {
          return {'success': true, 'message': 'Commande trouvée', 'data': data};
        }
      }

      return {
        'success': false,
        'message': 'Commande non trouvée (status: ${response.statusCode})',
      };
    } catch (e) {
      return {
        'success': false,
        'message': 'Erreur de vérification commande: $e',
      };
    }
  }

  /// Vérifie que le versement existe pour la commande
  Future<Map<String, dynamic>> _verifyPaymentExists(
    String commandeId,
    double montant,
  ) async {
    try {
      final versements = await OrderService.getOrderVersements(commandeId);

      if (versements['success'] && versements['versements'] != null) {
        final List<dynamic> versementsList = versements['versements'];

        // Chercher un versement correspondant au montant
        for (var versement in versementsList) {
          if (versement is Map) {
            final versementMontant =
                double.tryParse(versement['montant']?.toString() ?? '0') ?? 0.0;
            if ((versementMontant - montant).abs() < 0.01) {
              // Tolérance de 1 centime
              return {
                'success': true,
                'message': 'Versement trouvé',
                'data': versement,
              };
            }
          }
        }
      }

      return {
        'success': false,
        'message': 'Versement non trouvé pour le montant $montant',
      };
    } catch (e) {
      return {
        'success': false,
        'message': 'Erreur de vérification versement: $e',
      };
    }
  }

  String _getAmountValidationMessage(
    double amount,
    double dailyPrice,
    double totalPrice,
  ) {
    if (amount < dailyPrice) {
      return 'Montant trop faible (minimum: ${_formatPrice(dailyPrice.toInt())} FCFA)';
    } else if (amount > totalPrice) {
      return 'Montant trop élevé (maximum: ${_formatPrice(totalPrice.toInt())} FCFA)';
    } else if (amount % 5 != 0) {
      return 'Le montant doit être un multiple de 5 FCFA';
    } else {
      final jours = (amount / dailyPrice).floor();
      return 'Versement valide pour $jours jour(s) de service';
    }
  }

  bool _isValidPhoneNumber(String phone) {
    // Supprimer espaces et caractères spéciaux
    String cleanPhone = phone.replaceAll(RegExp(r'[\s\-\(\)]'), '');

    // Format international: +225XXXXXXXXX (9 chiffres après +225)
    if (cleanPhone.startsWith('+225')) {
      return cleanPhone.length == 14 && // "+225" + 9 chiffres
          RegExp(r'^\+225[0-9]{10}$').hasMatch(cleanPhone);
    }
    // Format local: XXXXXXXXXX (10 chiffres)
    else {
      return cleanPhone.length == 10 &&
          RegExp(r'^[0-9]{10}$').hasMatch(cleanPhone);
    }
  }

  Future<void> _processSuccessfulOrderAfterPayment(String itemId) async {
    try {
      _startGlobalLoading('Création de la commande...');
      // Effectuer la commande via l'API
      final result = await OrderService.placeOrder(itemId, isCarnet: true);

      if (result['success']) {
        // Commande réussie
        print('Commande effectuée avec succès');
        print("message : ${result['message']}");

        // Enregistrer le versement après le succès de la commande
        try {
          final commandeId = result['commande_id']?.toString();
          if (commandeId != null && _pendingMontant != null) {
            print(
              'Enregistrement du versement: commandeId=$commandeId, montant=$_pendingMontant',
            );

            final versementResult = await OrderService.addVersement(
              commandeId,
              _pendingMontant!,
              monnaie: _pendingMonnaie ?? 0.0,
              transactionCode: transactionCode,
            );

            if (versementResult['success']) {
              print('Versement enregistré avec succès');

              // Nettoyer les données en attente
              await _clearPendingTransaction();
              await _clearPaymentCodeAndReload();

              // Afficher le succès avec informations de vérification
              if (!mounted) return;
              _showOrderSuccess(
                '${result['message'] ?? 'Commande enregistrée avec succès !'}\n\n✓ Commande confirmée\n✓ Versement enregistré',
              );
            } else {
              print(
                'Erreur lors de l\'enregistrement du versement: ${versementResult['message']}',
              );
              // Afficher l'erreur de versement
              _showOrderError(
                versementResult['message'] ??
                    'Erreur lors de l\'enregistrement du versement',
              );
            }
          } else {
            print(
              "Commande ID invalide : $commandeId ou montant manquant : $_pendingMontant",
            );
          }
        } catch (e) {
          print('Erreur lors de l\'enregistrement du versement: $e');
        }
      } else {
        print(
          'Erreur lors de l\'enregistrement de la commande: ${result['message']}',
        );
        _showOrderError(
          result['message'] ??
              'Erreur lors de l\'enregistrement de la commande',
        );
      }
    } catch (e) {
      print('Erreur lors du traitement de la commande après paiement: $e');
      _showOrderError('Erreur lors du traitement de la commande: $e');
    }
  }

  /// Vérifie le statut d'une transaction CinetPay spécifique
  Future<void> _checkCinetPayTransactionStatus(String transactionId) async {
    try {
      print('Vérification du statut CinetPay pour transaction: $transactionId');

      // Utiliser le service CinetPay pour vérifier le statut
      final statusResult = await CinetPayService.checkTransactionStatus(
        transactionId,
      );

      if (statusResult['success']) {
        final status = statusResult['status']?.toString().toUpperCase();
        print('Statut CinetPay reçu: $status');

        if (status == 'ACCEPTED' || status == 'SUCCEEDED') {
          // Paiement réussi, traiter la commande
          print('Paiement CinetPay réussi, traitement de la commande...');

          // Récupérer l'itemId depuis les données en attente
          if (_pendingItemId != null) {
            await _processSuccessfulOrderAfterPayment(_pendingItemId!);
            // Nettoyer les données en attente après succès
            await _clearPendingTransaction();
          } else {
            print('Erreur: _pendingItemId est null');
            if (mounted) {
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(
                  content: Text('Erreur: informations de commande manquantes'),
                  backgroundColor: Colors.red,
                ),
              );
            }
          }
        } else if (status == 'PENDING' || status == 'PROCESSING') {
          print('Transaction CinetPay encore en cours: $status');
          // Continuer la vérification périodique
        } else {
          // Transaction échouée ou annulée
          print('Transaction CinetPay échouée: $status');
          await _clearPendingTransaction();

          if (mounted) {
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Text('Paiement CinetPay échoué. Statut: $status'),
                backgroundColor: Colors.red,
              ),
            );
          }
        }
      } else {
        print(
          'Erreur lors de la vérification CinetPay: ${statusResult['message']}',
        );
      }
    } catch (error) {
      print('Erreur lors de la vérification CinetPay: $error');
    }
  }

  /// Détermine si un ID de transaction appartient à CinetPay
  bool _isCinetPayTransactionId(String transactionId) {
    return transactionId.contains('CP_') ||
        transactionId.contains('CINETPAY') ||
        !transactionId.contains('-'); // Wave utilise souvent des tirets
  }

  void _startCinetPayPeriodicStatusCheck() {
    // Vérifier le statut toutes les 10 secondes pendant 3 minutes maximum
    int attempts = 0;
    const maxAttempts = 18; // 3 minutes / 10 secondes

    Timer.periodic(const Duration(seconds: 10), (timer) async {
      attempts++;

      if (attempts >= maxAttempts || !mounted) {
        timer.cancel();
        // Nettoyer les données en attente après timeout
        if (mounted) {
          await _clearPendingTransaction();
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(
                'Délai de vérification dépassé. Veuillez vérifier manuellement.',
              ),
              backgroundColor: Colors.orange,
            ),
          );
        }
        return;
      }

      if (_pendingTransactionCode != null) {
        await _checkCinetPayTransactionStatus(_pendingTransactionCode!);

        // Si la transaction est traitée, arrêter la vérification
        if (_pendingTransactionCode == null) {
          timer.cancel();
        }
      } else {
        timer.cancel();
      }
    });
  }
}
