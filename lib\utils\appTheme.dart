import 'package:flutter/material.dart';

class AppTheme {
  static final color = _color();
  static final asset = _Asset();
  static final assetSvg = _AssetSvg();
  
  // Ajout du thème principal de l'application
  static ThemeData get lightTheme {
    return ThemeData(
      useMaterial3: true,
      colorScheme: ColorScheme.fromSeed(
        seedColor: color.primaryColor,
        primary: color.primaryColor,
        secondary: color.secondaryColor,
        background: color.whithColor,
      ),
      appBarTheme: AppBarTheme(
        backgroundColor: color.primaryColor,
        foregroundColor: color.whithColor,
        elevation: 0,
      ),
      elevatedButtonTheme: ElevatedButtonThemeData(
        style: ElevatedButton.styleFrom(
          backgroundColor: color.primaryColor,
          foregroundColor: color.whithColor,
        ),
      ),
      textTheme: TextTheme(
        titleLarge: TextStyle(color: color.primaryColor, fontWeight: FontWeight.bold),
        bodyMedium: TextStyle(color: color.brunGris),
      ),
    );
  }
}

class _color {
  final primaryColor = Color(0xFF0073E6);//Titres, boutons, liens principaux
  final brunGris = Color(0XFF95836F); //Sous-titres, paragraphes
  final secondaryColor = Color(0xFF6EDCFF); //Hover, dégradés, arrière-plans légers
  final whithColor = Color(0xffFFFFFF);
  final jauneDoreclair = Color(0XFFFFD180); //Hover/effets secondaires
  final orangeColor = Color(0xFFF7941D); //Accents, icônes
  final greenColor = Color(0xFF4CAF50); // Pour les versements effectués
  final redColor = Color(0xFFF44336); // Pour les versements non effectués
  final lightGrey = Color(0xFFF5F5F5); // Fond gris clair pour les conteneurs
  final textColor = Color(0xFF1A1A1A); // Couleur principale pour le texte
  final accentColor = Color(0xFFFFA500); // Couleur d'accentuation
}

class _Asset {
  final logo = "assets/images/logo.png";
}

class _AssetSvg {

}
