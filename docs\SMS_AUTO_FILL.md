# Détection Automatique des SMS OTP

## Vue d'ensemble

Cette fonctionnalité permet la saisie automatique des codes OTP (One-Time Password) reçus par SMS dans l'écran de vérification OTP de l'application CallitrisPay.

## Fonctionnalités

### 1. Détection Automatique
- **Saisie automatique** : Le code OTP est automatiquement saisi dès qu'un SMS contenant un code à 6 chiffres est reçu
- **Vérification automatique** : Une fois le code saisi automatiquement, la vérification se lance automatiquement
- **Double méthode** : Utilise deux packages différents pour maximiser la compatibilité

### 2. Packages Utilisés

#### sms_autofill
- Package principal pour la détection automatique des SMS OTP
- Utilise l'API Google SMS Retriever
- Plus précis et sécurisé

#### telephony
- Package de fallback en cas d'échec du premier
- Écoute tous les SMS entrants
- Extrait automatiquement les codes à 6 chiffres

## Configuration

### Permissions Android

Les permissions suivantes ont été ajoutées dans `android/app/src/main/AndroidManifest.xml` :

```xml
<!-- Permissions pour la détection automatique des SMS OTP -->
<uses-permission android:name="android.permission.RECEIVE_SMS"/>
<uses-permission android:name="android.permission.READ_SMS"/>
<uses-permission android:name="com.google.android.gms.auth.api.phone.permission.SEND"/>
```

### Receiver SMS

Un receiver a été configuré pour intercepter les SMS :

```xml
<!-- Receiver pour la détection automatique des SMS -->
<receiver android:name="com.jaumard.smsautofill.SmsAutoFillReceiver"
    android:exported="true">
    <intent-filter>
        <action android:name="com.google.android.gms.auth.api.phone.SMS_RETRIEVED"/>
    </intent-filter>
</receiver>
```

## Utilisation

### Fonctionnement Automatique

1. **Envoi du code OTP** : Quand un code OTP est envoyé au numéro de téléphone
2. **Réception SMS** : L'application détecte automatiquement le SMS entrant
3. **Extraction du code** : Le code à 6 chiffres est extrait du message
4. **Saisie automatique** : Le code est automatiquement saisi dans le champ OTP
5. **Vérification automatique** : La vérification se lance automatiquement

### Gestion des Erreurs

- Si `sms_autofill` ne fonctionne pas, `telephony` prend le relais
- Si aucun des deux packages ne fonctionne, la saisie manuelle reste possible
- Les erreurs sont loggées pour le débogage

## Format des SMS Supportés

L'application peut détecter les codes OTP dans différents formats de SMS :

- `Votre code de vérification est : 123456`
- `Code OTP: 123456`
- `123456 est votre code de vérification`
- Tout message contenant 6 chiffres consécutifs

## Sécurité

### Permissions Minimales
- Les permissions sont demandées uniquement quand nécessaire
- L'écoute des SMS est limitée à l'écran OTP
- Aucun SMS n'est stocké ou transmis

### Signature d'Application
- `sms_autofill` utilise la signature de l'application pour sécuriser la détection
- Seuls les SMS destinés à cette application spécifique sont traités

## Débogage

### Logs Disponibles

```
App Signature: [signature_hash]
sms_autofill initialisé avec succès
telephony initialisé avec succès
SMS reçu: [contenu_du_sms]
Code OTP détecté: 123456
```

### Problèmes Courants

1. **MissingPluginException** : Redémarrer l'application après installation
2. **Permissions refusées** : Vérifier les permissions dans les paramètres Android
3. **Code non détecté** : Vérifier le format du SMS (doit contenir 6 chiffres consécutifs)

## Tests

### Test Manuel

1. Lancer l'application
2. Aller à l'écran de vérification OTP
3. Envoyer un SMS au téléphone avec un code à 6 chiffres
4. Vérifier que le code est automatiquement saisi et vérifié

### Test avec Émulateur

Pour tester avec l'émulateur Android :

1. Ouvrir l'émulateur
2. Aller dans l'application Messages
3. Envoyer un SMS avec un code à 6 chiffres
4. Retourner à l'application CallitrisPay

## Maintenance

### Mise à jour des Packages

```bash
flutter pub upgrade sms_autofill
flutter pub upgrade telephony
```

### Nettoyage après Mise à jour

```bash
flutter clean
flutter pub get
```

## Compatibilité

- **Android** : Entièrement supporté (API 21+)
- **iOS** : Partiellement supporté (limitations d'iOS pour l'accès aux SMS)
- **Web/Desktop** : Non supporté (pas d'accès aux SMS)
