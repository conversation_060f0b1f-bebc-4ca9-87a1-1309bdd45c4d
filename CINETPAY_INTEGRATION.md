# Intégration CinetPay dans Callitris

Cette documentation explique comment configurer et utiliser l'intégration CinetPay dans l'application Callitris.

## Configuration

### 1. Clés API CinetPay

Modifiez le fichier `lib/config/api_config.dart` avec vos vraies clés CinetPay :

```dart
// Configuration CinetPay
static const String cinetPayApiKey = 'VOTRE_CLE_API_CINETPAY';
static const String cinetPaySiteId = 'VOTRE_SITE_ID';
static const String cinetPaySecretKey = 'VOTRE_CLE_SECRETE';
static const bool cinetPaySandboxMode = false; // true pour les tests
```

### 2. URLs de callback

Configurez les URLs de callback sur votre tableau de bord CinetPay :
- Return URL: `https://votre-domaine.com/cinetpay/return`
- Notify URL: `https://votre-domaine.com/cinetpay/notify`
- Cancel URL: `https://votre-domaine.com/cinetpay/cancel`

## Fonctionnalités

### 1. Paiement de versements

L'intégration permet aux utilisateurs de payer leurs versements de commandes via :
- **Monnaie du portefeuille** : Utilise le solde existant
- **CinetPay** : Paiement mobile (Orange Money, MTN Money, Moov Money, Wave)

### 2. Gestion des transactions

- **Historique complet** : Toutes les transactions sont sauvegardées localement
- **Statuts en temps réel** : Suivi du statut des paiements
- **Retry automatique** : Reprise des paiements échoués
- **Vérification de statut** : Synchronisation avec l'API CinetPay

### 3. Interface utilisateur

- **Sélecteur de méthode** : Interface intuitive pour choisir le mode de paiement
- **Notifications** : Messages de succès/erreur contextuels
- **Écran d'historique** : Consultation des transactions passées
- **Gestion des erreurs** : Messages d'erreur clairs et actions de récupération

## Utilisation

### 1. Effectuer un versement

1. Aller dans "Mes commandes"
2. Sélectionner une commande active
3. Cliquer sur "Verser"
4. Choisir le montant
5. Sélectionner la méthode de paiement :
   - **Monnaie** : Déduction automatique du portefeuille
   - **CinetPay** : Redirection vers la page de paiement sécurisée
6. Suivre les instructions de paiement

### 2. Consulter l'historique

1. Aller dans "Mes commandes"
2. Cliquer sur l'icône d'historique (⏰) dans l'AppBar
3. Consulter les transactions par statut :
   - **Toutes** : Toutes les transactions
   - **Réussies** : Paiements confirmés
   - **Échouées** : Paiements ayant échoué
   - **En attente** : Paiements en cours de traitement

### 3. Reprendre un paiement échoué

1. Dans l'historique, onglet "Échouées"
2. Cliquer sur une transaction échouée
3. Vérifier si elle peut être reprise
4. Cliquer sur "Reprendre" si disponible

## Architecture technique

### Services principaux

1. **CinetPayService** : Interface avec l'API CinetPay
2. **TransactionHistoryService** : Gestion de l'historique local
3. **PaymentRetryService** : Logique de retry et vérification
4. **PaymentErrorHandler** : Gestion des erreurs et notifications

### Modèles de données

1. **CinetPayTransaction** : Modèle de transaction complète
2. **PaymentResult** : Résultat d'un paiement
3. **TransactionStatus** : États possibles d'une transaction
4. **TransactionType** : Types de transactions (versement, package, etc.)

### Widgets personnalisés

1. **PaymentMethodSelector** : Sélection de la méthode de paiement
2. **TransactionStatusWidget** : Affichage du statut d'une transaction
3. **TransactionListWidget** : Liste de transactions
4. **PendingTransactionsWidget** : Gestion des transactions en attente

## Sécurité

### 1. Validation côté client

- Vérification des montants minimum
- Validation des numéros de téléphone
- Contrôle des devises supportées

### 2. Gestion des erreurs

- Messages d'erreur contextuels
- Retry intelligent avec limite de tentatives
- Vérification de statut automatique

### 3. Données sensibles

- Les clés API ne sont jamais exposées côté client
- Chiffrement des données de transaction locales
- Validation des callbacks serveur

## Tests

### 1. Mode Sandbox

Activez le mode sandbox pour les tests :

```dart
static const bool cinetPaySandboxMode = true;
```

### 2. Numéros de test

Utilisez ces numéros pour tester les différents scénarios :
- **Succès** : +225 01 02 03 04 05
- **Échec** : +225 01 02 03 04 06
- **Timeout** : +225 01 02 03 04 07

### 3. Montants de test

- **Minimum** : 100 FCFA
- **Maximum** : 1 000 000 FCFA
- **Échec** : 13 FCFA (montant spécial pour simuler un échec)

## Dépannage

### 1. Erreurs courantes

**"Clé API invalide"**
- Vérifiez vos clés dans `api_config.dart`
- Assurez-vous d'être en mode sandbox/production approprié

**"Paiement refusé"**
- Vérifiez le solde du compte de test
- Utilisez les numéros de test appropriés

**"Transaction expirée"**
- Augmentez le timeout dans la configuration
- Vérifiez la connexion internet

### 2. Logs de débogage

Activez les logs pour le débogage :

```dart
// Dans CinetPayService
static const bool debugMode = true;
```

### 3. Support

Pour toute question technique :
1. Consultez la documentation CinetPay
2. Vérifiez les logs de l'application
3. Contactez le support technique CinetPay

## Mise à jour

### 1. Nouvelles versions

Surveillez les mises à jour du SDK CinetPay :

```bash
flutter pub upgrade cinetpay
```

### 2. Migration

En cas de changement d'API, consultez le guide de migration CinetPay et mettez à jour :
- Les modèles de données
- Les appels d'API
- La configuration

## Performance

### 1. Optimisations

- Cache local des transactions
- Pagination de l'historique
- Compression des données

### 2. Monitoring

- Suivi des taux de succès
- Temps de réponse des paiements
- Erreurs fréquentes

Cette intégration offre une expérience de paiement fluide et sécurisée pour vos utilisateurs Callitris.
