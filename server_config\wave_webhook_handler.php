<?php
/**
 * Gestionnaire de Webhooks Wave pour Callitris
 * 
 * Ce fichier traite les webhooks envoyés par Wave après les paiements
 * Il met à jour la table transactions selon le checkout_status reçu
 * 
 * URL du webhook: https://api.callitris-distribution.com/client-api.callitris-distribution.com/webhooks/wave
 * 
 * Types de webhooks supportés:
 * - checkout.session.completed (paiement réussi)
 * - checkout.session.payment_failed (paiement échoué)
 */

// Configuration des headers
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization, X-Wave-Signature');

// Gestion des requêtes OPTIONS (CORS preflight)
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit();
}

// Configuration de la base de données
// IMPORTANT: Modifiez ces paramètres selon votre configuration
define('DB_HOST', 'localhost');
define('DB_NAME', 'callitris');
define('DB_USER', 'your_username');
define('DB_PASS', 'your_password');
define('DB_CHARSET', 'utf8mb4');

// Configuration des logs
define('LOG_FILE', __DIR__ . '/logs/wave_webhooks.log');
define('LOG_LEVEL', 'INFO'); // DEBUG, INFO, WARNING, ERROR

// Clé secrète Wave pour vérifier la signature (optionnel mais recommandé)
define('WAVE_WEBHOOK_SECRET', 'your_wave_webhook_secret');

/**
 * Fonction de logging avec niveaux
 */
function logMessage($message, $level = 'INFO') {
    if (!in_array($level, ['DEBUG', 'INFO', 'WARNING', 'ERROR'])) {
        $level = 'INFO';
    }
    
    $timestamp = date('Y-m-d H:i:s');
    $logEntry = "[$timestamp] [$level] $message" . PHP_EOL;
    
    // Créer le dossier logs s'il n'existe pas
    $logDir = dirname(LOG_FILE);
    if (!is_dir($logDir)) {
        mkdir($logDir, 0755, true);
    }
    
    file_put_contents(LOG_FILE, $logEntry, FILE_APPEND | LOCK_EX);
    
    // Afficher aussi en mode debug
    if (LOG_LEVEL === 'DEBUG') {
        error_log($logEntry);
    }
}

/**
 * Connexion à la base de données avec PDO
 */
function getDatabaseConnection() {
    try {
        $dsn = "mysql:host=" . DB_HOST . ";dbname=" . DB_NAME . ";charset=" . DB_CHARSET;
        $options = [
            PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
            PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
            PDO::ATTR_EMULATE_PREPARES => false,
        ];
        
        $pdo = new PDO($dsn, DB_USER, DB_PASS, $options);
        logMessage("Connexion à la base de données établie", 'DEBUG');
        return $pdo;
        
    } catch (PDOException $e) {
        logMessage("Erreur de connexion à la base de données: " . $e->getMessage(), 'ERROR');
        throw new Exception("Erreur de base de données");
    }
}

/**
 * Vérification de la signature Wave (optionnel mais recommandé)
 */
function verifyWaveSignature($payload, $signature) {
    if (empty(WAVE_WEBHOOK_SECRET)) {
        logMessage("Signature Wave non vérifiée (secret non configuré)", 'WARNING');
        return true; // Passer si pas de secret configuré
    }
    
    $expectedSignature = hash_hmac('sha256', $payload, WAVE_WEBHOOK_SECRET);
    $isValid = hash_equals($expectedSignature, $signature);
    
    if (!$isValid) {
        logMessage("Signature Wave invalide", 'ERROR');
    }
    
    return $isValid;
}

/**
 * Validation des données du webhook Wave
 */
function validateWebhookData($data) {
    // Vérifier la structure de base
    if (!isset($data['id']) || !isset($data['type']) || !isset($data['data'])) {
        logMessage("Structure de webhook invalide: " . json_encode($data), 'ERROR');
        return false;
    }
    
    $webhookData = $data['data'];
    
    // Vérifier les champs requis dans data
    $requiredFields = ['id', 'checkout_status', 'amount', 'currency'];
    foreach ($requiredFields as $field) {
        if (!isset($webhookData[$field])) {
            logMessage("Champ requis manquant: $field", 'ERROR');
            return false;
        }
    }
    
    // Vérifier que le montant est numérique
    if (!is_numeric($webhookData['amount'])) {
        logMessage("Montant invalide: " . $webhookData['amount'], 'ERROR');
        return false;
    }
    
    // Vérifier le statut
    $validStatuses = ['complete', 'failed', 'pending', 'cancelled'];
    if (!in_array($webhookData['checkout_status'], $validStatuses)) {
        logMessage("Statut invalide: " . $webhookData['checkout_status'], 'ERROR');
        return false;
    }
    
    return true;
}

/**
 * Convertir le checkout_status Wave en statut de transaction
 */
function mapCheckoutStatusToTransactionStatus($checkoutStatus) {
    $statusMap = [
        'complete' => 'success',
        'failed' => 'failed',
        'pending' => 'pending',
        'cancelled' => 'cancelled'
    ];
    
    return $statusMap[$checkoutStatus] ?? 'pending';
}

/**
 * Enregistrer le webhook dans les logs
 */
function logWebhookCallback($pdo, $webhookData, $webhookType, $requestData, $responseData, $statusCode) {
    try {
        $sql = "INSERT INTO wave_callback_logs (
                    transaction_id, 
                    callback_type, 
                    request_method, 
                    request_data, 
                    response_data, 
                    ip_address, 
                    user_agent, 
                    status_code, 
                    created_at
                ) VALUES (
                    :transaction_id, 
                    :callback_type, 
                    :request_method, 
                    :request_data, 
                    :response_data, 
                    :ip_address, 
                    :user_agent, 
                    :status_code, 
                    NOW()
                )";
        
        $stmt = $pdo->prepare($sql);
        $stmt->execute([
            ':transaction_id' => $webhookData['transaction_id'] ?? $webhookData['id'] ?? null,
            ':callback_type' => $webhookType,
            ':request_method' => $_SERVER['REQUEST_METHOD'],
            ':request_data' => json_encode($requestData),
            ':response_data' => json_encode($responseData),
            ':ip_address' => $_SERVER['REMOTE_ADDR'] ?? null,
            ':user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? null,
            ':status_code' => $statusCode
        ]);
        
        logMessage("Webhook callback enregistré dans les logs", 'DEBUG');
        
    } catch (PDOException $e) {
        logMessage("Erreur lors de l'enregistrement du callback: " . $e->getMessage(), 'ERROR');
    }
}

/**
 * Mise à jour de la table commandes (si elle existe)
 */
function updateOrderStatus($pdo, $checkoutId, $transactionId, $status, $amount, $whenCompleted) {
    try {
        // Vérifier si la table commandes existe
        $tableCheck = $pdo->query("SHOW TABLES LIKE 'commandes'");
        if ($tableCheck->rowCount() == 0) {
            logMessage("Table commandes non trouvée, pas de mise à jour", 'DEBUG');
            return;
        }
        
        // Mettre à jour la commande
        $sql = "UPDATE commandes SET 
                    payment_status = :status,
                    payment_method = 'wave',
                    payment_transaction_id = :transaction_id,
                    payment_amount = :amount,
                    payment_date = :payment_date,
                    updated_at = NOW()
                WHERE id = :order_id OR payment_reference = :checkout_id";
        
        $stmt = $pdo->prepare($sql);
        $result = $stmt->execute([
            ':status' => $status,
            ':transaction_id' => $transactionId ?: $checkoutId,
            ':amount' => $amount,
            ':payment_date' => $whenCompleted ? date('Y-m-d H:i:s', strtotime($whenCompleted)) : date('Y-m-d H:i:s'),
            ':order_id' => $checkoutId,
            ':checkout_id' => $checkoutId
        ]);
        
        if ($result && $stmt->rowCount() > 0) {
            logMessage("Commande mise à jour - Checkout ID: $checkoutId, Status: $status", 'INFO');
        } else {
            logMessage("Aucune commande trouvée pour le checkout ID: $checkoutId", 'WARNING');
        }
        
    } catch (PDOException $e) {
        logMessage("Erreur lors de la mise à jour de la commande: " . $e->getMessage(), 'ERROR');
    }
}

/**
 * Mise à jour de la table transactions
 */
function updateTransaction($pdo, $webhookData, $webhookType) {
    try {
        // Extraire les informations importantes
        $checkoutId = $webhookData['id'];
        $checkoutStatus = $webhookData['checkout_status'];
        $amount = floatval($webhookData['amount']);
        $currency = $webhookData['currency'];
        $transactionId = $webhookData['transaction_id'] ?? null;
        $clientReference = $webhookData['client_reference'] ?? null;
        $paymentStatus = $webhookData['payment_status'] ?? $checkoutStatus;
        $whenCompleted = $webhookData['when_completed'] ?? null;
        $whenCreated = $webhookData['when_created'] ?? null;

        // Convertir le checkout_status en statut de transaction
        $transactionStatus = mapCheckoutStatusToTransactionStatus($checkoutStatus);

        logMessage("Mise à jour transaction - Checkout ID: $checkoutId, Status: $checkoutStatus -> $transactionStatus", 'INFO');

        // Vérifier si la transaction existe déjà
        $checkSql = "SELECT id, status FROM wave_transactions WHERE transaction_id = :transaction_id OR order_id = :checkout_id";
        $checkStmt = $pdo->prepare($checkSql);
        $checkStmt->execute([
            ':transaction_id' => $transactionId,
            ':checkout_id' => $checkoutId
        ]);
        $existingTransaction = $checkStmt->fetch();

        if ($existingTransaction) {
            // Mettre à jour la transaction existante
            $updateSql = "UPDATE wave_transactions SET
                            status = :status,
                            amount = :amount,
                            currency = :currency,
                            wave_response = :wave_response,
                            callback_received_at = NOW(),
                            updated_at = NOW()
                          WHERE id = :id";

            $updateStmt = $pdo->prepare($updateSql);
            $result = $updateStmt->execute([
                ':status' => $transactionStatus,
                ':amount' => $amount,
                ':currency' => $currency,
                ':wave_response' => json_encode($webhookData),
                ':id' => $existingTransaction['id']
            ]);

            if ($result) {
                logMessage("Transaction mise à jour avec succès - ID: " . $existingTransaction['id'], 'INFO');

                // Mettre à jour aussi la table commandes si elle existe
                updateOrderStatus($pdo, $checkoutId, $transactionId, $transactionStatus, $amount, $whenCompleted);

                return ['success' => true, 'action' => 'updated', 'transaction_id' => $existingTransaction['id']];
            } else {
                logMessage("Échec de la mise à jour de la transaction", 'ERROR');
                return ['success' => false, 'error' => 'Échec de la mise à jour'];
            }

        } else {
            // Créer une nouvelle transaction
            $insertSql = "INSERT INTO wave_transactions (
                            transaction_id,
                            order_id,
                            customer_id,
                            customer_phone,
                            amount,
                            currency,
                            status,
                            wave_response,
                            callback_received_at,
                            created_at,
                            updated_at
                          ) VALUES (
                            :transaction_id,
                            :order_id,
                            :customer_id,
                            :customer_phone,
                            :amount,
                            :currency,
                            :status,
                            :wave_response,
                            NOW(),
                            NOW(),
                            NOW()
                          )";

            $insertStmt = $pdo->prepare($insertSql);
            $result = $insertStmt->execute([
                ':transaction_id' => $transactionId ?: $checkoutId,
                ':order_id' => $checkoutId,
                ':customer_id' => $clientReference,
                ':customer_phone' => null, // Pas fourni dans le webhook
                ':amount' => $amount,
                ':currency' => $currency,
                ':status' => $transactionStatus,
                ':wave_response' => json_encode($webhookData)
            ]);

            if ($result) {
                $newTransactionId = $pdo->lastInsertId();
                logMessage("Nouvelle transaction créée - ID: $newTransactionId", 'INFO');

                // Mettre à jour aussi la table commandes si elle existe
                updateOrderStatus($pdo, $checkoutId, $transactionId, $transactionStatus, $amount, $whenCompleted);

                return ['success' => true, 'action' => 'created', 'transaction_id' => $newTransactionId];
            } else {
                logMessage("Échec de la création de la transaction", 'ERROR');
                return ['success' => false, 'error' => 'Échec de la création'];
            }
        }

    } catch (PDOException $e) {
        logMessage("Erreur SQL lors de la mise à jour: " . $e->getMessage(), 'ERROR');
        return ['success' => false, 'error' => 'Erreur de base de données'];
    } catch (Exception $e) {
        logMessage("Erreur lors de la mise à jour: " . $e->getMessage(), 'ERROR');
        return ['success' => false, 'error' => $e->getMessage()];
    }
}

/**
 * Traitement principal du webhook
 */
function processWebhook($webhookData, $webhookType) {
    $response = ['success' => false, 'message' => '', 'data' => []];

    try {
        // Connexion à la base de données
        $pdo = getDatabaseConnection();

        // Validation des données
        if (!validateWebhookData($webhookData)) {
            $response['message'] = 'Données de webhook invalides';
            return $response;
        }

        // Traitement selon le type de webhook
        switch ($webhookType) {
            case 'checkout.session.completed':
                logMessage("Traitement du webhook de paiement réussi", 'INFO');
                $result = updateTransaction($pdo, $webhookData['data'], $webhookType);
                break;

            case 'checkout.session.payment_failed':
                logMessage("Traitement du webhook de paiement échoué", 'INFO');
                $result = updateTransaction($pdo, $webhookData['data'], $webhookType);
                break;

            default:
                logMessage("Type de webhook non supporté: $webhookType", 'WARNING');
                $response['message'] = 'Type de webhook non supporté';
                return $response;
        }

        // Enregistrer le callback dans les logs
        logWebhookCallback($pdo, $webhookData['data'], $webhookType, $webhookData, $result, 200);

        if ($result['success']) {
            $response['success'] = true;
            $response['message'] = 'Webhook traité avec succès';
            $response['data'] = $result;
            logMessage("Webhook traité avec succès - Type: $webhookType", 'INFO');
        } else {
            $response['message'] = $result['error'] ?? 'Erreur lors du traitement';
            logMessage("Échec du traitement du webhook: " . $response['message'], 'ERROR');
        }

    } catch (Exception $e) {
        $response['message'] = 'Erreur interne: ' . $e->getMessage();
        logMessage("Erreur lors du traitement du webhook: " . $e->getMessage(), 'ERROR');
    }

    return $response;
}

// ============================================================================
// TRAITEMENT PRINCIPAL
// ============================================================================

try {
    // Vérifier que c'est une requête POST
    if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
        http_response_code(405);
        echo json_encode(['error' => 'Méthode non autorisée. Seules les requêtes POST sont acceptées.']);
        exit();
    }

    // Récupérer le contenu brut de la requête
    $rawPayload = file_get_contents('php://input');

    if (empty($rawPayload)) {
        logMessage("Payload vide reçu", 'ERROR');
        http_response_code(400);
        echo json_encode(['error' => 'Payload vide']);
        exit();
    }

    logMessage("Webhook reçu - Taille: " . strlen($rawPayload) . " bytes", 'INFO');
    logMessage("Payload brut: " . $rawPayload, 'DEBUG');

    // Vérifier la signature si configurée
    $signature = $_SERVER['HTTP_X_WAVE_SIGNATURE'] ?? '';
    if (!verifyWaveSignature($rawPayload, $signature)) {
        http_response_code(401);
        echo json_encode(['error' => 'Signature invalide']);
        exit();
    }

    // Décoder le JSON
    $webhookData = json_decode($rawPayload, true);

    if (json_last_error() !== JSON_ERROR_NONE) {
        logMessage("Erreur de décodage JSON: " . json_last_error_msg(), 'ERROR');
        http_response_code(400);
        echo json_encode(['error' => 'JSON invalide: ' . json_last_error_msg()]);
        exit();
    }

    // Extraire le type de webhook
    $webhookType = $webhookData['type'] ?? '';

    if (empty($webhookType)) {
        logMessage("Type de webhook manquant", 'ERROR');
        http_response_code(400);
        echo json_encode(['error' => 'Type de webhook manquant']);
        exit();
    }

    logMessage("Type de webhook: $webhookType", 'INFO');
    logMessage("Données du webhook: " . json_encode($webhookData), 'DEBUG');

    // Traiter le webhook
    $result = processWebhook($webhookData, $webhookType);

    // Retourner la réponse
    if ($result['success']) {
        http_response_code(200);
        echo json_encode([
            'status' => 'success',
            'message' => $result['message'],
            'data' => $result['data'],
            'timestamp' => date('Y-m-d H:i:s')
        ]);
    } else {
        http_response_code(400);
        echo json_encode([
            'status' => 'error',
            'message' => $result['message'],
            'timestamp' => date('Y-m-d H:i:s')
        ]);
    }

} catch (Exception $e) {
    logMessage("Erreur fatale: " . $e->getMessage(), 'ERROR');
    http_response_code(500);
    echo json_encode([
        'status' => 'error',
        'message' => 'Erreur interne du serveur',
        'timestamp' => date('Y-m-d H:i:s')
    ]);
}

// ============================================================================
// FONCTIONS UTILITAIRES POUR LES TESTS
// ============================================================================

/**
 * Fonction pour tester le webhook avec des données d'exemple
 * Appelez cette URL avec ?test=1 pour tester
 */
if (isset($_GET['test']) && $_GET['test'] == '1') {
    // Données de test basées sur votre exemple
    $testData = [
        'success' => [
            "id" => "AE_ijzo7oGgrlM7",
            "type" => "checkout.session.completed",
            "data" => [
                "id" => "cos-1b01sghpg100j",
                "amount" => "100",
                "checkout_status" => "complete",
                "client_reference" => "test_client_123",
                "currency" => "XOF",
                "error_url" => "https://example.com/error",
                "last_payment_error" => null,
                "business_name" => "Annas Apiaries",
                "payment_status" => "succeeded",
                "success_url" => "https://example.com/success",
                "wave_launch_url" => "https://pay.wave.com/c/cos-1b01sghpg100j?a=100&c=XOF&m=Annas%20Apiaries",
                "when_completed" => "2022-11-08T15:05:45Z",
                "when_created" => "2022-11-08T15:05:32Z",
                "when_expires" => "2022-11-09T15:05:32Z",
                "transaction_id" => "TCN4Y4ZC3FM"
            ]
        ],
        'failed' => [
            "id" => "EV_QvEZuDSQbLdI",
            "type" => "checkout.session.payment_failed",
            "data" => [
                "id" => "cos-18qq25rgr100a",
                "amount" => "1000",
                "checkout_status" => "failed",
                "client_reference" => "1f31dfd7-aec8-4adf-84ff-4a9c1981be2a",
                "currency" => "XOF",
                "error_url" => "https://example.com/error",
                "payment_status" => "failed",
                "success_url" => "https://example.com/success",
                "wave_launch_url" => "https://pay.wave.com/c/cos-18qq25rgr100a",
                "when_created" => "2021-12-08T10:13:04Z",
                "when_expires" => "2021-12-09T10:13:04Z"
            ]
        ]
    ];

    $testType = $_GET['type'] ?? 'success';
    $testWebhook = $testData[$testType] ?? $testData['success'];

    echo "<h2>Test du Webhook Wave - Type: $testType</h2>";
    echo "<h3>Données de test:</h3>";
    echo "<pre>" . json_encode($testWebhook, JSON_PRETTY_PRINT) . "</pre>";

    $result = processWebhook($testWebhook, $testWebhook['type']);

    echo "<h3>Résultat:</h3>";
    echo "<pre>" . json_encode($result, JSON_PRETTY_PRINT) . "</pre>";

    exit();
}

?>

<!--
INSTRUCTIONS D'INSTALLATION ET DE CONFIGURATION:

1. Configuration de la base de données:
   - Modifiez les constantes DB_HOST, DB_NAME, DB_USER, DB_PASS
   - Exécutez le script wave_database_setup.sql pour créer les tables

2. Configuration Wave:
   - Configurez l'URL du webhook dans votre dashboard Wave:
     https://api.callitris-distribution.com/client-api.callitris-distribution.com/webhooks/wave
   - Optionnel: Configurez WAVE_WEBHOOK_SECRET pour la vérification de signature

3. Permissions:
   - Assurez-vous que le dossier logs/ est accessible en écriture
   - Vérifiez que PHP peut se connecter à votre base de données

4. Test:
   - Testez avec: https://votre-domaine.com/path/to/wave_webhook_handler.php?test=1
   - Testez l'échec avec: ?test=1&type=failed

5. Logs:
   - Les logs sont stockés dans logs/wave_webhooks.log
   - Changez LOG_LEVEL à 'DEBUG' pour plus de détails

6. Sécurité:
   - Configurez WAVE_WEBHOOK_SECRET pour vérifier les signatures
   - Utilisez HTTPS en production
   - Limitez l'accès au fichier via .htaccess si nécessaire
-->
