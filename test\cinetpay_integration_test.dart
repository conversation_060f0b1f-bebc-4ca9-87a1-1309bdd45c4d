import 'package:flutter_test/flutter_test.dart';
import 'package:callitris/config/cinetpay_config.dart';
import 'package:callitris/models/cinetpay_transaction.dart';
import 'package:callitris/utils/payment_error_handler.dart';

void main() {
  group('CinetPay Configuration Tests', () {
    test('should have valid currency support', () {
      expect(CinetPayConfig.isCurrencySupported('XOF'), isTrue);
      expect(CinetPayConfig.isCurrencySupported('USD'), isTrue);
      expect(CinetPayConfig.isCurrencySupported('INVALID'), isFalse);
    });

    test('should validate amounts correctly', () {
      expect(CinetPayConfig.isAmountValid(1.0), isTrue);
      expect(CinetPayConfig.isAmountValid(0.5), isFalse);
      expect(CinetPayConfig.isAmountValid(0), isFalse);
    });

    test('should convert amounts correctly', () {
      expect(CinetPayConfig.convertToCentimes(10.50), equals(1050));
      expect(CinetPayConfig.convertFromCentimes(1050), equals(10.5));
    });

    test('should validate phone numbers', () {
      expect(CinetPayConfig.isValidPhoneNumber('+22501020304'), isTrue);
      expect(CinetPayConfig.isValidPhoneNumber('22501020304'), isTrue);
      expect(CinetPayConfig.isValidPhoneNumber('01020304'), isTrue);
      expect(CinetPayConfig.isValidPhoneNumber('invalid'), isFalse);
    });

    test('should format phone numbers correctly', () {
      expect(CinetPayConfig.formatPhoneNumber('01020304'), equals('+22501020304'));
      expect(CinetPayConfig.formatPhoneNumber('22501020304'), equals('+22501020304'));
      expect(CinetPayConfig.formatPhoneNumber('+22501020304'), equals('+22501020304'));
    });

    test('should generate unique transaction IDs', () {
      final id1 = CinetPayConfig.generateTransactionId();
      final id2 = CinetPayConfig.generateTransactionId();
      
      expect(id1, isNot(equals(id2)));
      expect(id1, startsWith('CALLITRIS_'));
      expect(id2, startsWith('CALLITRIS_'));
    });
  });

  group('CinetPay Transaction Model Tests', () {
    test('should create transaction correctly', () {
      final transaction = CinetPayTransaction(
        id: 'test_123',
        amount: 1000.0,
        currency: 'XOF',
        description: 'Test payment',
        customerName: 'John Doe',
        customerEmail: '<EMAIL>',
        createdAt: DateTime.now(),
      );

      expect(transaction.id, equals('test_123'));
      expect(transaction.amount, equals(1000.0));
      expect(transaction.currency, equals('XOF'));
      expect(transaction.status, equals(TransactionStatus.pending));
      expect(transaction.isPending, isTrue);
      expect(transaction.isCompleted, isFalse);
    });

    test('should serialize and deserialize correctly', () {
      final originalTransaction = CinetPayTransaction(
        id: 'test_123',
        amount: 1000.0,
        currency: 'XOF',
        description: 'Test payment',
        customerName: 'John Doe',
        customerEmail: '<EMAIL>',
        createdAt: DateTime.now(),
        metadata: {'orderId': '456'},
      );

      final json = originalTransaction.toJson();
      final deserializedTransaction = CinetPayTransaction.fromJson(json);

      expect(deserializedTransaction.id, equals(originalTransaction.id));
      expect(deserializedTransaction.amount, equals(originalTransaction.amount));
      expect(deserializedTransaction.currency, equals(originalTransaction.currency));
      expect(deserializedTransaction.metadata['orderId'], equals('456'));
    });

    test('should handle status changes correctly', () {
      final transaction = CinetPayTransaction(
        id: 'test_123',
        amount: 1000.0,
        currency: 'XOF',
        description: 'Test payment',
        customerName: 'John Doe',
        customerEmail: '<EMAIL>',
        createdAt: DateTime.now(),
      );

      expect(transaction.isPending, isTrue);

      final completedTransaction = transaction.copyWith(
        status: TransactionStatus.completed,
        completedAt: DateTime.now(),
      );

      expect(completedTransaction.isCompleted, isTrue);
      expect(completedTransaction.isPending, isFalse);
      expect(completedTransaction.duration, isNotNull);
    });

    test('should extract metadata correctly', () {
      final transaction = CinetPayTransaction(
        id: 'test_123',
        amount: 1000.0,
        currency: 'XOF',
        description: 'Test payment',
        customerName: 'John Doe',
        customerEmail: '<EMAIL>',
        createdAt: DateTime.now(),
        metadata: {
          'type': 'versement',
          'orderId': '456',
          'packageId': '789',
        },
      );

      expect(transaction.type, equals(TransactionType.versement));
      expect(transaction.orderId, equals('456'));
      expect(transaction.packageId, equals('789'));
    });
  });

  group('Payment Error Handler Tests', () {
    test('should map error codes correctly', () {
      final error1 = {'code': '01', 'message': 'Payment refused'};
      final error2 = {'code': '02', 'message': 'Insufficient funds'};
      final error3 = {'code': '99', 'message': 'Unknown error'};

      expect(PaymentErrorHandler.getErrorMessage(error1), contains('refusé'));
      expect(PaymentErrorHandler.getErrorMessage(error2), contains('insuffisants'));
      expect(PaymentErrorHandler.getErrorMessage(error3), equals('Unknown error'));
    });

    test('should handle string errors correctly', () {
      expect(
        PaymentErrorHandler.getErrorMessage('insufficient funds'),
        equals(CinetPayConfig.errorMessages['INSUFFICIENT_FUNDS']),
      );
      expect(
        PaymentErrorHandler.getErrorMessage('invalid phone number'),
        equals(CinetPayConfig.errorMessages['INVALID_PHONE']),
      );
      expect(
        PaymentErrorHandler.getErrorMessage('network error'),
        equals(CinetPayConfig.errorMessages['NETWORK_ERROR']),
      );
    });

    test('should identify retryable errors correctly', () {
      expect(PaymentErrorHandler.isRetryableError({'code': '01'}), isTrue); // Refusé - retryable
      expect(PaymentErrorHandler.isRetryableError({'code': '02'}), isFalse); // Fonds insuffisants - non retryable
      expect(PaymentErrorHandler.isRetryableError({'code': '06'}), isTrue); // Erreur réseau - retryable
      
      expect(PaymentErrorHandler.isRetryableError('network error'), isTrue);
      expect(PaymentErrorHandler.isRetryableError('insufficient funds'), isFalse);
      expect(PaymentErrorHandler.isRetryableError('cancelled by user'), isFalse);
    });
  });

  group('Transaction History Service Tests', () {
    // Note: Ces tests nécessiteraient un mock de SharedPreferences
    // pour fonctionner correctement dans un environnement de test
    
    test('should handle empty transaction list', () async {
      // Mock test - dans un vrai test, on mockerait SharedPreferences
      expect(true, isTrue); // Placeholder
    });
  });

  group('Integration Validation Tests', () {
    test('should have all required configuration', () {
      // Vérifier que toutes les configurations nécessaires sont présentes
      expect(CinetPayConfig.apiKey, isNotEmpty);
      expect(CinetPayConfig.siteId, isNotEmpty);
      expect(CinetPayConfig.defaultCurrency, isNotEmpty);
      expect(CinetPayConfig.minimumAmount, greaterThan(0));
      expect(CinetPayConfig.apiTimeout, greaterThan(0));
    });

    test('should have all required error messages', () {
      final requiredMessages = [
        'PAYMENT_FAILED',
        'PAYMENT_CANCELLED',
        'INSUFFICIENT_FUNDS',
        'INVALID_PHONE',
        'NETWORK_ERROR',
        'TIMEOUT',
        'UNKNOWN_ERROR',
      ];

      for (final key in requiredMessages) {
        expect(CinetPayConfig.errorMessages.containsKey(key), isTrue);
        expect(CinetPayConfig.errorMessages[key], isNotEmpty);
      }
    });

    test('should have all required success messages', () {
      final requiredMessages = [
        'PAYMENT_SUCCESS',
        'PAYMENT_PENDING',
      ];

      for (final key in requiredMessages) {
        expect(CinetPayConfig.successMessages.containsKey(key), isTrue);
        expect(CinetPayConfig.successMessages[key], isNotEmpty);
      }
    });

    test('should support all required payment methods', () {
      expect(CinetPayConfig.availablePaymentMethods, contains('MOBILE_MONEY'));
      expect(CinetPayConfig.availablePaymentMethods, isNotEmpty);
    });

    test('should have valid currency configuration', () {
      expect(CinetPayConfig.supportedCurrencies, isNotEmpty);
      expect(CinetPayConfig.supportedCurrencies.containsKey('XOF'), isTrue);
      expect(CinetPayConfig.isCurrencySupported(CinetPayConfig.defaultCurrency), isTrue);
    });
  });
}
