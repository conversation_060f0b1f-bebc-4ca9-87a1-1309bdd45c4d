import 'package:flutter/material.dart';

class AppTheme {
  static final color = AppColors();
  
  static ThemeData get lightTheme {
    return ThemeData(
      useMaterial3: true,
      colorScheme: ColorScheme.fromSeed(
        seedColor: color.primaryColor,
        primary: color.primaryColor,
        secondary: color.secondaryColor,
        background: Colors.white,
        surface: Colors.white,
        brightness: Brightness.light,
      ),
      scaffoldBackgroundColor: Colors.white,
      appBarTheme: AppBarTheme(
        backgroundColor: Colors.white,
        elevation: 0,
        centerTitle: true,
        titleTextStyle: TextStyle(
          color: color.textColor,
          fontSize: 18,
          fontWeight: FontWeight.w600,
        ),
        iconTheme: IconThemeData(color: color.textColor),
      ),
      // cardTheme: CardTheme(
      //   elevation: 0,
      //   shape: RoundedRectangleBorder(
      //     borderRadius: BorderRadius.circular(16),
      //     side: BorderSide(color: Colors.grey.shade200, width: 1),
      //   ),
      //   color: Colors.white,
      // ),
      elevatedButtonTheme: ElevatedButtonThemeData(
        style: ElevatedButton.styleFrom(
          backgroundColor: color.primaryColor,
          foregroundColor: Colors.white,
          elevation: 0,
          padding: const EdgeInsets.symmetric(vertical: 16, horizontal: 24),
          shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
          textStyle: const TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.w600,
          ),
        ),
      ),
      textTheme: TextTheme(
        displayLarge: TextStyle(
          fontSize: 28,
          fontWeight: FontWeight.bold,
          color: color.textColor,
        ),
        titleLarge: TextStyle(
          fontSize: 20,
          fontWeight: FontWeight.w600,
          color: color.textColor,
        ),
        bodyLarge: TextStyle(
          fontSize: 16,
          color: color.textColor,
        ),
        bodyMedium: TextStyle(
          fontSize: 14,
          color: color.textColor,
        ),
      ),
    );
  }
}

class AppColors {
  // Palette inspirée de Djamo
  final Color primaryColor = const Color(0xFF0066F5);
  final Color secondaryColor = const Color(0xFF00D9F5);
  final Color accentColor = const Color(0xFFFFA500);
  final Color textColor = const Color(0xFF1A1A1A);
  final Color lightGrey = const Color(0xFFF5F5F5);
  final Color mediumGrey = const Color(0xFFE0E0E0);
  final Color brunGris = const Color(0xFF757575);
  final Color orangeColor = const Color(0xFFFF9500);
  final Color greenColor = const Color(0xFF00C853);
}