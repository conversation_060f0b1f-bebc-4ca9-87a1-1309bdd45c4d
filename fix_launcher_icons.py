#!/usr/bin/env python3
"""
Script pour créer des icônes de launcher temporaires pour Flutter
Résout le problème de compilation Android avec des icônes corrompues
"""

import os
from PIL import Image, ImageDraw

def create_launcher_icon(size, output_path):
    """Créer une icône de launcher simple"""
    # Créer une image avec fond bleu
    img = Image.new('RGBA', (size, size), (33, 150, 243, 255))  # Bleu Material
    draw = ImageDraw.Draw(img)
    
    # Dessiner un cercle blanc au centre
    margin = size // 4
    draw.ellipse([margin, margin, size - margin, size - margin], 
                fill=(255, 255, 255, 255), outline=None)
    
    # Dessiner la lettre "C" pour Callitris
    font_size = size // 3
    text_x = size // 2 - font_size // 4
    text_y = size // 2 - font_size // 2
    
    # Simuler une police en dessinant des rectangles pour former un "C"
    c_width = font_size // 2
    c_height = font_size
    stroke_width = max(2, size // 20)
    
    # Partie haute du C
    draw.rectangle([text_x, text_y, text_x + c_width, text_y + stroke_width], 
                  fill=(33, 150, 243, 255))
    
    # Partie gauche du C
    draw.rectangle([text_x, text_y, text_x + stroke_width, text_y + c_height], 
                  fill=(33, 150, 243, 255))
    
    # Partie basse du C
    draw.rectangle([text_x, text_y + c_height - stroke_width, text_x + c_width, text_y + c_height], 
                  fill=(33, 150, 243, 255))
    
    # Sauvegarder l'image
    os.makedirs(os.path.dirname(output_path), exist_ok=True)
    img.save(output_path, 'PNG')
    print(f"✅ Icône créée: {output_path} ({size}x{size})")

def main():
    """Créer toutes les icônes de launcher nécessaires"""
    print("🔧 Création des icônes de launcher pour Android...\n")
    
    # Définir les tailles d'icônes pour chaque densité
    icon_sizes = {
        'mipmap-mdpi': 48,
        'mipmap-hdpi': 72,
        'mipmap-xhdpi': 96,
        'mipmap-xxhdpi': 144,
        'mipmap-xxxhdpi': 192,
    }
    
    base_path = "android/app/src/main/res"
    
    for folder, size in icon_sizes.items():
        output_path = os.path.join(base_path, folder, "ic_launcher.png")
        create_launcher_icon(size, output_path)
    
    print(f"\n🎉 Toutes les icônes ont été créées avec succès !")
    print("📱 Les icônes temporaires ont un design simple avec:")
    print("   • Fond bleu Material Design")
    print("   • Cercle blanc au centre")
    print("   • Lettre 'C' pour Callitris")
    print("\n💡 Conseil: Remplacez ces icônes par le vrai logo de l'app plus tard")
    print("   Utilisez un outil comme flutter_launcher_icons pour automatiser")

if __name__ == "__main__":
    try:
        main()
    except ImportError:
        print("❌ Erreur: PIL (Pillow) n'est pas installé")
        print("📦 Installez-le avec: pip install Pillow")
        print("\n🔄 Alternative: Utilisez la solution manuelle ci-dessous")
        print("   Copiez des icônes PNG valides dans les dossiers mipmap-*")
