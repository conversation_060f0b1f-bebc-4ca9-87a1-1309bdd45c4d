import 'dart:io';
import 'dart:typed_data';
import 'dart:ui' as ui;
import 'package:flutter/material.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:path_provider/path_provider.dart';

/// Service optimisé pour la gestion des images
class ImageService {
  static const String _tag = '[ImageService]';
  static ImageService? _instance;
  static ImageService get instance => _instance ??= ImageService._();

  ImageService._();

  // Cache des images redimensionnées
  final Map<String, ui.Image> _resizedImageCache = {};
  
  // Tailles d'images prédéfinies pour l'optimisation
  static const Map<String, Size> imageSizes = {
    'thumbnail': Size(150, 150),
    'small': Size(300, 300),
    'medium': Size(600, 600),
    'large': Size(1200, 1200),
  };

  /// Widget d'image optimisé avec cache et redimensionnement
  Widget optimizedImage({
    required String imageUrl,
    double? width,
    double? height,
    BoxFit fit = BoxFit.cover,
    String? placeholder,
    String? errorImage,
    bool enableMemoryCache = true,
    bool enableDiskCache = true,
    Duration? cacheMaxAge,
    String sizeCategory = 'medium',
  }) {
    // Générer une clé de cache unique
    final cacheKey = _generateCacheKey(imageUrl, width, height, sizeCategory);
    
    return CachedNetworkImage(
      imageUrl: imageUrl,
      width: width,
      height: height,
      fit: fit,
      cacheKey: cacheKey,
      memCacheWidth: _getOptimalWidth(width, sizeCategory),
      memCacheHeight: _getOptimalHeight(height, sizeCategory),
      maxWidthDiskCache: _getOptimalWidth(width, sizeCategory),
      maxHeightDiskCache: _getOptimalHeight(height, sizeCategory),
      placeholder: placeholder != null 
        ? (context, url) => _buildPlaceholder(placeholder, width, height)
        : (context, url) => _buildDefaultPlaceholder(width, height),
      errorWidget: errorImage != null
        ? (context, url, error) => _buildErrorWidget(errorImage, width, height)
        : (context, url, error) => _buildDefaultErrorWidget(width, height),
      fadeInDuration: const Duration(milliseconds: 200),
      fadeOutDuration: const Duration(milliseconds: 100),
    );
  }

  /// Widget d'image avec lazy loading pour les listes
  Widget lazyImage({
    required String imageUrl,
    double? width,
    double? height,
    BoxFit fit = BoxFit.cover,
    String sizeCategory = 'small',
  }) {
    return LayoutBuilder(
      builder: (context, constraints) {
        // Calculer la taille optimale basée sur les contraintes
        final optimalWidth = width ?? constraints.maxWidth;
        final optimalHeight = height ?? constraints.maxHeight;
        
        return optimizedImage(
          imageUrl: imageUrl,
          width: optimalWidth,
          height: optimalHeight,
          fit: fit,
          sizeCategory: sizeCategory,
        );
      },
    );
  }

  /// Précharge une image en arrière-plan
  Future<void> preloadImage(String imageUrl, {String sizeCategory = 'medium'}) async {
    try {
      final cacheKey = _generateCacheKey(imageUrl, null, null, sizeCategory);
      await CachedNetworkImage.evictFromCache(cacheKey);
      
      // Précharger avec la taille optimale
      final size = imageSizes[sizeCategory] ?? imageSizes['medium']!;
      await precacheImage(
        CachedNetworkImageProvider(
          imageUrl,
          cacheKey: cacheKey,
          maxWidth: size.width.toInt(),
          maxHeight: size.height.toInt(),
        ),
        NavigationService.navigatorKey.currentContext!,
      );
    } catch (e) {
      debugPrint('$_tag: Erreur lors du préchargement de $imageUrl: $e');
    }
  }

  /// Précharge plusieurs images en parallèle
  Future<void> preloadImages(
    List<String> imageUrls, {
    String sizeCategory = 'medium',
    int concurrency = 3,
  }) async {
    final futures = <Future<void>>[];
    
    for (int i = 0; i < imageUrls.length; i += concurrency) {
      final batch = imageUrls.skip(i).take(concurrency);
      futures.addAll(
        batch.map((url) => preloadImage(url, sizeCategory: sizeCategory)),
      );
      
      // Attendre le batch actuel avant de passer au suivant
      await Future.wait(futures);
      futures.clear();
    }
  }

  /// Nettoie le cache des images
  Future<void> clearImageCache() async {
    try {
      await CachedNetworkImage.evictFromCache('');
      _resizedImageCache.clear();
      debugPrint('$_tag: Cache des images nettoyé');
    } catch (e) {
      debugPrint('$_tag: Erreur lors du nettoyage du cache: $e');
    }
  }

  /// Obtient la taille du cache des images
  Future<int> getCacheSize() async {
    try {
      final directory = await getTemporaryDirectory();
      final cacheDir = Directory('${directory.path}/libCachedImageData');
      
      if (!await cacheDir.exists()) return 0;
      
      int totalSize = 0;
      await for (final entity in cacheDir.list(recursive: true)) {
        if (entity is File) {
          totalSize += await entity.length();
        }
      }
      
      return totalSize;
    } catch (e) {
      debugPrint('$_tag: Erreur lors du calcul de la taille du cache: $e');
      return 0;
    }
  }

  /// Génère une clé de cache unique
  String _generateCacheKey(String imageUrl, double? width, double? height, String sizeCategory) {
    final size = imageSizes[sizeCategory] ?? imageSizes['medium']!;
    final w = width?.toInt() ?? size.width.toInt();
    final h = height?.toInt() ?? size.height.toInt();
    return '${imageUrl}_${w}x$h';
  }

  /// Obtient la largeur optimale pour le cache mémoire
  int? _getOptimalWidth(double? width, String sizeCategory) {
    if (width != null) return (width * 2).toInt(); // 2x pour les écrans haute densité
    
    final size = imageSizes[sizeCategory] ?? imageSizes['medium']!;
    return (size.width * 2).toInt();
  }

  /// Obtient la hauteur optimale pour le cache mémoire
  int? _getOptimalHeight(double? height, String sizeCategory) {
    if (height != null) return (height * 2).toInt();
    
    final size = imageSizes[sizeCategory] ?? imageSizes['medium']!;
    return (size.height * 2).toInt();
  }

  /// Construit un placeholder par défaut
  Widget _buildDefaultPlaceholder(double? width, double? height) {
    return Container(
      width: width,
      height: height,
      decoration: BoxDecoration(
        color: Colors.grey[200],
        borderRadius: BorderRadius.circular(8),
      ),
      child: const Center(
        child: SizedBox(
          width: 24,
          height: 24,
          child: CircularProgressIndicator(strokeWidth: 2),
        ),
      ),
    );
  }

  /// Construit un placeholder personnalisé
  Widget _buildPlaceholder(String placeholder, double? width, double? height) {
    return Container(
      width: width,
      height: height,
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(8),
      ),
      child: Image.asset(
        placeholder,
        fit: BoxFit.cover,
        width: width,
        height: height,
      ),
    );
  }

  /// Construit un widget d'erreur par défaut
  Widget _buildDefaultErrorWidget(double? width, double? height) {
    return Container(
      width: width,
      height: height,
      decoration: BoxDecoration(
        color: Colors.grey[300],
        borderRadius: BorderRadius.circular(8),
      ),
      child: const Center(
        child: Icon(
          Icons.broken_image_outlined,
          color: Colors.grey,
          size: 32,
        ),
      ),
    );
  }

  /// Construit un widget d'erreur personnalisé
  Widget _buildErrorWidget(String errorImage, double? width, double? height) {
    return Container(
      width: width,
      height: height,
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(8),
      ),
      child: Image.asset(
        errorImage,
        fit: BoxFit.cover,
        width: width,
        height: height,
      ),
    );
  }
}

/// Service de navigation pour accéder au contexte global
class NavigationService {
  static final GlobalKey<NavigatorState> navigatorKey = GlobalKey<NavigatorState>();
}
