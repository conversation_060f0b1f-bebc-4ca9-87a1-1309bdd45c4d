import 'dart:convert';
import 'package:http/http.dart' as http;
import '../config/api_config.dart';

class CarnetService {
  static Future<Map<String, dynamic>> getCarnetCategories() async {
    try {
      final response = await http.get(
        Uri.parse(ApiConfig.getCarnetCategoriesEndpoint),
      ).timeout(const Duration(seconds: 15));
      
      print('Réponse API carnets: ${response.body}');
      
      if (response.statusCode == 200) {
        final jsonResponse = jsonDecode(response.body);
        return {
          'success': true,
          'data': jsonResponse,
        };
      } else {
        return {
          'success': false,
          'message': 'Erreur serveur: ${response.statusCode}',
        };
      }
    } catch (e) {
      print('Exception dans getCarnetCategories: $e');
      return {
        'success': false,
        'message': 'Erreur de connexion: $e',
      };
    }
  }
  
  static Future<Map<String, dynamic>> getCarnetElements(String carnetId) async {
    try {
      final response = await http.get(
        Uri.parse('${ApiConfig.baseUrl}/kit/getKitCarnet.php?livret_id=$carnetId'),
      ).timeout(const Duration(seconds: 15));
      
      print('Réponse API éléments carnet: ${response.body}');
      
      if (response.statusCode == 200) {
        final jsonResponse = jsonDecode(response.body);
        return {
          'success': true,
          'data': jsonResponse,
        };
      } else {
        return {
          'success': false,
          'message': 'Erreur serveur: ${response.statusCode}',
        };
      }
    } catch (e) {
      print('Exception dans getCarnetElements: $e'); // Log pour déboguer
      return {
        'success': false,
        'message': 'Erreur de connexion: $e',
      };
    }
  }

    // Récupérer les détails d'un carnet spécifique
  static Future<Map<String, dynamic>> getCarnetDetails(String carnetId) async {
    try {
      final response = await http.get(
        Uri.parse('${ApiConfig.baseUrl}/carnet/get_carnet_details.php?id_carnet=$carnetId'),
      ).timeout(const Duration(seconds: 15));
      
      print('Réponse API détails carnet: ${response.body}'); // Log pour déboguer
      
      if (response.statusCode == 200) {
        final jsonResponse = jsonDecode(response.body);
        return {
          'success': true,
          'data': jsonResponse,
        };
      } else {
        return {
          'success': false,
          'message': 'Erreur serveur: ${response.statusCode}',
        };
      }
    } catch (e) {
      print('Exception dans getCarnetDetails: $e'); // Log pour déboguer
      return {
        'success': false,
        'message': 'Erreur de connexion: $e',
      };
    }
  }
  
  // Souscrire à un carnet
  static Future<Map<String, dynamic>> subscribeToCarnet(String carnetId, String userId) async {
    try {
      final response = await http.post(
        Uri.parse(ApiConfig.subscribeToCarnetEndpoint),
        headers: {
          'Content-Type': 'application/json',
        },
        body: jsonEncode({
          'id_carnet': carnetId,
          'id_client': userId,
        }),
      ).timeout(const Duration(seconds: 15));
      
      if (response.statusCode == 200) {
        final jsonResponse = jsonDecode(response.body);
        return {
          'success': true,
          'data': jsonResponse,
        };
      } else {
        return {
          'success': false,
          'message': 'Erreur serveur: ${response.statusCode}',
        };
      }
    } catch (e) {
      return {
        'success': false,
        'message': 'Erreur de connexion: $e',
      };
    }
  }
}