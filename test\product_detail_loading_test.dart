import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:callitris/screens/boutique/product_detail_screen.dart';

void main() {
  group('ProductDetailScreen Loading Tests', () {
    testWidgets('Should show global loading overlay when processing', (WidgetTester tester) async {
      // Créer un produit de test
      final testProduct = {
        'id': '1',
        'name': 'Test Product',
        'image_cat': 'test.jpg',
        'journalier_cat': 1000,
        'nombre_jour_cat': 30,
        'description_cat': 'Test description',
      };

      // Construire le widget
      await tester.pumpWidget(
        MaterialApp(
          home: ProductDetailScreen(product: testProduct),
        ),
      );

      // Vérifier que l'overlay de chargement n'est pas visible initialement
      expect(find.text('Actions bloquées'), findsNothing);

      // Simuler un tap sur le bouton de commande
      final orderButton = find.text('Commander maintenant');
      expect(orderButton, findsOneWidget);

      await tester.tap(orderButton);
      await tester.pump();

      // Vérifier que l'overlay de chargement est maintenant visible
      expect(find.text('Actions bloquées'), findsOneWidget);
      expect(find.byType(CircularProgressIndicator), findsWidgets);
    });

    testWidgets('Should block all actions when global loading is active', (WidgetTester tester) async {
      final testProduct = {
        'id': '1',
        'name': 'Test Product',
        'image_cat': 'test.jpg',
        'journalier_cat': 1000,
        'nombre_jour_cat': 30,
        'description_cat': 'Test description',
      };

      await tester.pumpWidget(
        MaterialApp(
          home: ProductDetailScreen(product: testProduct),
        ),
      );

      // Simuler l'activation du chargement global
      final orderButton = find.text('Commander maintenant');
      await tester.tap(orderButton);
      await tester.pump();

      // Vérifier que les boutons sont désactivés
      final backButton = find.byIcon(Icons.arrow_back_ios_rounded);
      final favoriteButton = find.byIcon(Icons.favorite_border_rounded);
      final shareButton = find.byIcon(Icons.share_rounded);

      // Ces boutons devraient être désactivés (onPressed = null)
      expect(backButton, findsOneWidget);
      expect(favoriteButton, findsOneWidget);
      expect(shareButton, findsOneWidget);
    });
  });
}
