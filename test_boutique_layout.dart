/// Script de test pour vérifier le nouveau layout de la boutique
void main() {
  print('🧪 Test du nouveau layout de la boutique...\n');
  
  // Simulation des données produits
  final List<Map<String, dynamic>> sampleProducts = [
    {
      'id': '1',
      'name': 'Smartphone Samsung Galaxy A54',
      'price': 250000,
      'dailyPrice': 12500,
      'imageUrl': 'https://example.com/phone.jpg',
      'inStock': true,
    },
    {
      'id': '2',
      'name': 'MacBook Pro 13"',
      'price': 800000,
      'dailyPrice': 40000,
      'imageUrl': 'https://example.com/laptop.jpg',
      'inStock': true,
    },
    {
      'id': '3',
      'name': 'Réfrigérateur Samsung 350L',
      'price': 320000,
      'dailyPrice': 16000,
      'imageUrl': 'https://example.com/fridge.jpg',
      'inStock': false,
    },
    {
      'id': '4',
      'name': 'Télévision LG 55" 4K',
      'price': 450000,
      'dailyPrice': 22500,
      'imageUrl': 'https://example.com/tv.jpg',
      'inStock': true,
    },
  ];
  
  print('📊 Analyse du nouveau format d\'affichage:');
  print('');
  
  print('🔄 AVANT (format détaillé):');
  print('   • Image du produit (120x120px)');
  print('   • Nom complet du produit');
  print('   • Statut stock (En stock/Rupture)');
  print('   • Prix total formaté');
  print('   • Prix journalier');
  print('   • Bouton "Voir les détails"');
  print('   • Layout: Liste verticale (ListView)');
  print('');
  
  print('✨ APRÈS (format simplifié):');
  print('   • Image du produit (taille flexible)');
  print('   • Prix total uniquement');
  print('   • Layout: Grille 2 colonnes (GridView)');
  print('   • Ratio de carte: 0.8 (rectangulaire)');
  print('   • Espacement: 12px entre les cartes');
  print('');
  
  print('📱 Avantages du nouveau format:');
  print('   ✅ Plus d\'espace pour les images');
  print('   ✅ Affichage plus visuel et moderne');
  print('   ✅ Plus de produits visibles à l\'écran');
  print('   ✅ Navigation plus rapide');
  print('   ✅ Focus sur l\'essentiel (image + prix)');
  print('   ✅ Meilleure expérience mobile');
  print('');
  
  print('🎨 Détails techniques:');
  print('   • GridView avec crossAxisCount: 2');
  print('   • childAspectRatio: 0.8');
  print('   • crossAxisSpacing: 12px');
  print('   • mainAxisSpacing: 12px');
  print('   • Image: flex 3 (75% de la hauteur)');
  print('   • Prix: flex 1 (25% de la hauteur)');
  print('');
  
  print('💰 Exemples de prix formatés:');
  for (final product in sampleProducts) {
    final formattedPrice = _formatPrice(product['price']);
    print('   • ${product['name']}: $formattedPrice FCFA');
  }
  
  print('');
  print('🏁 Le nouveau layout est optimisé pour une expérience d\'achat rapide et visuelle !');
}

// Formater le prix avec des espaces comme séparateurs de milliers
String _formatPrice(dynamic price) {
  final int priceInt = price is int ? price : price.toInt();
  final String priceString = priceInt.toString();
  final StringBuffer result = StringBuffer();

  for (int i = 0; i < priceString.length; i++) {
    if (i > 0 && (priceString.length - i) % 3 == 0) {
      result.write(' ');
    }
    result.write(priceString[i]);
  }

  return result.toString();
}
