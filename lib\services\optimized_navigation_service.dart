import 'package:flutter/material.dart';
import 'package:callitris/utils/optimized_animations.dart';

/// Service de navigation optimisé avec cache et debouncing
class OptimizedNavigationService {
  static final GlobalKey<NavigatorState> navigatorKey =
      GlobalKey<NavigatorState>();

  static NavigatorState? get navigator => navigatorKey.currentState;
  static BuildContext? get context => navigatorKey.currentContext;

  // Cache des routes pour éviter les reconstructions
  static final Map<String, Widget Function(BuildContext)> _routeCache = {};

  // Debouncing pour éviter les doubles taps
  static DateTime? _lastNavigationTime;
  static const Duration _navigationDebounce = Duration(milliseconds: 500);

  /// Navigation optimisée avec animation personnalisée
  static Future<T?> pushOptimized<T extends Object?>(
    Widget page, {
    AnimationType animationType = AnimationType.slideFromRight,
    Duration duration = OptimizedAnimations.normal,
    RouteSettings? settings,
  }) {
    return navigator!.push<T>(
      OptimizedAnimations.createPageRoute<T>(
        page: page,
        type: animationType,
        duration: duration,
        settings: settings,
      ),
    );
  }

  /// Navigation de remplacement optimisée
  static Future<T?> pushReplacementOptimized<T extends Object?, TO extends Object?>(
    Widget page, {
    AnimationType animationType = AnimationType.slideFromRight,
    Duration duration = OptimizedAnimations.normal,
    RouteSettings? settings,
    TO? result,
  }) {
    return navigator!.pushReplacement<T, TO>(
      OptimizedAnimations.createPageRoute<T>(
        page: page,
        type: animationType,
        duration: duration,
        settings: settings,
      ),
      result: result,
    );
  }

  /// Navigation avec nettoyage de la pile optimisée
  static Future<T?> pushAndClearStackOptimized<T extends Object?>(
    Widget page, {
    AnimationType animationType = AnimationType.slideFromRight,
    Duration duration = OptimizedAnimations.normal,
    RouteSettings? settings,
  }) {
    return navigator!.pushAndRemoveUntil<T>(
      OptimizedAnimations.createPageRoute<T>(
        page: page,
        type: animationType,
        duration: duration,
        settings: settings,
      ),
      (route) => false,
    );
  }

  /// Navigation avec debouncing pour éviter les doubles taps
  static Future<T?> pushDebouncedOptimized<T extends Object?>(
    Widget page, {
    AnimationType animationType = AnimationType.slideFromRight,
    Duration duration = OptimizedAnimations.normal,
    RouteSettings? settings,
  }) {
    final now = DateTime.now();
    if (_lastNavigationTime != null &&
        now.difference(_lastNavigationTime!) < _navigationDebounce) {
      return Future.value(null);
    }

    _lastNavigationTime = now;
    return pushOptimized<T>(
      page,
      animationType: animationType,
      duration: duration,
      settings: settings,
    );
  }

  /// Navigation modale optimisée
  static Future<T?> showOptimizedModalBottomSheet<T>({
    required Widget child,
    bool isScrollControlled = true,
    bool enableDrag = true,
    bool isDismissible = true,
    Color? backgroundColor,
    double? elevation,
    ShapeBorder? shape,
  }) {
    return showModalBottomSheet<T>(
      context: context!,
      isScrollControlled: isScrollControlled,
      enableDrag: enableDrag,
      isDismissible: isDismissible,
      backgroundColor: backgroundColor ?? Colors.transparent,
      elevation: elevation ?? 0,
      shape: shape,
      builder: (context) => child,
    );
  }

  /// Dialog optimisé avec animation
  static Future<T?> showOptimizedDialog<T>({
    required Widget child,
    bool barrierDismissible = true,
    Color? barrierColor,
    String? barrierLabel,
    Duration transitionDuration = OptimizedAnimations.normal,
  }) {
    return showGeneralDialog<T>(
      context: context!,
      barrierDismissible: barrierDismissible,
      barrierColor: barrierColor ?? Colors.black54,
      barrierLabel: barrierLabel,
      transitionDuration: transitionDuration,
      pageBuilder: (context, animation, secondaryAnimation) => child,
      transitionBuilder: (context, animation, secondaryAnimation, child) {
        return ScaleTransition(
          scale: Tween<double>(
            begin: 0.8,
            end: 1.0,
          ).animate(CurvedAnimation(
            parent: animation,
            curve: OptimizedAnimations.easeOutQuart,
          )),
          child: FadeTransition(
            opacity: animation,
            child: child,
          ),
        );
      },
    );
  }

  /// Snackbar optimisé
  static void showOptimizedSnackBar({
    required String message,
    Duration duration = const Duration(seconds: 3),
    Color? backgroundColor,
    Color? textColor,
    IconData? icon,
    VoidCallback? action,
    String? actionLabel,
  }) {
    final scaffoldMessenger = ScaffoldMessenger.of(context!);
    
    // Supprimer le snackbar précédent s'il existe
    scaffoldMessenger.hideCurrentSnackBar();
    
    scaffoldMessenger.showSnackBar(
      SnackBar(
        content: Row(
          children: [
            if (icon != null) ...[
              Icon(icon, color: textColor ?? Colors.white),
              const SizedBox(width: 12),
            ],
            Expanded(
              child: Text(
                message,
                style: TextStyle(color: textColor ?? Colors.white),
              ),
            ),
          ],
        ),
        duration: duration,
        backgroundColor: backgroundColor,
        action: action != null && actionLabel != null
            ? SnackBarAction(
                label: actionLabel,
                onPressed: action,
                textColor: textColor ?? Colors.white,
              )
            : null,
        behavior: SnackBarBehavior.floating,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(8),
        ),
      ),
    );
  }

  /// Précharge une route pour une navigation plus rapide
  static void preloadRoute(String routeName, Widget Function(BuildContext) builder) {
    _routeCache[routeName] = builder;
  }

  /// Récupère une route préchargée
  static Widget Function(BuildContext)? getCachedRoute(String routeName) {
    return _routeCache[routeName];
  }

  /// Nettoie le cache des routes
  static void clearRouteCache() {
    _routeCache.clear();
  }

  /// Navigation standard
  static void pop<T extends Object?>([T? result]) {
    return navigator!.pop<T>(result);
  }

  static bool canPop() {
    return navigator!.canPop();
  }

  /// Navigation vers une route nommée
  static Future<T?> pushNamed<T extends Object?>(
    String routeName, {
    Object? arguments,
  }) {
    return navigator!.pushNamed<T>(routeName, arguments: arguments);
  }

  /// Remplacement par une route nommée
  static Future<T?> pushReplacementNamed<T extends Object?, TO extends Object?>(
    String routeName, {
    Object? arguments,
    TO? result,
  }) {
    return navigator!.pushReplacementNamed<T, TO>(
      routeName,
      arguments: arguments,
      result: result,
    );
  }

  /// Navigation avec nettoyage de la pile vers une route nommée
  static Future<T?> pushNamedAndClearStack<T extends Object?>(
    String routeName, {
    Object? arguments,
  }) {
    return navigator!.pushNamedAndRemoveUntil<T>(
      routeName,
      (route) => false,
      arguments: arguments,
    );
  }

  /// Méthodes de convenance pour les animations courantes
  static Future<T?> slideFromRight<T extends Object?>(Widget page) {
    return pushOptimized<T>(page, animationType: AnimationType.slideFromRight);
  }

  static Future<T?> slideFromBottom<T extends Object?>(Widget page) {
    return pushOptimized<T>(page, animationType: AnimationType.slideFromBottom);
  }

  static Future<T?> fadeIn<T extends Object?>(Widget page) {
    return pushOptimized<T>(page, animationType: AnimationType.fadeIn);
  }

  static Future<T?> scaleIn<T extends Object?>(Widget page) {
    return pushOptimized<T>(page, animationType: AnimationType.scaleIn);
  }

  /// Méthodes de convenance avec debouncing
  static Future<T?> slideFromRightDebounced<T extends Object?>(Widget page) {
    return pushDebouncedOptimized<T>(page, animationType: AnimationType.slideFromRight);
  }

  static Future<T?> slideFromBottomDebounced<T extends Object?>(Widget page) {
    return pushDebouncedOptimized<T>(page, animationType: AnimationType.slideFromBottom);
  }
}
