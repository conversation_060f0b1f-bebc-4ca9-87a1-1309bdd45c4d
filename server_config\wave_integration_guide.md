# Guide d'intégration Wave avec Deep Links - Callitris

## 🌊 Configuration Wave pour votre domaine

### URLs de callback à configurer dans Wave

Configurez ces URLs dans votre tableau de bord Wave :

```
Success URL: https://dev-mani.io/teams/client-api.callitris-distribution.com/callback/wave/success
Failure URL: https://dev-mani.io/teams/client-api.callitris-distribution.com/callback/wave/failure
Cancel URL:  https://dev-mani.io/teams/client-api.callitris-distribution.com/callback/wave/cancel
Notify URL:  https://dev-mani.io/teams/client-api.callitris-distribution.com/callback/wave/notify
```

## 📁 Structure des fichiers backend

Déployez ces fichiers sur votre serveur `dev-mani.io` :

```
/teams/client-api.callitris-distribution.com/
├── callback/
│   └── wave/
│       ├── wave_callback_handler.php
│       └── logs/ (dossier pour les logs)
├── .htaccess (si Apache)
└── nginx.conf (si Nginx)
```

## 🔧 Configuration serveur

### Option 1: Apache (.htaccess)

Créez un fichier `.htaccess` dans `/teams/client-api.callitris-distribution.com/` :

```apache
RewriteEngine On

# Callbacks Wave
RewriteRule ^callback/wave/success/?$ callback/wave/wave_callback_handler.php [L,QSA]
RewriteRule ^callback/wave/failure/?$ callback/wave/wave_callback_handler.php [L,QSA]
RewriteRule ^callback/wave/cancel/?$ callback/wave/wave_callback_handler.php [L,QSA]
RewriteRule ^callback/wave/notify/?$ callback/wave/wave_callback_handler.php [L,QSA]
RewriteRule ^callback/wave/return/?$ callback/wave/wave_callback_handler.php [L,QSA]

# Headers CORS
Header always set Access-Control-Allow-Origin "*"
Header always set Access-Control-Allow-Methods "GET, POST, OPTIONS"
Header always set Access-Control-Allow-Headers "Content-Type, Authorization"
```

### Option 2: Configuration Nginx

Ajoutez dans votre configuration Nginx pour `dev-mani.io` :

```nginx
location ~ ^/teams/client-api\.callitris-distribution\.com/callback/wave/(success|failure|cancel|notify|return)/?$ {
    try_files $uri /teams/client-api.callitris-distribution.com/callback/wave/wave_callback_handler.php?$query_string;
    
    fastcgi_pass unix:/var/run/php/php8.1-fpm.sock;
    fastcgi_index index.php;
    fastcgi_param SCRIPT_FILENAME $document_root$fastcgi_script_name;
    include fastcgi_params;
    
    add_header 'Access-Control-Allow-Origin' '*' always;
    add_header 'Access-Control-Allow-Methods' 'GET, POST, OPTIONS' always;
    add_header 'Access-Control-Allow-Headers' 'Content-Type, Authorization' always;
}
```

## 💾 Configuration de base de données

Modifiez le fichier `wave_callback_handler.php` avec vos credentials :

```php
// Configuration de base de données (ajoutez au début du fichier)
define('DB_HOST', 'localhost');
define('DB_NAME', 'callitris_db');
define('DB_USER', 'votre_utilisateur_db');
define('DB_PASS', 'votre_mot_de_passe_db');

// Fonction de connexion à la base de données
function getDbConnection() {
    try {
        $pdo = new PDO(
            'mysql:host=' . DB_HOST . ';dbname=' . DB_NAME . ';charset=utf8mb4',
            DB_USER,
            DB_PASS,
            [
                PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
                PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
                PDO::ATTR_EMULATE_PREPARES => false,
            ]
        );
        return $pdo;
    } catch (PDOException $e) {
        logMessage("Erreur de connexion DB: " . $e->getMessage());
        return null;
    }
}

// Mise à jour de la fonction updateOrderStatus
function updateOrderStatus($transactionId, $status, $amount, $orderId = null) {
    try {
        $pdo = getDbConnection();
        if (!$pdo) {
            return false;
        }
        
        // Requête pour mettre à jour le statut de la commande
        $sql = "UPDATE commandes SET 
                payment_status = :status, 
                payment_transaction_id = :transaction_id,
                payment_amount = :amount,
                payment_date = NOW(),
                updated_at = NOW()
                WHERE id = :order_id OR payment_reference = :transaction_id";
        
        $stmt = $pdo->prepare($sql);
        $result = $stmt->execute([
            ':status' => $status,
            ':transaction_id' => $transactionId,
            ':amount' => $amount,
            ':order_id' => $orderId
        ]);
        
        if ($result) {
            logMessage("Commande mise à jour - ID: $orderId, Transaction: $transactionId, Statut: $status");
        }
        
        return $result;
        
    } catch (Exception $e) {
        logMessage("Erreur lors de la mise à jour de la commande: " . $e->getMessage());
        return false;
    }
}
```

## 🧪 Tests de fonctionnement

### 1. Test des callbacks backend

```bash
# Test callback de succès
curl -X POST "https://dev-mani.io/teams/client-api.callitris-distribution.com/callback/wave/success" \
  -H "Content-Type: application/json" \
  -d '{
    "transaction_id": "wave_test_123",
    "status": "success",
    "amount": "1000",
    "order_id": "456",
    "currency": "XOF"
  }'

# Test callback d'échec
curl -X POST "https://dev-mani.io/teams/client-api.callitris-distribution.com/callback/wave/failure" \
  -H "Content-Type: application/json" \
  -d '{
    "transaction_id": "wave_test_124",
    "status": "failed",
    "error_message": "Insufficient funds",
    "order_id": "457"
  }'
```

### 2. Test des deep links mobile

#### Android (via ADB)
```bash
# Test succès
adb shell am start -W -a android.intent.action.VIEW \
  -d "callitris://payment/success?transaction_id=test123&amount=1000&order_id=456" \
  com.callitris.app

# Test échec
adb shell am start -W -a android.intent.action.VIEW \
  -d "callitris://payment/failure?transaction_id=test124&error_message=Insufficient+funds" \
  com.callitris.app
```

#### Test dans navigateur mobile
Ouvrez ces URLs dans le navigateur de votre téléphone :
- `callitris://payment/success?transaction_id=test123&amount=1000`
- `callitris://payment/failure?transaction_id=test124&error_message=Test+error`

## 🔍 Vérification des logs

### Logs backend
```bash
# Voir les logs Wave
tail -f /path/to/your/site/teams/client-api.callitris-distribution.com/callback/wave/logs/wave_callbacks.log

# Logs serveur web
tail -f /var/log/nginx/access.log | grep wave
tail -f /var/log/apache2/access.log | grep wave
```

### Logs mobile
Dans la console Flutter, vous devriez voir :
```
[DeepLinkService]: Deep link reçu: callitris://payment/success?...
[PaymentReturnHandler]: Paiement réussi détecté
```

## 🚀 Déploiement en production

### Checklist de déploiement

1. **Backend** :
   - [ ] Fichier `wave_callback_handler.php` déployé
   - [ ] Configuration serveur web appliquée
   - [ ] Dossier `logs/` créé avec permissions 755
   - [ ] Credentials de base de données configurés
   - [ ] Test des URLs de callback avec curl

2. **Wave** :
   - [ ] URLs de callback configurées dans le tableau de bord Wave
   - [ ] Mode sandbox/production configuré
   - [ ] Clés API Wave configurées

3. **Mobile** :
   - [ ] App compilée et installée sur les appareils de test
   - [ ] Deep links testés manuellement
   - [ ] Flux de paiement complet testé

### URLs finales pour Wave

Remplacez dans votre configuration Wave :
```
Return URL: https://dev-mani.io/teams/client-api.callitris-distribution.com/callback/wave/return
Success URL: https://dev-mani.io/teams/client-api.callitris-distribution.com/callback/wave/success
Failure URL: https://dev-mani.io/teams/client-api.callitris-distribution.com/callback/wave/failure
Cancel URL: https://dev-mani.io/teams/client-api.callitris-distribution.com/callback/wave/cancel
Webhook URL: https://dev-mani.io/teams/client-api.callitris-distribution.com/callback/wave/notify
```

## 🛠️ Personnalisation

### Modifier les messages de redirection

Dans `wave_callback_handler.php`, vous pouvez personnaliser la page HTML de redirection :

```php
// Modifier le titre et le message
$html = str_replace(
    '<h2>Retour vers Callitris</h2>',
    '<h2>Votre paiement a été traité</h2>',
    $html
);
```

### Ajouter des paramètres personnalisés

Vous pouvez ajouter des paramètres supplémentaires aux deep links :

```php
$deepLink = generateDeepLink('success', [
    'transaction_id' => $transactionId,
    'amount' => $amount,
    'order_id' => $orderId,
    'customer_id' => $customerId,
    'payment_method' => 'wave',
    'timestamp' => time()
]);
```

## 🆘 Dépannage

### Problèmes courants

1. **"Namespace not specified" (Gradle)** :
   - Utilisez `app_links` au lieu de `uni_links`
   - Exécutez `flutter clean && flutter pub get`

2. **Deep link ne s'ouvre pas** :
   - Vérifiez AndroidManifest.xml et Info.plist
   - Testez avec `adb shell am start`
   - Vérifiez que l'app est installée

3. **Callback non reçu** :
   - Vérifiez les logs serveur
   - Testez avec curl
   - Vérifiez la configuration des URLs Wave

4. **Erreur de base de données** :
   - Vérifiez les credentials dans `wave_callback_handler.php`
   - Vérifiez les permissions de base de données
   - Consultez les logs d'erreur PHP

### Commandes de debug

```bash
# Nettoyer le projet Flutter
flutter clean
flutter pub get

# Vérifier les deep links Android
adb shell dumpsys package domain-preferred-apps

# Voir les logs en temps réel
adb logcat | grep -i callitris
```

Cette configuration vous permettra d'avoir un système complet de deep links pour Wave avec votre domaine `dev-mani.io`.
