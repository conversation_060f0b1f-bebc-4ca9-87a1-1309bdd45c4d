import 'package:flutter/material.dart';
import 'package:callitris/models/cinetpay_transaction.dart';
import 'package:intl/intl.dart';

/// Widget pour afficher le statut d'une transaction CinetPay
class TransactionStatusWidget extends StatelessWidget {
  final CinetPayTransaction transaction;
  final bool showDetails;
  final VoidCallback? onTap;

  const TransactionStatusWidget({
    super.key,
    required this.transaction,
    this.showDetails = false,
    this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(12),
          border: Border.all(color: Colors.grey.shade200),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withOpacity(0.05),
              blurRadius: 4,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // En-tête avec statut
            Row(
              children: [
                _buildStatusIcon(),
                const SizedBox(width: 12),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        _getStatusText(),
                        style: TextStyle(
                          fontSize: 14,
                          fontWeight: FontWeight.w600,
                          color: _getStatusColor(),
                        ),
                      ),
                      const SizedBox(height: 2),
                      Text(
                        transaction.description,
                        style: TextStyle(
                          fontSize: 12,
                          color: Colors.grey.shade600,
                        ),
                        maxLines: 1,
                        overflow: TextOverflow.ellipsis,
                      ),
                    ],
                  ),
                ),
                Text(
                  '${transaction.amount.toStringAsFixed(0)} ${transaction.currency}',
                  style: const TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.w700,
                    color: Color(0xFF0A0A0A),
                  ),
                ),
              ],
            ),
            
            if (showDetails) ...[
              const SizedBox(height: 16),
              const Divider(height: 1),
              const SizedBox(height: 16),
              
              // Détails de la transaction
              _buildDetailRow('ID Transaction', transaction.id),
              _buildDetailRow('Date', _formatDate(transaction.createdAt)),
              
              if (transaction.completedAt != null)
                _buildDetailRow('Complétée le', _formatDate(transaction.completedAt!)),
              
              if (transaction.paymentMethod != null)
                _buildDetailRow('Méthode', transaction.paymentMethod!),
              
              if (transaction.operatorId != null)
                _buildDetailRow('Opérateur', transaction.operatorId!),
              
              if (transaction.failureReason != null) ...[
                const SizedBox(height: 8),
                Container(
                  padding: const EdgeInsets.all(12),
                  decoration: BoxDecoration(
                    color: Colors.red.withOpacity(0.1),
                    borderRadius: BorderRadius.circular(8),
                    border: Border.all(
                      color: Colors.red.withOpacity(0.3),
                    ),
                  ),
                  child: Row(
                    children: [
                      Icon(
                        Icons.error_outline,
                        color: Colors.red,
                        size: 16,
                      ),
                      const SizedBox(width: 8),
                      Expanded(
                        child: Text(
                          transaction.failureReason!,
                          style: TextStyle(
                            fontSize: 12,
                            color: Colors.red.shade700,
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              ],
              
              // Durée de la transaction si complétée
              if (transaction.duration != null) ...[
                const SizedBox(height: 8),
                Text(
                  'Durée: ${_formatDuration(transaction.duration!)}',
                  style: TextStyle(
                    fontSize: 11,
                    color: Colors.grey.shade500,
                  ),
                ),
              ],
            ],
            
            // Indicateur de tap si onTap est fourni
            if (onTap != null && !showDetails) ...[
              const SizedBox(height: 8),
              Row(
                mainAxisAlignment: MainAxisAlignment.end,
                children: [
                  Text(
                    'Voir détails',
                    style: TextStyle(
                      fontSize: 12,
                      color: const Color(0xFF4CAF50),
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                  const SizedBox(width: 4),
                  Icon(
                    Icons.arrow_forward_ios,
                    size: 12,
                    color: const Color(0xFF4CAF50),
                  ),
                ],
              ),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildStatusIcon() {
    IconData iconData;
    Color iconColor;
    
    switch (transaction.status) {
      case TransactionStatus.completed:
        iconData = Icons.check_circle;
        iconColor = const Color(0xFF4CAF50);
        break;
      case TransactionStatus.failed:
        iconData = Icons.error;
        iconColor = Colors.red;
        break;
      case TransactionStatus.cancelled:
        iconData = Icons.cancel;
        iconColor = Colors.orange;
        break;
      case TransactionStatus.processing:
        iconData = Icons.hourglass_empty;
        iconColor = Colors.blue;
        break;
      case TransactionStatus.refunded:
        iconData = Icons.undo;
        iconColor = Colors.purple;
        break;
      default:
        iconData = Icons.schedule;
        iconColor = Colors.grey;
    }
    
    return Container(
      padding: const EdgeInsets.all(8),
      decoration: BoxDecoration(
        color: iconColor.withOpacity(0.1),
        shape: BoxShape.circle,
      ),
      child: Icon(
        iconData,
        color: iconColor,
        size: 20,
      ),
    );
  }

  String _getStatusText() {
    switch (transaction.status) {
      case TransactionStatus.completed:
        return 'Paiement réussi';
      case TransactionStatus.failed:
        return 'Paiement échoué';
      case TransactionStatus.cancelled:
        return 'Paiement annulé';
      case TransactionStatus.processing:
        return 'En cours de traitement';
      case TransactionStatus.refunded:
        return 'Remboursé';
      default:
        return 'En attente';
    }
  }

  Color _getStatusColor() {
    switch (transaction.status) {
      case TransactionStatus.completed:
        return const Color(0xFF4CAF50);
      case TransactionStatus.failed:
        return Colors.red;
      case TransactionStatus.cancelled:
        return Colors.orange;
      case TransactionStatus.processing:
        return Colors.blue;
      case TransactionStatus.refunded:
        return Colors.purple;
      default:
        return Colors.grey;
    }
  }

  Widget _buildDetailRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 8),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 100,
            child: Text(
              label,
              style: TextStyle(
                fontSize: 12,
                color: Colors.grey.shade600,
                fontWeight: FontWeight.w500,
              ),
            ),
          ),
          Expanded(
            child: Text(
              value,
              style: const TextStyle(
                fontSize: 12,
                color: Color(0xFF0A0A0A),
              ),
            ),
          ),
        ],
      ),
    );
  }

  String _formatDate(DateTime date) {
    return DateFormat('dd/MM/yyyy à HH:mm').format(date);
  }

  String _formatDuration(Duration duration) {
    if (duration.inMinutes < 1) {
      return '${duration.inSeconds}s';
    } else if (duration.inHours < 1) {
      return '${duration.inMinutes}min ${duration.inSeconds % 60}s';
    } else {
      return '${duration.inHours}h ${duration.inMinutes % 60}min';
    }
  }
}

/// Widget pour afficher une liste de transactions
class TransactionListWidget extends StatelessWidget {
  final List<CinetPayTransaction> transactions;
  final Function(CinetPayTransaction)? onTransactionTap;
  final String? emptyMessage;

  const TransactionListWidget({
    super.key,
    required this.transactions,
    this.onTransactionTap,
    this.emptyMessage,
  });

  @override
  Widget build(BuildContext context) {
    if (transactions.isEmpty) {
      return Center(
        child: Padding(
          padding: const EdgeInsets.all(32),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(
                Icons.receipt_long_outlined,
                size: 64,
                color: Colors.grey.shade400,
              ),
              const SizedBox(height: 16),
              Text(
                emptyMessage ?? 'Aucune transaction trouvée',
                style: TextStyle(
                  fontSize: 16,
                  color: Colors.grey.shade600,
                ),
                textAlign: TextAlign.center,
              ),
            ],
          ),
        ),
      );
    }

    return ListView.separated(
      itemCount: transactions.length,
      separatorBuilder: (context, index) => const SizedBox(height: 12),
      itemBuilder: (context, index) {
        final transaction = transactions[index];
        return TransactionStatusWidget(
          transaction: transaction,
          onTap: onTransactionTap != null 
              ? () => onTransactionTap!(transaction)
              : null,
        );
      },
    );
  }
}
