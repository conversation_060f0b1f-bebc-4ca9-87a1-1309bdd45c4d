import 'package:flutter/material.dart';
import 'package:callitris/services/app_state_service.dart';
import 'package:connectivity_plus/connectivity_plus.dart';

/// Widget réactif pour gérer l'état global de l'application
class ReactiveAppWidget extends StatefulWidget {
  final Widget child;
  final bool showLoadingOverlay;
  final bool showErrorSnackbar;
  final bool showConnectivityStatus;
  
  const ReactiveAppWidget({
    Key? key,
    required this.child,
    this.showLoadingOverlay = true,
    this.showErrorSnackbar = true,
    this.showConnectivityStatus = true,
  }) : super(key: key);

  @override
  State<ReactiveAppWidget> createState() => _ReactiveAppWidgetState();
}

class _ReactiveAppWidgetState extends State<ReactiveAppWidget> {
  @override
  Widget build(BuildContext context) {
    return Stack(
      children: [
        widget.child,
        
        // Overlay de chargement global
        if (widget.showLoadingOverlay)
          StreamBuilder<bool>(
            stream: AppStateService.instance.isLoadingStream,
            builder: (context, snapshot) {
              final isLoading = snapshot.data ?? false;
              
              if (!isLoading) {
                return const SizedBox.shrink();
              }
              
              return Container(
                color: Colors.black.withOpacity(0.3),
                child: const Center(
                  child: Card(
                    child: Padding(
                      padding: EdgeInsets.all(20),
                      child: Column(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          CircularProgressIndicator(),
                          SizedBox(height: 16),
                          Text('Chargement...'),
                        ],
                      ),
                    ),
                  ),
                ),
              );
            },
          ),
        
        // Indicateur de connectivité
        if (widget.showConnectivityStatus)
          Positioned(
            top: 0,
            left: 0,
            right: 0,
            child: StreamBuilder<bool>(
              stream: AppStateService.instance.isConnectedStream,
              builder: (context, snapshot) {
                final isConnected = snapshot.data ?? true;
                
                if (isConnected) {
                  return const SizedBox.shrink();
                }
                
                return Container(
                  color: Colors.red,
                  padding: const EdgeInsets.symmetric(vertical: 8),
                  child: const Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Icon(Icons.wifi_off, color: Colors.white, size: 16),
                      SizedBox(width: 8),
                      Text(
                        'Pas de connexion internet',
                        style: TextStyle(color: Colors.white, fontSize: 12),
                      ),
                    ],
                  ),
                );
              },
            ),
          ),
      ],
    );
  }
}

/// Widget pour afficher les erreurs de manière réactive
class ReactiveErrorWidget extends StatefulWidget {
  final Widget? child;
  final bool showAsSnackbar;
  final Duration snackbarDuration;
  
  const ReactiveErrorWidget({
    Key? key,
    this.child,
    this.showAsSnackbar = true,
    this.snackbarDuration = const Duration(seconds: 4),
  }) : super(key: key);

  @override
  State<ReactiveErrorWidget> createState() => _ReactiveErrorWidgetState();
}

class _ReactiveErrorWidgetState extends State<ReactiveErrorWidget> {
  String? _lastError;
  
  @override
  Widget build(BuildContext context) {
    return StreamBuilder<String?>(
      stream: AppStateService.instance.errorStream,
      builder: (context, snapshot) {
        final error = snapshot.data;
        
        // Afficher la snackbar pour les nouvelles erreurs
        if (widget.showAsSnackbar && error != null && error != _lastError) {
          _lastError = error;
          WidgetsBinding.instance.addPostFrameCallback((_) {
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Row(
                  children: [
                    const Icon(Icons.error, color: Colors.white),
                    const SizedBox(width: 8),
                    Expanded(child: Text(error)),
                  ],
                ),
                backgroundColor: Colors.red,
                duration: widget.snackbarDuration,
                action: SnackBarAction(
                  label: 'Fermer',
                  textColor: Colors.white,
                  onPressed: () {
                    ScaffoldMessenger.of(context).hideCurrentSnackBar();
                    AppStateService.instance.clearError();
                  },
                ),
              ),
            );
          });
        }
        
        return widget.child ?? const SizedBox.shrink();
      },
    );
  }
}

/// Widget pour afficher l'indicateur de chargement global
class ReactiveLoadingIndicator extends StatelessWidget {
  final Widget? child;
  final bool linear;
  final Color? color;
  
  const ReactiveLoadingIndicator({
    Key? key,
    this.child,
    this.linear = true,
    this.color,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return StreamBuilder<bool>(
      stream: AppStateService.instance.isLoadingStream,
      builder: (context, snapshot) {
        final isLoading = snapshot.data ?? false;
        
        if (!isLoading) {
          return child ?? const SizedBox.shrink();
        }
        
        if (linear) {
          return LinearProgressIndicator(color: color);
        } else {
          return CircularProgressIndicator(color: color);
        }
      },
    );
  }
}

/// Widget pour afficher le statut de connectivité
class ReactiveConnectivityWidget extends StatelessWidget {
  final Widget Function(bool isConnected)? builder;
  final Widget? connectedWidget;
  final Widget? disconnectedWidget;
  
  const ReactiveConnectivityWidget({
    Key? key,
    this.builder,
    this.connectedWidget,
    this.disconnectedWidget,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return StreamBuilder<bool>(
      stream: AppStateService.instance.isConnectedStream,
      builder: (context, snapshot) {
        final isConnected = snapshot.data ?? true;
        
        if (builder != null) {
          return builder!(isConnected);
        }
        
        if (isConnected) {
          return connectedWidget ?? const SizedBox.shrink();
        } else {
          return disconnectedWidget ?? Container(
            padding: const EdgeInsets.all(8),
            color: Colors.red,
            child: const Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(Icons.wifi_off, color: Colors.white, size: 16),
                SizedBox(width: 8),
                Text(
                  'Pas de connexion',
                  style: TextStyle(color: Colors.white, fontSize: 12),
                ),
              ],
            ),
          );
        }
      },
    );
  }
}

/// Widget pour afficher la dernière mise à jour
class ReactiveLastUpdateWidget extends StatelessWidget {
  final TextStyle? textStyle;
  final String prefix;
  final String format;
  
  const ReactiveLastUpdateWidget({
    Key? key,
    this.textStyle,
    this.prefix = 'Dernière mise à jour: ',
    this.format = 'HH:mm',
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return StreamBuilder<DateTime>(
      stream: AppStateService.instance.lastUpdateStream,
      builder: (context, snapshot) {
        final lastUpdate = snapshot.data;
        
        if (lastUpdate == null) {
          return const SizedBox.shrink();
        }
        
        final formattedTime = _formatTime(lastUpdate);
        
        return Text(
          '$prefix$formattedTime',
          style: textStyle ?? TextStyle(
            fontSize: 12,
            color: Colors.grey[600],
          ),
        );
      },
    );
  }
  
  String _formatTime(DateTime dateTime) {
    final now = DateTime.now();
    final difference = now.difference(dateTime);
    
    if (difference.inMinutes < 1) {
      return 'À l\'instant';
    } else if (difference.inMinutes < 60) {
      return 'Il y a ${difference.inMinutes} min';
    } else if (difference.inHours < 24) {
      return 'Il y a ${difference.inHours}h';
    } else {
      return '${dateTime.day}/${dateTime.month} ${dateTime.hour}:${dateTime.minute.toString().padLeft(2, '0')}';
    }
  }
}

/// Widget pour gérer le pull-to-refresh global
class ReactiveRefreshWidget extends StatelessWidget {
  final Widget child;
  final Future<void> Function()? onRefresh;
  final bool enabled;
  
  const ReactiveRefreshWidget({
    Key? key,
    required this.child,
    this.onRefresh,
    this.enabled = true,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    if (!enabled) {
      return child;
    }
    
    return RefreshIndicator(
      onRefresh: onRefresh ?? () => AppStateService.instance.refreshAllData(),
      child: child,
    );
  }
}

/// Mixin pour les widgets qui ont besoin de réactivité
mixin ReactiveWidgetMixin<T extends StatefulWidget> on State<T> {
  @override
  void initState() {
    super.initState();
    // S'assurer que les services sont initialisés
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _initializeServices();
    });
  }
  
  void _initializeServices() async {
    // Initialiser les services si nécessaire
    // Cette méthode peut être surchargée dans les widgets enfants
  }
  
  /// Méthode utilitaire pour rafraîchir toutes les données
  Future<void> refreshAllData() {
    return AppStateService.instance.refreshAllData();
  }
  
  /// Méthode utilitaire pour afficher une erreur
  void showError(String message) {
    AppStateService.instance.setError(message);
  }
  
  /// Méthode utilitaire pour définir l'état de chargement
  void setLoading(bool loading) {
    AppStateService.instance.setLoading(loading);
  }
}
