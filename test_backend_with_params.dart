import 'dart:convert';
import 'dart:io';

/// Script de test pour vérifier l'envoi des nouveaux paramètres au backend
void main() async {
  print('🧪 Test de l\'API backend avec les nouveaux paramètres...\n');
  
  const String backendUrl = 'https://dev-mani.io/client-api.callitris-distribution.com/paiement/init_cinetpay.php';
  
  // Test avec tous les paramètres requis
  final testData = {
    'amount': 1000.0,
    'phone': '+22501020304',
    'commande_id': '123',
    'cle': 'test_token_123456789',
    'clientId': '456',
  };
  
  print('📤 Envoi des données complètes:');
  print('URL: $backendUrl');
  print('Données: ${jsonEncode(testData)}\n');
  
  try {
    final client = HttpClient();
    final request = await client.postUrl(Uri.parse(backendUrl));
    
    // Headers
    request.headers.set('Content-Type', 'application/json');
    request.headers.set('Accept', 'application/json');
    
    // Body
    request.add(utf8.encode(jsonEncode(testData)));
    
    final response = await request.close();
    final responseBody = await response.transform(utf8.decoder).join();
    
    print('📥 Réponse du serveur:');
    print('Status Code: ${response.statusCode}');
    print('Body: $responseBody\n');
    
    if (response.statusCode == 200) {
      try {
        final jsonResponse = jsonDecode(responseBody);
        print('✅ JSON décodé avec succès:');
        print('Success: ${jsonResponse['success']}');
        print('Message: ${jsonResponse['message']}');
        if (jsonResponse['payment_url'] != null) {
          print('Payment URL: ${jsonResponse['payment_url']}');
        }
        if (jsonResponse['transaction_id'] != null) {
          print('Transaction ID: ${jsonResponse['transaction_id']}');
        }
      } catch (e) {
        print('❌ Erreur de décodage JSON: $e');
        print('Réponse brute: $responseBody');
      }
    } else {
      print('❌ Erreur HTTP: ${response.statusCode}');
    }
    
    client.close();
    
  } catch (e) {
    print('❌ Erreur de connexion: $e');
  }
  
  print('\n🔍 Vérification des paramètres envoyés:');
  testData.forEach((key, value) {
    print('✓ $key: $value (${value.runtimeType})');
  });
  
  print('\n🏁 Test terminé.');
}
