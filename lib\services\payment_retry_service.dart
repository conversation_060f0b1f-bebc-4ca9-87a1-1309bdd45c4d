import 'dart:async';
import 'package:flutter/material.dart';
import 'package:callitris/services/cinetpay_service.dart';
import 'package:callitris/services/transaction_history_service.dart';
import 'package:callitris/models/cinetpay_transaction.dart';
import 'package:callitris/utils/payment_error_handler.dart';

/// Service pour gérer les tentatives de paiement
class PaymentRetryService {
  static const int maxRetries = 3;
  static const Duration retryDelay = Duration(seconds: 2);
  
  /// Effectue un paiement avec retry automatique
  static Future<Map<String, dynamic>> processPaymentWithRetry({
    required BuildContext context,
    required CinetPayTransaction transaction,
    int currentAttempt = 1,
  }) async {
    try {
      // Vérifier si le contexte est toujours monté
      if (!context.mounted) {
        return {
          'success': false,
          'message': 'Contexte non disponible',
        };
      }
      
      // Afficher un message de tentative si ce n'est pas la première
      if (currentAttempt > 1) {
        PaymentErrorHandler.showInfoNotification(
          context,
          'Tentative $currentAttempt/$maxRetries...',
        );
        
        // Attendre un délai avant la nouvelle tentative
        await Future.delayed(retryDelay);
      }
      
      // Mettre à jour le statut de la transaction
      transaction.status = TransactionStatus.processing;
      await TransactionHistoryService.saveTransaction(transaction);
      
      // Vérifier le contexte avant l'appel
      if (!context.mounted) {
        return {
          'success': false,
          'message': 'Contexte non disponible',
        };
      }

      // Effectuer le paiement
      final customerPhone = transaction.customerPhone ?? '';
      final result = await CinetPayService.processPayment(
        context: context,
        amount: transaction.amount,
        customerPhone: customerPhone,
        transaction: transaction,
      );
      
      if (result['success']) {
        // Succès
        transaction.status = TransactionStatus.completed;
        transaction.completedAt = DateTime.now();
        transaction.operatorId = result['operator_id'];
        transaction.paymentMethod = result['payment_method'];
        
        await TransactionHistoryService.saveTransaction(transaction);
        
        return result;
      } else {
        // Échec
        final error = result['message'];
        transaction.status = TransactionStatus.failed;
        transaction.failureReason = error;
        
        await TransactionHistoryService.saveTransaction(transaction);
        
        // Vérifier si on peut réessayer
        if (currentAttempt < maxRetries && PaymentErrorHandler.isRetryableError(error)) {
          // Vérifier le contexte avant le dialog
          if (!context.mounted) {
            return result;
          }

          // Demander à l'utilisateur s'il veut réessayer
          final shouldRetry = await PaymentErrorHandler.showRetryDialog(context, error);

          if (shouldRetry && context.mounted) {
            // Réessayer
            return await processPaymentWithRetry(
              context: context,
              transaction: transaction,
              currentAttempt: currentAttempt + 1,
            );
          }
        }
        
        return result;
      }
      
    } catch (e) {
      // Erreur inattendue
      transaction.status = TransactionStatus.failed;
      transaction.failureReason = e.toString();
      
      await TransactionHistoryService.saveTransaction(transaction);
      
      // Vérifier si on peut réessayer
      if (currentAttempt < maxRetries) {
        // Vérifier le contexte avant le dialog
        if (!context.mounted) {
          return {
            'success': false,
            'message': 'Contexte non disponible',
          };
        }

        final shouldRetry = await PaymentErrorHandler.showRetryDialog(context, e.toString());

        if (shouldRetry && context.mounted) {
          return await processPaymentWithRetry(
            context: context,
            transaction: transaction,
            currentAttempt: currentAttempt + 1,
          );
        }
      }
      
      return {
        'success': false,
        'message': 'Erreur lors du paiement: $e',
      };
    }
  }
  
  /// Reprend une transaction échouée
  static Future<Map<String, dynamic>> retryFailedTransaction({
    required BuildContext context,
    required String transactionId,
  }) async {
    try {
      // Récupérer la transaction
      final transaction = await TransactionHistoryService.getTransaction(transactionId);

      if (transaction == null) {
        return {
          'success': false,
          'message': 'Transaction introuvable',
        };
      }

      if (transaction.status != TransactionStatus.failed) {
        return {
          'success': false,
          'message': 'Cette transaction n\'a pas échoué',
        };
      }

      // Vérifier si l'erreur est récupérable
      if (!PaymentErrorHandler.isRetryableError(transaction.failureReason)) {
        return {
          'success': false,
          'message': 'Cette transaction ne peut pas être reprise',
        };
      }

      // Vérifier le contexte
      if (!context.mounted) {
        return {
          'success': false,
          'message': 'Contexte non disponible',
        };
      }

      // Réinitialiser le statut
      final updatedTransaction = transaction.copyWith(
        status: TransactionStatus.pending,
        failureReason: null,
      );

      // Reprendre le paiement
      return await processPaymentWithRetry(
        context: context,
        transaction: updatedTransaction,
      );

    } catch (e) {
      return {
        'success': false,
        'message': 'Erreur lors de la reprise: $e',
      };
    }
  }
  
  /// Vérifie le statut d'une transaction en attente
  static Future<Map<String, dynamic>> checkPendingTransaction(String transactionId) async {
    try {
      // Vérifier le statut via l'API CinetPay
      final statusResult = await CinetPayService.checkTransactionStatus(transactionId);
      
      if (statusResult['success']) {
        // Mettre à jour la transaction locale
        final transaction = await TransactionHistoryService.getTransaction(transactionId);
        
        if (transaction != null) {
          TransactionStatus newStatus;
          final apiStatus = statusResult['status']?.toString().toUpperCase();
          
          switch (apiStatus) {
            case 'ACCEPTED':
            case 'SUCCESS':
              newStatus = TransactionStatus.completed;
              break;
            case 'REFUSED':
            case 'FAILED':
              newStatus = TransactionStatus.failed;
              break;
            case 'CANCELLED':
              newStatus = TransactionStatus.cancelled;
              break;
            default:
              newStatus = TransactionStatus.processing;
          }
          
          final updatedTransaction = transaction.copyWith(
            status: newStatus,
            operatorId: statusResult['operator_id'],
            paymentMethod: statusResult['payment_method'],
            completedAt: newStatus == TransactionStatus.completed ? DateTime.now() : null,
          );
          
          await TransactionHistoryService.saveTransaction(updatedTransaction);
          
          return {
            'success': true,
            'transaction': updatedTransaction,
            'statusChanged': newStatus != transaction.status,
          };
        }
      }
      
      return statusResult;
      
    } catch (e) {
      return {
        'success': false,
        'message': 'Erreur lors de la vérification: $e',
      };
    }
  }
  
  /// Vérifie toutes les transactions en attente
  static Future<List<CinetPayTransaction>> checkAllPendingTransactions() async {
    try {
      final pendingTransactions = await TransactionHistoryService.getTransactionsByStatus(
        TransactionStatus.pending,
      );
      
      final processingTransactions = await TransactionHistoryService.getTransactionsByStatus(
        TransactionStatus.processing,
      );
      
      final allPending = [...pendingTransactions, ...processingTransactions];
      final updatedTransactions = <CinetPayTransaction>[];
      
      for (final transaction in allPending) {
        final result = await checkPendingTransaction(transaction.id);
        if (result['success'] && result['statusChanged'] == true) {
          updatedTransactions.add(result['transaction']);
        }
      }
      
      return updatedTransactions;
      
    } catch (e) {
      print('Erreur lors de la vérification des transactions en attente: $e');
      return [];
    }
  }
}
