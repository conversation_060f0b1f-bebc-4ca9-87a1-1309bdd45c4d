/// Script de test pour vérifier le nouveau design de la boutique avec nom
void main() {
  print('🧪 Test du nouveau design de la boutique avec nom...\n');
  
  // Simulation des données produits avec les vrais noms de l'API
  final List<Map<String, dynamic>> sampleProducts = [
    {
      'id': '354',
      'name': 'pack 1',
      'price': 150000,
      'dailyPrice': 1000,
      'imageUrl': 'https://dev-mani.io/app.callitris-distribution.com/app/public/img/kit/30ce0a32e0dc2b824310d18966ccb83478d39e0cbague.jpg',
      'inStock': true,
    },
    {
      'id': '355',
      'name': 'pack électronique premium',
      'price': 250000,
      'dailyPrice': 12500,
      'imageUrl': 'https://example.com/electronics.jpg',
      'inStock': true,
    },
    {
      'id': '356',
      'name': 'pack maison confort',
      'price': 320000,
      'dailyPrice': 16000,
      'imageUrl': 'https://example.com/home.jpg',
      'inStock': false,
    },
    {
      'id': '357',
      'name': 'pack beauté et bien-être',
      'price': 180000,
      'dailyPrice': 9000,
      'imageUrl': 'https://example.com/beauty.jpg',
      'inStock': true,
    },
  ];
  
  print('📊 Analyse du nouveau format d\'affichage:');
  print('');
  
  print('🔄 AVANT (image + prix seulement):');
  print('   • Image du produit (75% de la hauteur)');
  print('   • Prix total (25% de la hauteur)');
  print('   • childAspectRatio: 0.8');
  print('   • flex: image=3, prix=1');
  print('');
  
  print('✨ APRÈS (image + nom + prix):');
  print('   • Image du produit (60% de la hauteur)');
  print('   • Nom du produit (20% de la hauteur)');
  print('   • Prix total (20% de la hauteur)');
  print('   • childAspectRatio: 0.7 (plus d\'espace vertical)');
  print('   • flex: image=3, contenu=2');
  print('');
  
  print('🎨 Structure de la nouvelle carte:');
  print('┌─────────────────────┐');
  print('│                     │');
  print('│       IMAGE         │ ← flex: 3 (60%)');
  print('│                     │');
  print('├─────────────────────┤');
  print('│    Nom du produit   │ ← Texte centré, 2 lignes max');
  print('│    150 000 FCFA     │ ← Prix en couleur primaire');
  print('└─────────────────────┘ ← flex: 2 (40%)');
  print('');
  
  print('📱 Avantages du nouveau design:');
  print('   ✅ Identification rapide des produits');
  print('   ✅ Meilleure lisibilité avec le nom');
  print('   ✅ Hiérarchie visuelle claire');
  print('   ✅ Espacement optimisé (8px entre nom et prix)');
  print('   ✅ Gestion du débordement de texte (ellipsis)');
  print('   ✅ Centrage du contenu pour un look équilibré');
  print('');
  
  print('🎯 Détails techniques:');
  print('   • Nom: fontSize 14, fontWeight w600, maxLines 2');
  print('   • Prix: fontSize 16, fontWeight bold, couleur primaire');
  print('   • Espacement: SizedBox(height: 8) entre nom et prix');
  print('   • Padding: EdgeInsets.all(12) pour le contenu');
  print('   • Overflow: TextOverflow.ellipsis pour les noms longs');
  print('');
  
  print('💰 Exemples d\'affichage:');
  for (final product in sampleProducts) {
    final formattedPrice = _formatPrice(product['price']);
    final displayName = product['name'].length > 20 
        ? '${product['name'].substring(0, 17)}...' 
        : product['name'];
    
    print('┌─────────────────────┐');
    print('│       [IMAGE]       │');
    print('├─────────────────────┤');
    print('│ ${displayName.padRight(19)} │');
    print('│ ${formattedPrice.padRight(11)} FCFA │');
    print('└─────────────────────┘');
    print('');
  }
  
  print('🏁 Le nouveau design offre une expérience plus complète et informative !');
}

// Formater le prix avec des espaces comme séparateurs de milliers
String _formatPrice(dynamic price) {
  final int priceInt = price is int ? price : price.toInt();
  final String priceString = priceInt.toString();
  final StringBuffer result = StringBuffer();

  for (int i = 0; i < priceString.length; i++) {
    if (i > 0 && (priceString.length - i) % 3 == 0) {
      result.write(' ');
    }
    result.write(priceString[i]);
  }

  return result.toString();
}
