<?php
/**
 * Script de test pour le gestionnaire de webhooks Wave
 * 
 * Ce script permet de tester le webhook handler ave<PERSON> des données réelles
 * sans avoir besoin d'attendre un vrai webhook de Wave
 */

require_once 'wave_webhook_handler.php';

?>
<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Webhook Wave - Callitris</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        .test-section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .success { border-color: #28a745; background-color: #f8fff9; }
        .error { border-color: #dc3545; background-color: #fff8f8; }
        .warning { border-color: #ffc107; background-color: #fffdf5; }
        .info { border-color: #17a2b8; background-color: #f8fdff; }
        
        pre {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 5px;
            overflow-x: auto;
            border: 1px solid #e9ecef;
        }
        .btn {
            display: inline-block;
            padding: 10px 20px;
            background: #007bff;
            color: white;
            text-decoration: none;
            border-radius: 5px;
            margin: 5px;
            border: none;
            cursor: pointer;
        }
        .btn:hover { background: #0056b3; }
        .btn-success { background: #28a745; }
        .btn-danger { background: #dc3545; }
        .btn-warning { background: #ffc107; color: #212529; }
        
        h1, h2, h3 { color: #333; }
        .status { font-weight: bold; padding: 5px 10px; border-radius: 3px; }
        .status.success { background: #d4edda; color: #155724; }
        .status.error { background: #f8d7da; color: #721c24; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🌊 Test Webhook Wave - Callitris</h1>
        <p>Utilisez cette page pour tester le gestionnaire de webhooks Wave avec différents scénarios.</p>
        
        <?php if (isset($_POST['test_webhook'])): ?>
            <div class="test-section">
                <h2>Résultat du Test</h2>
                <?php
                $testType = $_POST['test_type'] ?? 'success';
                $customData = $_POST['custom_data'] ?? '';
                
                // Données de test prédéfinies
                $testData = [
                    'success' => [
                        "id" => "AE_test_" . uniqid(),
                        "type" => "checkout.session.completed",
                        "data" => [
                            "id" => "cos-test-" . uniqid(),
                            "amount" => "1500",
                            "checkout_status" => "complete",
                            "client_reference" => "test_client_" . time(),
                            "currency" => "XOF",
                            "error_url" => "https://example.com/error",
                            "last_payment_error" => null,
                            "business_name" => "Callitris Test",
                            "payment_status" => "succeeded",
                            "success_url" => "https://example.com/success",
                            "wave_launch_url" => "https://pay.wave.com/c/test",
                            "when_completed" => date('c'),
                            "when_created" => date('c', strtotime('-5 minutes')),
                            "when_expires" => date('c', strtotime('+1 day')),
                            "transaction_id" => "TCN_TEST_" . strtoupper(uniqid())
                        ]
                    ],
                    'failed' => [
                        "id" => "EV_test_" . uniqid(),
                        "type" => "checkout.session.payment_failed",
                        "data" => [
                            "id" => "cos-test-failed-" . uniqid(),
                            "amount" => "2000",
                            "checkout_status" => "failed",
                            "client_reference" => "test_client_failed_" . time(),
                            "currency" => "XOF",
                            "error_url" => "https://example.com/error",
                            "payment_status" => "failed",
                            "success_url" => "https://example.com/success",
                            "wave_launch_url" => "https://pay.wave.com/c/test-failed",
                            "when_created" => date('c', strtotime('-2 minutes')),
                            "when_expires" => date('c', strtotime('+1 day'))
                        ]
                    ]
                ];
                
                // Utiliser des données personnalisées si fournies
                if (!empty($customData)) {
                    $testWebhook = json_decode($customData, true);
                    if (json_last_error() !== JSON_ERROR_NONE) {
                        echo '<div class="status error">Erreur JSON dans les données personnalisées: ' . json_last_error_msg() . '</div>';
                        $testWebhook = $testData[$testType];
                    }
                } else {
                    $testWebhook = $testData[$testType] ?? $testData['success'];
                }
                
                echo '<h3>Données envoyées:</h3>';
                echo '<pre>' . json_encode($testWebhook, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE) . '</pre>';
                
                // Traiter le webhook
                $startTime = microtime(true);
                $result = processWebhook($testWebhook, $testWebhook['type']);
                $endTime = microtime(true);
                $processingTime = round(($endTime - $startTime) * 1000, 2);
                
                echo '<h3>Résultat:</h3>';
                if ($result['success']) {
                    echo '<div class="status success">✅ Succès</div>';
                } else {
                    echo '<div class="status error">❌ Échec</div>';
                }
                
                echo '<pre>' . json_encode($result, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE) . '</pre>';
                echo '<p><strong>Temps de traitement:</strong> ' . $processingTime . ' ms</p>';
                ?>
            </div>
        <?php endif; ?>
        
        <div class="test-section info">
            <h2>Tests Prédéfinis</h2>
            <form method="post" style="margin-bottom: 20px;">
                <input type="hidden" name="test_webhook" value="1">
                
                <h3>Choisir un type de test:</h3>
                <label>
                    <input type="radio" name="test_type" value="success" checked> 
                    ✅ Paiement réussi (checkout.session.completed)
                </label><br>
                <label>
                    <input type="radio" name="test_type" value="failed"> 
                    ❌ Paiement échoué (checkout.session.payment_failed)
                </label><br><br>
                
                <button type="submit" class="btn btn-success">Lancer le Test</button>
            </form>
        </div>
        
        <div class="test-section warning">
            <h2>Test avec Données Personnalisées</h2>
            <form method="post">
                <input type="hidden" name="test_webhook" value="1">
                <input type="hidden" name="test_type" value="custom">
                
                <h3>JSON du Webhook:</h3>
                <textarea name="custom_data" rows="15" style="width: 100%; font-family: monospace;" placeholder='Collez ici votre JSON de webhook Wave...

Exemple:
{
  "id": "AE_custom_test",
  "type": "checkout.session.completed",
  "data": {
    "id": "cos-custom-test",
    "amount": "5000",
    "checkout_status": "complete",
    "client_reference": "custom_client_123",
    "currency": "XOF",
    "transaction_id": "TCN_CUSTOM_123"
  }
}'></textarea><br><br>
                
                <button type="submit" class="btn btn-warning">Tester avec Données Personnalisées</button>
            </form>
        </div>
        
        <div class="test-section">
            <h2>Vérification de la Configuration</h2>
            <?php
            echo '<h3>Configuration de la Base de Données:</h3>';
            try {
                $pdo = getDatabaseConnection();
                echo '<div class="status success">✅ Connexion à la base de données réussie</div>';
                
                // Vérifier les tables
                $tables = ['wave_transactions', 'wave_callback_logs'];
                foreach ($tables as $table) {
                    $stmt = $pdo->query("SHOW TABLES LIKE '$table'");
                    if ($stmt->rowCount() > 0) {
                        echo '<div class="status success">✅ Table ' . $table . ' existe</div>';
                    } else {
                        echo '<div class="status error">❌ Table ' . $table . ' manquante</div>';
                    }
                }
                
            } catch (Exception $e) {
                echo '<div class="status error">❌ Erreur de connexion: ' . htmlspecialchars($e->getMessage()) . '</div>';
            }
            
            echo '<h3>Configuration des Logs:</h3>';
            $logDir = dirname(LOG_FILE);
            if (is_dir($logDir) && is_writable($logDir)) {
                echo '<div class="status success">✅ Dossier de logs accessible en écriture</div>';
            } else {
                echo '<div class="status error">❌ Dossier de logs non accessible: ' . htmlspecialchars($logDir) . '</div>';
            }
            
            echo '<h3>Configuration Wave:</h3>';
            if (defined('WAVE_WEBHOOK_SECRET') && !empty(WAVE_WEBHOOK_SECRET) && WAVE_WEBHOOK_SECRET !== 'your_wave_webhook_secret') {
                echo '<div class="status success">✅ Secret webhook configuré</div>';
            } else {
                echo '<div class="status warning">⚠️ Secret webhook non configuré (optionnel)</div>';
            }
            ?>
        </div>
        
        <div class="test-section">
            <h2>Logs Récents</h2>
            <?php
            if (file_exists(LOG_FILE)) {
                $logs = file_get_contents(LOG_FILE);
                $logLines = array_slice(array_filter(explode("\n", $logs)), -20); // 20 dernières lignes
                echo '<pre>' . htmlspecialchars(implode("\n", $logLines)) . '</pre>';
                
                echo '<p><a href="?clear_logs=1" class="btn btn-danger" onclick="return confirm(\'Êtes-vous sûr de vouloir effacer les logs ?\')">Effacer les Logs</a></p>';
            } else {
                echo '<p>Aucun fichier de log trouvé.</p>';
            }
            
            // Effacer les logs si demandé
            if (isset($_GET['clear_logs'])) {
                file_put_contents(LOG_FILE, '');
                echo '<div class="status success">✅ Logs effacés</div>';
                echo '<script>setTimeout(function(){ window.location.href = window.location.pathname; }, 1000);</script>';
            }
            ?>
        </div>
    </div>
    
    <div class="container">
        <h2>📋 Instructions d'Utilisation</h2>
        <ol>
            <li><strong>Configuration:</strong> Modifiez les paramètres de base de données dans <code>wave_webhook_handler.php</code></li>
            <li><strong>Tables:</strong> Exécutez le script <code>wave_database_setup.sql</code> pour créer les tables nécessaires</li>
            <li><strong>URL Webhook:</strong> Configurez cette URL dans votre dashboard Wave:<br>
                <code><?php echo (isset($_SERVER['HTTPS']) ? 'https' : 'http') . '://' . $_SERVER['HTTP_HOST'] . dirname($_SERVER['REQUEST_URI']) . '/wave_webhook_handler.php'; ?></code>
            </li>
            <li><strong>Test:</strong> Utilisez cette page pour tester avant de configurer Wave</li>
            <li><strong>Monitoring:</strong> Surveillez les logs pour détecter les problèmes</li>
        </ol>
    </div>
</body>
</html>
