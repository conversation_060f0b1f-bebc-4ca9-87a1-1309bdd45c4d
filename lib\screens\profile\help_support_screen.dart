import 'package:flutter/material.dart';
import 'package:callitris/utils/appTheme.dart';

class HelpSupportScreen extends StatefulWidget {
  const HelpSupportScreen({super.key});

  @override
  State<HelpSupportScreen> createState() => _HelpSupportScreenState();
}

class _HelpSupportScreenState extends State<HelpSupportScreen> {
  // Liste des questions fréquentes
  final List<Map<String, dynamic>> _faqs = [
    {
      'question': 'Comment fonctionne une cotisation ?',
      'answer': 'Une cotisation est un système d\'épargne personnelle où vous contribuez régulièrement sur une période définie. Dans notre application, vous pouvez définir le montant journalier et effectuer vos versements à votre rythme sur une période de 21 jours. Cela vous permet d\'épargner de manière disciplinée pour atteindre vos objectifs financiers.',
      'isExpanded': false,
    },
    {
      'question': 'Comment créer une cotisation ?',
      'answer': 'Pour créer une cotisation, accédez à l\'écran "Mes Cotisations" et appuyez sur le bouton "+" en bas de l\'écran. Suivez ensuite les instructions pour définir les paramètres de votre cotisation, comme le nom et le montant journalier. La durée est fixée à 21 jours pour toutes les cotisations.',
      'isExpanded': false,
    },
    {
      'question': 'Comment fonctionne la période de cotisation ?',
      'answer': 'Chaque cotisation s\'étend sur une période fixe de 21 jours. Vous pouvez effectuer vos versements quotidiennement ou regrouper plusieurs jours en un seul versement. L\'application calcule automatiquement combien de jours sont couverts par votre versement et garde la monnaie pour de futurs versements.',
      'isExpanded': false,
    },
    {
      'question': 'Comment effectuer un versement ?',
      'answer': 'Pour effectuer un versement, accédez aux détails de votre cotisation et appuyez sur le bouton "Verser". Vous pourrez alors choisir le montant (qui peut couvrir un ou plusieurs jours) et la méthode de paiement. Le système calculera automatiquement le nombre de jours couverts et la monnaie restante.',
      'isExpanded': false,
    },
    {
      'question': 'Comment fonctionne la monnaie ?',
      'answer': 'La monnaie représente le reliquat de vos versements qui n\'atteint pas le montant d\'un jour complet. Par exemple, si votre cotisation journalière est de 5 000 FCFA et que vous versez 12 000 FCFA, le système comptabilisera 2 jours (10 000 FCFA) et gardera 2 000 FCFA en monnaie. Cette monnaie peut être utilisée pour de futurs versements ou retirée.',
      'isExpanded': false,
    },
    {
      'question': 'Que se passe-t-il à la fin de la période de cotisation ?',
      'answer': 'À la fin de la période de 21 jours, votre cotisation est marquée comme terminée. Vous pouvez alors créer une nouvelle cotisation ou consulter l\'historique de vos cotisations passées. Toute monnaie restante peut être utilisée pour de nouvelles cotisations ou retirée sur votre compte bancaire ou mobile money.',
      'isExpanded': false,
    },
  ];

  final _formKey = GlobalKey<FormState>();
  final _subjectController = TextEditingController();
  final _messageController = TextEditingController();

  @override
  void dispose() {
    _subjectController.dispose();
    _messageController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Aide et support'),
      ),
      body: SingleChildScrollView(
        child: Column(
          children: [
            _buildHeader(),
            _buildFAQSection(),
            _buildContactForm(),
          ],
        ),
      ),
    );
  }

  Widget _buildHeader() {
    return Container(
      padding: const EdgeInsets.all(16),
      color: AppTheme.color.primaryColor,
      child: Row(
        children: [
          Container(
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: Colors.white.withOpacity(0.2),
              shape: BoxShape.circle,
            ),
            child: const Icon(
              Icons.help_outline,
              color: Colors.white,
              size: 28,
            ),
          ),
          const SizedBox(width: 16),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                const Text(
                  'Comment pouvons-nous vous aider ?',
                  style: TextStyle(
                    color: Colors.white,
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  'Consultez nos FAQ ou contactez-nous directement',
                  style: TextStyle(
                    color: Colors.white.withOpacity(0.8),
                    fontSize: 14,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildFAQSection() {
    return Card(
      margin: const EdgeInsets.all(16),
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Questions fréquentes',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
                color: AppTheme.color.primaryColor,
              ),
            ),
            const SizedBox(height: 16),
            ListView.separated(
              physics: const NeverScrollableScrollPhysics(),
              shrinkWrap: true,
              itemCount: _faqs.length,
              separatorBuilder: (context, index) => const Divider(),
              itemBuilder: (context, index) {
                return ExpansionTile(
                  title: Text(
                    _faqs[index]['question'],
                    style: const TextStyle(
                      fontWeight: FontWeight.bold,
                      fontSize: 15,
                    ),
                  ),
                  tilePadding: EdgeInsets.zero,
                  childrenPadding: const EdgeInsets.only(bottom: 8),
                  expandedCrossAxisAlignment: CrossAxisAlignment.start,
                  onExpansionChanged: (expanded) {
                    setState(() {
                      _faqs[index]['isExpanded'] = expanded;
                    });
                  },
                  initiallyExpanded: _faqs[index]['isExpanded'],
                  children: [
                    Padding(
                      padding: const EdgeInsets.symmetric(horizontal: 16),
                      child: Text(
                        _faqs[index]['answer'],
                        style: TextStyle(
                          color: AppTheme.color.brunGris,
                          height: 1.5,
                        ),
                      ),
                    ),
                  ],
                );
              },
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildContactForm() {
    return Card(
      margin: const EdgeInsets.all(16),
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Form(
          key: _formKey,
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                'Contactez-nous',
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                  color: AppTheme.color.primaryColor,
                ),
              ),
              const SizedBox(height: 16),
              TextFormField(
                controller: _subjectController,
                decoration: InputDecoration(
                  labelText: 'Sujet',
                  border: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(10),
                  ),
                  prefixIcon: Icon(Icons.subject, color: AppTheme.color.primaryColor),
                ),
                validator: (value) {
                  if (value == null || value.isEmpty) {
                    return 'Veuillez entrer un sujet';
                  }
                  return null;
                },
              ),
              const SizedBox(height: 16),
              TextFormField(
                controller: _messageController,
                decoration: InputDecoration(
                  labelText: 'Message',
                  border: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(10),
                  ),
                  prefixIcon: Icon(Icons.message, color: AppTheme.color.primaryColor),
                ),
                maxLines: 5,
                validator: (value) {
                  if (value == null || value.isEmpty) {
                    return 'Veuillez entrer votre message';
                  }
                  return null;
                },
              ),
              const SizedBox(height: 24),
              ElevatedButton(
                onPressed: () {
                  if (_formKey.currentState!.validate()) {
                    // Logique pour envoyer le message
                    ScaffoldMessenger.of(context).showSnackBar(
                      SnackBar(
                        content: const Text('Message envoyé avec succès'),
                        backgroundColor: AppTheme.color.greenColor,
                      ),
                    );
                    // Réinitialiser le formulaire
                    _subjectController.clear();
                    _messageController.clear();
                  }
                },
                style: ElevatedButton.styleFrom(
                  minimumSize: const Size(double.infinity, 50),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(10),
                  ),
                ),
                child: const Text('Envoyer'),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
