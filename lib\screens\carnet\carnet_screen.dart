import 'package:callitris/config/api_config.dart';
import 'package:flutter/material.dart';
import 'package:callitris/utils/appTheme.dart';
import 'package:callitris/services/carnet_service.dart';
import 'package:callitris/services/auth_service.dart';
import 'package:callitris/widgets/navigation_menu_button.dart';

import 'carnet_elements_screen.dart';

class CarnetScreen extends StatefulWidget {
  const CarnetScreen({super.key});

  @override
  State<CarnetScreen> createState() => _CarnetScreenState();
}

class _CarnetScreenState extends State<CarnetScreen>
    with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  final ScrollController _scrollController = ScrollController();
  bool _isScrolled = false;
  bool _isLoading = true;
  List<Map<String, dynamic>> _carnetCategories = [];
  String? _selectedCarnetId;
  bool _hasError = false;
  String _errorMessage = '';
  String _searchQuery = '';
  final TextEditingController _searchController = TextEditingController();
  bool isAcces = false;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 300),
    );

    _scrollController.addListener(() {
      if (_scrollController.offset > 0 && !_isScrolled) {
        setState(() {
          _isScrolled = true;
        });
      } else if (_scrollController.offset <= 0 && _isScrolled) {
        setState(() {
          _isScrolled = false;
        });
      }
    });

    _loadCarnetCategories();
    _loadAccecibility();
  }

  @override
  void dispose() {
    _animationController.dispose();
    _scrollController.dispose();
    _searchController.dispose();
    super.dispose();
  }

  Future<void> _loadAccecibility() async {
    setState(() {
      _isLoading = true;
    });
    try {
      final userData = await AuthService.getUserData();
      if (userData?['is_access'] == 1) {
        setState(() {
          isAcces = true;
          _isLoading = false;
        });
      }
    } catch (e) {
      print("Une erreur est survenu : $e");
      setState(() {
        _isLoading = true;
      });
    }
  }

  // Méthode pour traiter les éléments du carnet selon la structure de la réponse API
  // Méthode pour traiter les éléments du carnet selon la structure de la réponse API
  List<Map<String, dynamic>> _processCarnetElements(dynamic data) {
    print("Structure des données reçues: ${data.runtimeType}");

    if (data is List && data.isNotEmpty) {
      if (data[0] is List) {
        // Structure observée dans votre réponse: [ [ {item1}, {item2}, ... ] ]
        print("Structure détectée: liste de listes");
        return List<Map<String, dynamic>>.from(data[0]);
      } else {
        // Structure alternative: [ {item1}, {item2}, ... ]
        print("Structure détectée: liste simple");
        return List<Map<String, dynamic>>.from(data);
      }
    } else if (data is Map && data.containsKey('id_kit')) {
      // Structure pour un seul élément: { id_kit: ..., ... }
      print("Structure détectée: élément unique");
      return [Map<String, dynamic>.from(data)];
    } else {
      // Aucun élément trouvé ou format non reconnu
      print("Structure non reconnue ou vide");
      return [];
    }
  }

  Future<void> _loadCarnetCategories() async {
    setState(() {
      _isLoading = true;
      _hasError = false;
    });

    final result = await CarnetService.getCarnetCategories();

    if (result['success']) {
      final data = result['data'];

      // Déboguer la structure des données
      _debugPrintDataStructure(data);

      // Vérifier les différentes structures possibles de la réponse
      if (data.containsKey('catégories')) {
        setState(() {
          _carnetCategories = List<Map<String, dynamic>>.from(
            data['catégories'],
          );
          _isLoading = false;
        });
      } else if (data.containsKey('categories')) {
        // Alternative si la clé est sans accent
        setState(() {
          _carnetCategories = List<Map<String, dynamic>>.from(
            data['categories'],
          );
          _isLoading = false;
        });
      } else if (data is List) {
        // Si les données sont directement une liste
        setState(() {
          _carnetCategories = List<Map<String, dynamic>>.from(data);
          _isLoading = false;
        });
      } else {
        // Essayer de trouver une clé qui pourrait contenir les catégories
        bool foundCategories = false;
        data.forEach((key, value) {
          if (value is List && !foundCategories) {
            setState(() {
              _carnetCategories = List<Map<String, dynamic>>.from(value);
              _isLoading = false;
              foundCategories = true;
            });
          }
        });

        if (!foundCategories) {
          setState(() {
            _isLoading = false;
            _hasError = true;
            _errorMessage =
                'Structure de données non reconnue. Veuillez contacter le support.';
          });
        }
      }
    } else {
      setState(() {
        _isLoading = false;
        _hasError = true;
        _errorMessage = result['message'];
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFFF8F9FA),
      body:
          isAcces
              ? CustomScrollView(
                controller: _scrollController,
                slivers: [
                  _buildModernAppBar(),
                  if (_isLoading)
                    SliverFillRemaining(child: _buildLoadingState())
                  else if (_hasError)
                    SliverFillRemaining(child: _buildErrorState())
                  else if (_carnetCategories.isEmpty)
                    SliverFillRemaining(child: _buildEmptyState())
                  else ...[
                    _buildSearchSection(),
                    _buildStatsSection(),
                    _buildCarnetGrid(),
                  ],
                ],
              )
              : AccessDeniedCard(isLoading: _isLoading),
    );
  }

  // AppBar moderne avec effet de scroll
  Widget _buildModernAppBar() {
    return SliverAppBar(
      expandedHeight: 120.0,
      floating: false,
      pinned: true,
      elevation: 0,
      backgroundColor: Colors.white,
      surfaceTintColor: Colors.transparent,
      flexibleSpace: FlexibleSpaceBar(
        titlePadding: const EdgeInsets.only(left: 20, bottom: 16),
        title: AnimatedOpacity(
          opacity: _isScrolled ? 1.0 : 0.0,
          duration: const Duration(milliseconds: 200),
          child: Text(
            'Carnets',
            style: TextStyle(
              color: AppTheme.color.textColor,
              fontSize: 20,
              fontWeight: FontWeight.bold,
            ),
          ),
        ),
        background: Container(
          decoration: BoxDecoration(
            gradient: LinearGradient(
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
              colors: [Colors.orange.shade400, Colors.amber.shade400],
            ),
            borderRadius: const BorderRadius.only(
              bottomLeft: Radius.circular(30),
              bottomRight: Radius.circular(30),
            ),
          ),
          child: SafeArea(
            child: Padding(
              padding: const EdgeInsets.symmetric(horizontal: 20),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                mainAxisAlignment: MainAxisAlignment.end,
                children: [
                  const SizedBox(height: 20),
                  Row(
                    children: [
                      Container(
                        padding: const EdgeInsets.all(12),
                        decoration: BoxDecoration(
                          color: Colors.white.withValues(alpha: 0.2),
                          borderRadius: BorderRadius.circular(16),
                          boxShadow: [
                            BoxShadow(
                              color: Colors.black.withValues(alpha: 0.1),
                              blurRadius: 8,
                              offset: const Offset(0, 2),
                            ),
                          ],
                        ),
                        child: const Icon(
                          Icons.menu_book_rounded,
                          color: Colors.white,
                          size: 28,
                        ),
                      ),
                      const SizedBox(width: 16),
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            const Text(
                              'Mes Carnets',
                              style: TextStyle(
                                fontSize: 28,
                                fontWeight: FontWeight.bold,
                                color: Colors.white,
                                height: 1.2,
                              ),
                            ),
                            const SizedBox(height: 4),
                            Text(
                              'Découvrez nos collections',
                              style: TextStyle(
                                fontSize: 16,
                                color: Colors.white.withValues(alpha: 0.9),
                                fontWeight: FontWeight.w500,
                              ),
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 20),
                ],
              ),
            ),
          ),
        ),
      ),
      actions: [
        NavigationMenuButton(iconColor: const Color.fromARGB(255, 0, 0, 0)),
      ],
    );
  }

  Widget _buildLoadingState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Container(
            padding: const EdgeInsets.all(20),
            decoration: BoxDecoration(
              color: AppTheme.color.primaryColor.withOpacity(0.1),
              shape: BoxShape.circle,
            ),
            child: CircularProgressIndicator(
              valueColor: AlwaysStoppedAnimation<Color>(
                AppTheme.color.primaryColor,
              ),
              strokeWidth: 3,
            ),
          ),
          const SizedBox(height: 24),
          Text(
            'Chargement des carnets...',
            style: TextStyle(
              fontSize: 16,
              color: AppTheme.color.brunGris,
              fontWeight: FontWeight.w500,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildErrorState() {
    return Center(
      child: SingleChildScrollView(
        padding: const EdgeInsets.all(24),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.error_outline, size: 80, color: Colors.red[300]),
            const SizedBox(height: 24),
            Text(
              'Erreur de chargement',
              style: TextStyle(
                fontSize: 20,
                fontWeight: FontWeight.bold,
                color: AppTheme.color.textColor,
              ),
            ),
            const SizedBox(height: 16),
            Text(
              _errorMessage,
              style: TextStyle(fontSize: 16, color: AppTheme.color.brunGris),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 32),
            SizedBox(
              width: 200,
              height: 50,
              child: ElevatedButton(
                onPressed: _loadCarnetCategories,
                style: ElevatedButton.styleFrom(
                  backgroundColor: AppTheme.color.primaryColor,
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(12),
                  ),
                ),
                child: const Text(
                  'Réessayer',
                  style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  // Section de recherche moderne
  Widget _buildSearchSection() {
    return SliverToBoxAdapter(
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Container(
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(16),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withOpacity(0.04),
                blurRadius: 10,
                offset: const Offset(0, 2),
              ),
            ],
          ),
          child: TextField(
            controller: _searchController,
            onChanged: (value) {
              setState(() {
                _searchQuery = value.toLowerCase();
              });
            },
            decoration: InputDecoration(
              hintText: 'Rechercher un carnet...',
              hintStyle: TextStyle(
                color: AppTheme.color.brunGris,
                fontSize: 16,
              ),
              prefixIcon: Icon(
                Icons.search,
                color: AppTheme.color.primaryColor,
                size: 24,
              ),
              suffixIcon:
                  _searchQuery.isNotEmpty
                      ? IconButton(
                        icon: Icon(Icons.clear, color: AppTheme.color.brunGris),
                        onPressed: () {
                          _searchController.clear();
                          setState(() {
                            _searchQuery = '';
                          });
                        },
                      )
                      : null,
              border: InputBorder.none,
              contentPadding: const EdgeInsets.symmetric(
                horizontal: 20,
                vertical: 16,
              ),
            ),
            style: TextStyle(fontSize: 16, color: AppTheme.color.textColor),
          ),
        ),
      ),
    );
  }

  // Section des statistiques
  Widget _buildStatsSection() {
    final filteredCarnets = _getFilteredCarnets();

    return SliverToBoxAdapter(
      child: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 20),
        child: Container(
          padding: const EdgeInsets.all(20),
          decoration: BoxDecoration(
            gradient: LinearGradient(
              colors: [
                AppTheme.color.primaryColor,
                AppTheme.color.primaryColor.withOpacity(0.8),
              ],
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
            ),
            borderRadius: BorderRadius.circular(20),
            boxShadow: [
              BoxShadow(
                color: AppTheme.color.primaryColor.withOpacity(0.3),
                blurRadius: 15,
                offset: const Offset(0, 5),
              ),
            ],
          ),
          child: Row(
            children: [
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      '${filteredCarnets.length}',
                      style: const TextStyle(
                        fontSize: 32,
                        fontWeight: FontWeight.bold,
                        color: Colors.white,
                      ),
                    ),
                    const SizedBox(height: 4),
                    Text(
                      filteredCarnets.length <= 1
                          ? 'Carnet disponible'
                          : 'Carnets disponibles',
                      style: TextStyle(
                        fontSize: 16,
                        color: Colors.white.withOpacity(0.9),
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ],
                ),
              ),
              Container(
                padding: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  color: Colors.white.withValues(alpha: 0.2),
                  borderRadius: BorderRadius.circular(16),
                ),
                child: const Icon(
                  Icons.auto_stories,
                  color: Colors.white,
                  size: 32,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildEmptyState() {
    return Center(
      child: SingleChildScrollView(
        padding: const EdgeInsets.all(24),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Container(
              padding: const EdgeInsets.all(24),
              decoration: BoxDecoration(
                color: AppTheme.color.primaryColor.withOpacity(0.1),
                shape: BoxShape.circle,
              ),
              child: Icon(
                Icons.book_outlined,
                size: 64,
                color: AppTheme.color.primaryColor,
              ),
            ),
            const SizedBox(height: 24),
            Text(
              'Aucun carnet disponible',
              style: TextStyle(
                fontSize: 24,
                fontWeight: FontWeight.bold,
                color: AppTheme.color.textColor,
              ),
            ),
            const SizedBox(height: 16),
            Text(
              'Aucune catégorie de carnet n\'est disponible actuellement.\nVeuillez réessayer plus tard.',
              style: TextStyle(
                fontSize: 16,
                color: AppTheme.color.brunGris,
                height: 1.5,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 32),
            ElevatedButton.icon(
              onPressed: _loadCarnetCategories,
              icon: const Icon(Icons.refresh),
              label: const Text(
                'Actualiser',
                style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
              ),
              style: ElevatedButton.styleFrom(
                backgroundColor: AppTheme.color.primaryColor,
                foregroundColor: Colors.white,
                padding: const EdgeInsets.symmetric(
                  horizontal: 24,
                  vertical: 12,
                ),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(12),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  // Méthode pour filtrer les carnets selon la recherche
  List<Map<String, dynamic>> _getFilteredCarnets() {
    if (_searchQuery.isEmpty) {
      return _carnetCategories;
    }

    return _carnetCategories.where((carnet) {
      final name =
          (carnet['nom_carnet'] ?? carnet['nom'] ?? carnet['name'] ?? '')
              .toString()
              .toLowerCase();
      final description =
          (carnet['description'] ?? '').toString().toLowerCase();
      return name.contains(_searchQuery) || description.contains(_searchQuery);
    }).toList();
  }

  // Grille moderne des carnets
  Widget _buildCarnetGrid() {
    final filteredCarnets = _getFilteredCarnets();

    if (filteredCarnets.isEmpty && _searchQuery.isNotEmpty) {
      return SliverToBoxAdapter(
        child: Container(
          padding: const EdgeInsets.all(40),
          child: Column(
            children: [
              Icon(Icons.search_off, size: 64, color: AppTheme.color.brunGris),
              const SizedBox(height: 16),
              Text(
                'Aucun résultat trouvé',
                style: TextStyle(
                  fontSize: 20,
                  fontWeight: FontWeight.bold,
                  color: AppTheme.color.textColor,
                ),
              ),
              const SizedBox(height: 8),
              Text(
                'Essayez avec d\'autres mots-clés',
                style: TextStyle(fontSize: 16, color: AppTheme.color.brunGris),
              ),
            ],
          ),
        ),
      );
    }

    return SliverPadding(
      padding: const EdgeInsets.all(20),
      sliver: SliverGrid(
        gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
          crossAxisCount: 2,
          childAspectRatio: 0.85,
          crossAxisSpacing: 12,
          mainAxisSpacing: 12,
        ),
        delegate: SliverChildBuilderDelegate((context, index) {
          final category = filteredCarnets[index];
          final isSelected =
              _selectedCarnetId == category['id_carnet']?.toString();
          return _buildModernCarnetCard(category, isSelected);
        }, childCount: filteredCarnets.length),
      ),
    );
  }

  // Carte moderne pour les carnets avec le nouveau design
  Widget _buildModernCarnetCard(
    Map<String, dynamic> category,
    bool isSelected,
  ) {
    // Extraire les valeurs avec des fallbacks
    final String id =
        category['id_carnet']?.toString() ??
        category['id']?.toString() ??
        'ID inconnu';
    final String date_debut_cotisation =
        category['date_debut_cotisation']?.toString() ?? 'Date inconnue';
    final String date_fin_cotisation =
        category['date_fin_cotisation']?.toString() ?? 'Date inconnue';
    final String name =
        category['nom_carnet'] ??
        category['nom'] ??
        category['name'] ??
        'Carnet sans nom';

    final String imageUrl =
        category['carnet_img'] ??
        category['imageUrl'] ??
        'assets/images/carnet_default.jpg';

    final String description =
        '${category['description'] ?? 'Découvrir ce carnet'}';

    // Générer une couleur et icône basées sur l'ID avec les nouvelles couleurs
    final int colorSeed = id.hashCode;
    final List<Color> cardColors = [
      Colors.orange.shade400, // Orange
      Colors.teal.shade400, // Teal
      Colors.purple.shade400, // Violet
      Colors.blue.shade400, // Bleu
      Colors.green.shade400, // Vert
      Colors.amber.shade400, // Amber
    ];

    final List<IconData> cardIcons = [
      Icons.auto_stories,
      Icons.menu_book,
      Icons.library_books,
      Icons.book,
      Icons.chrome_reader_mode,
      Icons.import_contacts,
    ];

    final Color cardColor = cardColors[colorSeed % cardColors.length];
    final IconData cardIcon = cardIcons[colorSeed % cardIcons.length];

    return Container(
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [Colors.white, cardColor.withValues(alpha: 0.05)],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        borderRadius: BorderRadius.circular(16),
        border: Border.all(color: cardColor.withValues(alpha: 0.2), width: 1),
        boxShadow: [
          BoxShadow(
            color: cardColor.withValues(alpha: 0.1),
            blurRadius: 12,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Material(
        color: Colors.transparent,
        borderRadius: BorderRadius.circular(16),
        child: InkWell(
          onTap: () => _onCarnetTap(category, id, name),
          borderRadius: BorderRadius.circular(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Image du carnet en haut
              Expanded(
                flex: 3,
                child: Container(
                  width: double.infinity,
                  margin: const EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(12),
                    boxShadow: [
                      BoxShadow(
                        color: cardColor.withValues(alpha: 0.2),
                        blurRadius: 4,
                        offset: const Offset(0, 2),
                      ),
                    ],
                  ),
                  child: Stack(
                    children: [
                      // Image du carnet
                      ClipRRect(
                        borderRadius: BorderRadius.circular(12),
                        child:
                            category['carnet_img'] != null &&
                                    category['carnet_img'].toString().isNotEmpty
                                ? Image.network(
                                  category['carnet_img'].toString().startsWith(
                                        'http',
                                      )
                                      ? category['carnet_img']
                                      : '${ApiConfig.baseUrl2}/${category['carnet_img']}',
                                  fit: BoxFit.cover,
                                  width: double.infinity,
                                  height: double.infinity,
                                  errorBuilder: (context, error, stackTrace) {
                                    // Fallback vers un gradient avec icône si l'image ne charge pas
                                    return Container(
                                      decoration: BoxDecoration(
                                        gradient: LinearGradient(
                                          begin: Alignment.topLeft,
                                          end: Alignment.bottomRight,
                                          colors: [
                                            cardColor.withValues(alpha: 0.8),
                                            cardColor,
                                          ],
                                        ),
                                        borderRadius: BorderRadius.circular(12),
                                      ),
                                      child: Center(
                                        child: Icon(
                                          cardIcon,
                                          size: 32,
                                          color: Colors.white.withValues(
                                            alpha: 0.9,
                                          ),
                                        ),
                                      ),
                                    );
                                  },
                                )
                                : Container(
                                  decoration: BoxDecoration(
                                    gradient: LinearGradient(
                                      begin: Alignment.topLeft,
                                      end: Alignment.bottomRight,
                                      colors: [
                                        cardColor.withValues(alpha: 0.8),
                                        cardColor,
                                      ],
                                    ),
                                    borderRadius: BorderRadius.circular(12),
                                  ),
                                  child: Center(
                                    child: Icon(
                                      cardIcon,
                                      size: 32,
                                      color: Colors.white.withValues(
                                        alpha: 0.9,
                                      ),
                                    ),
                                  ),
                                ),
                      ),
                      // Badge de sélection en haut à droite
                      if (isSelected)
                        Positioned(
                          top: 6,
                          right: 6,
                          child: Container(
                            padding: const EdgeInsets.all(4),
                            decoration: BoxDecoration(
                              color: Colors.white,
                              shape: BoxShape.circle,
                              boxShadow: [
                                BoxShadow(
                                  color: Colors.black.withValues(alpha: 0.2),
                                  blurRadius: 4,
                                  offset: const Offset(0, 1),
                                ),
                              ],
                            ),
                            child: Icon(
                              Icons.check,
                              color: cardColor,
                              size: 12,
                            ),
                          ),
                        ),
                    ],
                  ),
                ),
              ),

              // Contenu en bas
              Expanded(
                flex: 2,
                child: Padding(
                  padding: const EdgeInsets.fromLTRB(6, 2, 6, 4),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      // Titre
                      Text(
                        name,
                        style: const TextStyle(
                          fontSize: 12,
                          fontWeight: FontWeight.bold,
                          color: Colors.black87,
                          height: 1.0,
                        ),
                        maxLines: 1,
                        overflow: TextOverflow.ellipsis,
                      ),

                      const SizedBox(height: 4),
                      Text(
                        date_fin_cotisation.isNotEmpty
                            ? 'Fin de cotisation : $date_fin_cotisation'
                            : 'Date de fin inconnue',
                        style: TextStyle(
                          fontSize: 13,
                          fontWeight: FontWeight.bold,
                          color: Colors.red,
                          height: 1.1,
                        ),
                        maxLines: 2,
                        overflow: TextOverflow.ellipsis,
                      ),
                      // Description (flexible)
                      Expanded(
                        child: Padding(
                          padding: const EdgeInsets.only(top: 1),
                          child: Text(
                            description,
                            style: TextStyle(
                              fontSize: 12,
                              color: const Color.fromARGB(255, 255, 2, 2),
                              height: 1.0,
                            ),
                            maxLines: 2,
                            overflow: TextOverflow.ellipsis,
                          ),
                        ),
                      ),

                      // Bouton d'action (plus compact)
                      Container(
                        width: double.infinity,
                        decoration: BoxDecoration(
                          gradient: LinearGradient(
                            colors: [
                              cardColor.withValues(alpha: 0.1),
                              cardColor.withValues(alpha: 0.05),
                            ],
                          ),
                          borderRadius: BorderRadius.circular(4),
                          border: Border.all(
                            color: cardColor.withValues(alpha: 0.3),
                            width: 1,
                          ),
                        ),
                        child: Material(
                          color: Colors.transparent,
                          borderRadius: BorderRadius.circular(4),
                          child: InkWell(
                            onTap: () => _onCarnetTap(category, id, name),
                            borderRadius: BorderRadius.circular(4),
                            child: Padding(
                              padding: const EdgeInsets.symmetric(
                                vertical: 4,
                                horizontal: 6,
                              ),
                              child: Row(
                                mainAxisAlignment: MainAxisAlignment.center,
                                mainAxisSize: MainAxisSize.min,
                                children: [
                                  Icon(
                                    Icons.visibility,
                                    size: 11,
                                    color: cardColor,
                                  ),
                                  const SizedBox(width: 2),
                                  Text(
                                    'Voir',
                                    style: TextStyle(
                                      fontSize: 9,
                                      fontWeight: FontWeight.w600,
                                      color: cardColor,
                                      height: 1.0,
                                    ),
                                  ),
                                ],
                              ),
                            ),
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  // Méthode pour gérer le tap sur un carnet
  Future<void> _onCarnetTap(
    Map<String, dynamic> category,
    String id,
    String name,
  ) async {
    setState(() {
      _selectedCarnetId = id;
    });

    // Afficher un indicateur de chargement
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (BuildContext context) {
        return Center(
          child: Container(
            padding: const EdgeInsets.all(20),
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(12),
            ),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                CircularProgressIndicator(
                  valueColor: AlwaysStoppedAnimation<Color>(
                    AppTheme.color.primaryColor,
                  ),
                ),
                const SizedBox(height: 16),
                Text(
                  'Chargement...',
                  style: TextStyle(
                    color: AppTheme.color.textColor,
                    fontSize: 16,
                  ),
                ),
              ],
            ),
          ),
        );
      },
    );

    // Récupérer les éléments du carnet
    final result = await CarnetService.getCarnetElements(id);

    // Fermer l'indicateur de chargement
    Navigator.pop(context);

    if (result['success']) {
      // Naviguer vers l'écran des éléments du carnet
      Navigator.push(
        context,
        MaterialPageRoute(
          builder:
              (context) => CarnetElementsScreen(
                carnetId: id,
                carnetName: name,
                carnetData: category,
                preloadedElements: _processCarnetElements(result['data']),
              ),
        ),
      );
    } else {
      // En cas d'erreur, naviguer quand même vers l'écran des éléments
      Navigator.push(
        context,
        MaterialPageRoute(
          builder:
              (context) => CarnetElementsScreen(
                carnetId: id,
                carnetName: name,
                carnetData: category,
              ),
        ),
      );

      // Afficher un message d'erreur
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Erreur lors du chargement: ${result['message']}'),
          backgroundColor: Colors.red,
          behavior: SnackBarBehavior.floating,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(10),
          ),
        ),
      );
    }
  }

  Widget _buildCarnetCategoryCard(
    Map<String, dynamic> category,
    bool isSelected,
  ) {
    // Extraire les valeurs avec des fallbacks
    final String id =
        category['id_carnet']?.toString() ??
        category['id']?.toString() ??
        'ID inconnu';

    final String name =
        category['nom_carnet'] ??
        category['nom'] ??
        category['name'] ??
        'Carnet sans nom';

    // Description ou sous-titre (utiliser une valeur par défaut si non disponible)
    final String description =
        category['description'] ??
        category['details'] ??
        'Appuyez pour voir les détails';

    // Générer une couleur basée sur l'ID
    final int colorSeed = id.hashCode;
    final List<Color> iconColors = [
      Color(0xFF5E72E4), // Bleu Djamo
      Color(0xFF11CDEF), // Bleu clair
      Color(0xFFFB6340), // Orange
      Color(0xFF2DCE89), // Vert
      Color(0xFFF5365C), // Rose
      Color(0xFFFFD600), // Jaune
    ];

    // Icônes variées pour les carnets
    final List<IconData> cardIcons = [
      Icons.book,
      Icons.menu_book,
      Icons.library_books,
      Icons.auto_stories,
      Icons.chrome_reader_mode,
      Icons.import_contacts,
    ];

    final Color iconColor = iconColors[colorSeed % iconColors.length];
    final IconData cardIcon = cardIcons[colorSeed % cardIcons.length];

    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      decoration: BoxDecoration(
        color: isSelected ? iconColor.withOpacity(0.08) : Colors.white,
        borderRadius: BorderRadius.circular(16),
        border: Border.all(
          color: isSelected ? iconColor : Colors.grey.shade200,
          width: isSelected ? 1.5 : 1,
        ),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.03),
            blurRadius: 8,
            offset: const Offset(0, 3),
          ),
        ],
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          onTap: () async {
            setState(() {
              _selectedCarnetId = id;
            });

            // Afficher un indicateur de chargement
            showDialog(
              context: context,
              barrierDismissible: false,
              builder: (BuildContext context) {
                return const Center(child: CircularProgressIndicator());
              },
            );

            // Récupérer les éléments du carnet directement
            final result = await CarnetService.getCarnetElements(id);

            // Fermer l'indicateur de chargement
            Navigator.pop(context);

            if (result['success']) {
              // Naviguer vers l'écran des éléments du carnet avec les éléments préchargés
              Navigator.push(
                context,
                MaterialPageRoute(
                  builder:
                      (context) => CarnetElementsScreen(
                        carnetId: id,
                        carnetName: name,
                        carnetData: category,
                        preloadedElements: _processCarnetElements(
                          result['data'],
                        ),
                      ),
                ),
              );
            } else {
              // En cas d'erreur, naviguer quand même vers l'écran des éléments
              Navigator.push(
                context,
                MaterialPageRoute(
                  builder:
                      (context) => CarnetElementsScreen(
                        carnetId: id,
                        carnetName: name,
                        carnetData: category,
                      ),
                ),
              );

              // Afficher un message d'erreur
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(
                  content: Text(
                    'Erreur lors du chargement des éléments: ${result['message']}',
                  ),
                  backgroundColor: Colors.red,
                ),
              );
            }
          },
          borderRadius: BorderRadius.circular(16),
          // Reste du code inchangé...
          child: Padding(
            padding: const EdgeInsets.symmetric(vertical: 12, horizontal: 16),
            child: Row(
              children: [
                // Avatar/Icon container
                Container(
                  width: 56,
                  height: 56,
                  decoration: BoxDecoration(
                    color: isSelected ? iconColor : iconColor.withOpacity(0.1),
                    shape: BoxShape.circle,
                  ),
                  child: Center(
                    child: Icon(
                      cardIcon,
                      color: isSelected ? Colors.white : iconColor,
                      size: 28,
                    ),
                  ),
                ),
                const SizedBox(width: 16),

                // Content
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        name,
                        style: TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.bold,
                          color: AppTheme.color.textColor,
                        ),
                        maxLines: 1,
                        overflow: TextOverflow.ellipsis,
                      ),
                      const SizedBox(height: 4),
                      Text(
                        description,
                        style: TextStyle(
                          fontSize: 14,
                          color: AppTheme.color.brunGris,
                        ),
                        maxLines: 1,
                        overflow: TextOverflow.ellipsis,
                      ),
                    ],
                  ),
                ),

                // Right side - status indicator and action
                Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    if (isSelected)
                      Container(
                        padding: const EdgeInsets.symmetric(
                          horizontal: 10,
                          vertical: 6,
                        ),
                        decoration: BoxDecoration(
                          color: iconColor.withOpacity(0.1),
                          borderRadius: BorderRadius.circular(12),
                        ),
                        child: Row(
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            Icon(
                              Icons.check_circle,
                              color: iconColor,
                              size: 14,
                            ),
                            const SizedBox(width: 4),
                            Text(
                              'Actif',
                              style: TextStyle(
                                color: iconColor,
                                fontSize: 12,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                          ],
                        ),
                      ),
                    const SizedBox(width: 8),
                    Container(
                      width: 32,
                      height: 32,
                      decoration: BoxDecoration(
                        color:
                            isSelected
                                ? iconColor.withOpacity(0.1)
                                : Colors.grey.shade50,
                        shape: BoxShape.circle,
                      ),
                      child: Center(
                        child: Icon(
                          Icons.arrow_forward_ios,
                          color: isSelected ? iconColor : Colors.grey.shade400,
                          size: 14,
                        ),
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  void _showCarnetDetails(Map<String, dynamic> category) async {
    // Afficher un dialogue de chargement
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (BuildContext context) {
        return const Center(child: CircularProgressIndicator());
      },
    );

    // Récupérer les détails du carnet (simulation pour l'instant)
    // Dans une implémentation réelle, vous utiliseriez CarnetService.getCarnetDetails
    await Future.delayed(const Duration(milliseconds: 500));

    // Fermer le dialogue de chargement
    Navigator.pop(context);

    // Afficher les détails du carnet
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (BuildContext context) {
        return AlertDialog(
          title: Text(
            category['nom_carnet'] ?? 'Détails du carnet',
            style: TextStyle(
              color: AppTheme.color.primaryColor,
              fontWeight: FontWeight.bold,
            ),
          ),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                'ID: ${category['id_carnet']}',
                style: TextStyle(fontSize: 16, color: AppTheme.color.brunGris),
              ),
              const SizedBox(height: 16),
              Text(
                'Pour accéder aux détails complets et aux options de ce carnet, veuillez continuer.',
                style: TextStyle(fontSize: 14, color: AppTheme.color.textColor),
              ),
            ],
          ),
          actions: [
            TextButton(
              onPressed: () {
                Navigator.pop(context);
              },
              child: Text(
                'Annuler',
                style: TextStyle(color: AppTheme.color.brunGris),
              ),
            ),
            ElevatedButton(
              onPressed: () async {
                Navigator.pop(context);

                // Vérifier si l'utilisateur est connecté
                final isLoggedIn = await AuthService.isLoggedIn();

                if (isLoggedIn) {
                  // Récupérer les données de l'utilisateur
                  final userData = await AuthService.getUserData();

                  if (userData != null && userData.containsKey('id_client')) {
                    // Simuler la souscription à un carnet
                    _subscribeToCarnet(
                      category,
                      userData['id_client'].toString(),
                    );
                  } else {
                    _showLoginRequiredDialog();
                  }
                } else {
                  _showLoginRequiredDialog();
                }
              },
              style: ElevatedButton.styleFrom(
                backgroundColor: AppTheme.color.primaryColor,
              ),
              child: const Text('Continuer'),
            ),
          ],
        );
      },
    );
  }

  void _subscribeToCarnet(Map<String, dynamic> category, String userId) async {
    // Afficher un dialogue de chargement
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (BuildContext context) {
        return const Center(child: CircularProgressIndicator());
      },
    );

    // Simuler une souscription à un carnet
    // Dans une implémentation réelle, vous utiliseriez CarnetService.subscribeToCarnet
    await Future.delayed(const Duration(seconds: 1));

    // Fermer le dialogue de chargement
    Navigator.pop(context);

    // Afficher un message de succès
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('Carnet sélectionné: ${category['nom_carnet']}'),
        backgroundColor: AppTheme.color.primaryColor,
      ),
    );

    // Ici, vous pourriez naviguer vers un écran de détails du carnet
    // Navigator.push(context, MaterialPageRoute(builder: (context) =>
    //   CarnetDetailScreen(carnetId: category['id_carnet'].toString())));
  }

  void _showLoginRequiredDialog() {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (BuildContext context) {
        return AlertDialog(
          title: Text(
            'Connexion requise',
            style: TextStyle(
              color: AppTheme.color.primaryColor,
              fontWeight: FontWeight.bold,
            ),
          ),
          content: const Text(
            'Vous devez être connecté pour souscrire à un carnet.',
          ),
          actions: [
            TextButton(
              onPressed: () {
                Navigator.pop(context);
              },
              child: const Text('Annuler'),
            ),
            ElevatedButton(
              onPressed: () {
                Navigator.pop(context);
                // Naviguer vers l'écran de connexion
                // Navigator.pushNamed(context, '/login');
              },
              style: ElevatedButton.styleFrom(
                backgroundColor: AppTheme.color.primaryColor,
              ),
              child: const Text('Se connecter'),
            ),
          ],
        );
      },
    );
  }

  // Fonction de débogage pour afficher la structure des données
  void _debugPrintDataStructure(dynamic data) {
    print('=== STRUCTURE DES DONNÉES ===');
    if (data is Map) {
      print('Type: Map');
      print('Clés: ${data.keys.toList()}');
      data.forEach((key, value) {
        if (value is List) {
          print('$key: Liste de ${value.length} éléments');
          if (value.isNotEmpty) {
            print('Premier élément: ${value.first}');
          }
        } else {
          print('$key: $value');
        }
      });
    } else if (data is List) {
      print('Type: Liste de ${data.length} éléments');
      if (data.isNotEmpty) {
        print('Premier élément: ${data.first}');
      }
    } else {
      print('Type: ${data.runtimeType}');
      print('Valeur: $data');
    }
    print('============================');
  }
}

class AccessDeniedCard extends StatelessWidget {
  final bool isLoading;
  const AccessDeniedCard({super.key, required this.isLoading});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Center(
        child: Padding(
          padding: const EdgeInsets.all(20),
          child: Card(
            elevation: 8,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(20),
            ),
            child: Padding(
              padding: const EdgeInsets.symmetric(vertical: 40, horizontal: 30),
              child:
                  isLoading
                      ? CircularProgressIndicator()
                      : Column(
                        mainAxisSize: MainAxisSize.min,
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Container(
                            padding: const EdgeInsets.all(15),
                            decoration: BoxDecoration(
                              color: Colors.red.withOpacity(0.1),
                              shape: BoxShape.circle,
                            ),
                            child: const Icon(
                              Icons.block,
                              color: Colors.red,
                              size: 60,
                            ),
                          ),
                          const SizedBox(height: 25),
                          const Text(
                            "Accès refusé",
                            style: TextStyle(
                              fontSize: 24,
                              fontWeight: FontWeight.bold,
                              color: Colors.red,
                            ),
                          ),
                          const SizedBox(height: 10),
                          const Text(
                            "Désolé, vous ne pouvez pas accéder à cette page.",
                            textAlign: TextAlign.center,
                            style: TextStyle(
                              fontSize: 16,
                              color: Colors.black54,
                            ),
                          ),
                        ],
                      ),
            ),
          ),
        ),
      ),
    );
  }
}
