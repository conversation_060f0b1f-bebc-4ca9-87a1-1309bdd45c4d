import 'package:flutter/material.dart';
import 'package:callitris/utils/appTheme.dart';
import 'package:callitris/screens/boutique/order_detail_screen.dart';
import 'package:callitris/widgets/navigation_menu_button.dart';

class OrdersScreen extends StatefulWidget {
  const OrdersScreen({super.key});

  @override
  State<OrdersScreen> createState() => _OrdersScreenState();
}

class _OrdersScreenState extends State<OrdersScreen>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;
  bool _isLoading = true;
  List<Map<String, dynamic>> _activeOrders = [];
  List<Map<String, dynamic>> _completedOrders = [];

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 2, vsync: this);
    _loadOrders();
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  Future<void> _loadOrders() async {
    // Simuler un chargement depuis une API
    await Future.delayed(const Duration(milliseconds: 800));

    // Données fictives pour les commandes
    final List<Map<String, dynamic>> orders = [
      {
        'id': '1',
        'productName': 'Smartphone Samsung Galaxy A54',
        'totalPrice': 250000,
        'dailyPrice': 12500,
        'duration': 20,
        'versementsEffectues': 5,
        'versementsRestants': 15,
        'montantVerse': 62500,
        'montantRestant': 187500,
        'progress': 0.25,
        'imageUrl': 'assets/images/phone1.jpg',
        'nextPaymentDate': '15/09/2023',
        'status': 'En cours',
        'orderDate': '01/08/2023',
      },
      {
        'id': '2',
        'productName': 'Téléviseur LG 43 pouces Smart TV',
        'totalPrice': 180000,
        'dailyPrice': 9000,
        'duration': 20,
        'versementsEffectues': 10,
        'versementsRestants': 10,
        'montantVerse': 90000,
        'montantRestant': 90000,
        'progress': 0.5,
        'imageUrl': 'assets/images/tv1.jpg',
        'nextPaymentDate': '18/09/2023',
        'status': 'En cours',
        'orderDate': '10/07/2023',
      },
      {
        'id': '3',
        'productName': 'Machine à laver Hisense 7kg',
        'totalPrice': 150000,
        'dailyPrice': 7500,
        'duration': 20,
        'versementsEffectues': 20,
        'versementsRestants': 0,
        'montantVerse': 150000,
        'montantRestant': 0,
        'progress': 1.0,
        'imageUrl': 'assets/images/washer1.jpg',
        'completionDate': '10/08/2023',
        'status': 'Terminé',
        'orderDate': '01/06/2023',
      },
    ];

    setState(() {
      _activeOrders =
          orders.where((order) => order['status'] == 'En cours').toList();
      _completedOrders =
          orders.where((order) => order['status'] == 'Terminé').toList();
      _isLoading = false;
    });
  }

  String _formatPrice(dynamic price) {
    // Convertir en entier si nécessaire
    final int priceInt = price is int ? price : price.toInt();
    final String priceString = priceInt.toString();
    final StringBuffer result = StringBuffer();

    for (int i = 0; i < priceString.length; i++) {
      if (i > 0 && (priceString.length - i) % 3 == 0) {
        result.write(' ');
      }
      result.write(priceString[i]);
    }

    return result.toString();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Mes commandes'),
        actions: [NavigationMenuButton()],
        bottom: TabBar(
          controller: _tabController,
          tabs: const [Tab(text: 'En cours'), Tab(text: 'Terminées')],
          indicatorColor: AppTheme.color.primaryColor,
          labelColor: AppTheme.color.primaryColor,
          unselectedLabelColor: AppTheme.color.brunGris,
        ),
      ),
      body:
          _isLoading
              ? const Center(child: CircularProgressIndicator())
              : TabBarView(
                controller: _tabController,
                children: [
                  // Onglet des commandes en cours
                  _buildOrdersList(_activeOrders, true),

                  // Onglet des commandes terminées
                  _buildOrdersList(_completedOrders, false),
                ],
              ),
    );
  }

  Widget _buildOrdersList(List<Map<String, dynamic>> orders, bool isActive) {
    if (orders.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              isActive
                  ? Icons.shopping_bag_outlined
                  : Icons.check_circle_outline,
              size: 80,
              color: Colors.grey[400],
            ),
            const SizedBox(height: 16),
            Text(
              isActive
                  ? 'Aucune commande en cours'
                  : 'Aucune commande terminée',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
                color: AppTheme.color.brunGris,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              isActive
                  ? 'Parcourez notre boutique pour trouver des produits'
                  : 'Vos commandes terminées apparaîtront ici',
              style: TextStyle(color: AppTheme.color.brunGris),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 24),
            if (isActive)
              ElevatedButton(
                onPressed: () {
                  // Naviguer vers la boutique
                  Navigator.of(context).pop();
                },
                child: const Text('Voir la boutique'),
              ),
          ],
        ),
      );
    }

    return ListView.builder(
      padding: const EdgeInsets.all(16),
      itemCount: orders.length,
      itemBuilder: (context, index) {
        final order = orders[index];
        return _buildOrderCard(order);
      },
    );
  }

  Widget _buildOrderCard(Map<String, dynamic> order) {
    final bool isActive = order['status'] == 'En cours';

    return Card(
      elevation: 2,
      margin: const EdgeInsets.only(bottom: 16),
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: InkWell(
        onTap: () {
          Navigator.push(
            context,
            MaterialPageRoute(
              builder: (context) => OrderDetailScreen(orderId: order['id']),
            ),
          ).then((_) {
            // Recharger les commandes au retour
            _loadOrders();
          });
        },
        borderRadius: BorderRadius.circular(12),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Image du produit
                  ClipRRect(
                    borderRadius: BorderRadius.circular(8),
                    child: Image.asset(
                      order['imageUrl'],
                      width: 80,
                      height: 80,
                      fit: BoxFit.cover,
                      errorBuilder: (context, error, stackTrace) {
                        return Container(
                          width: 80,
                          height: 80,
                          color: Colors.grey[200],
                          child: Icon(
                            Icons.image_not_supported,
                            color: AppTheme.color.brunGris,
                            size: 30,
                          ),
                        );
                      },
                    ),
                  ),
                  const SizedBox(width: 16),

                  // Informations sur la commande
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        // Nom du produit
                        Text(
                          order['productName'],
                          style: const TextStyle(
                            fontWeight: FontWeight.bold,
                            fontSize: 16,
                          ),
                          maxLines: 2,
                          overflow: TextOverflow.ellipsis,
                        ),
                        const SizedBox(height: 8),

                        // Statut de la commande
                        Container(
                          padding: const EdgeInsets.symmetric(
                            horizontal: 8,
                            vertical: 4,
                          ),
                          decoration: BoxDecoration(
                            color:
                                isActive
                                    ? AppTheme.color.orangeColor.withOpacity(
                                      0.2,
                                    )
                                    : AppTheme.color.greenColor.withOpacity(
                                      0.2,
                                    ),
                            borderRadius: BorderRadius.circular(12),
                          ),
                          child: Text(
                            order['status'],
                            style: TextStyle(
                              color:
                                  isActive
                                      ? AppTheme.color.orangeColor
                                      : AppTheme.color.greenColor,
                              fontWeight: FontWeight.bold,
                              fontSize: 12,
                            ),
                          ),
                        ),
                        const SizedBox(height: 8),

                        // Date de commande
                        Text(
                          'Commandé le: ${order['orderDate']}',
                          style: TextStyle(
                            color: AppTheme.color.brunGris,
                            fontSize: 12,
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 16),

              // Barre de progression
              if (isActive) ...[
                const SizedBox(height: 8),
                LinearProgressIndicator(
                  value: order['progress'],
                  backgroundColor: Colors.grey[300],
                  valueColor: AlwaysStoppedAnimation<Color>(
                    AppTheme.color.primaryColor,
                  ),
                  borderRadius: BorderRadius.circular(4),
                  minHeight: 6,
                ),
                const SizedBox(height: 8),
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Text(
                      '${order['versementsEffectues']}/${order['duration']} versements',
                      style: TextStyle(
                        color: AppTheme.color.brunGris,
                        fontSize: 12,
                      ),
                    ),
                    Text(
                      '${(order['progress'] * 100).toInt()}%',
                      style: TextStyle(
                        color: AppTheme.color.primaryColor,
                        fontWeight: FontWeight.bold,
                        fontSize: 12,
                      ),
                    ),
                  ],
                ),
              ],

              const Divider(height: 24),

              // Informations sur le prix
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'Prix total',
                        style: TextStyle(
                          color: AppTheme.color.brunGris,
                          fontSize: 12,
                        ),
                      ),
                      Text(
                        '${_formatPrice(order['totalPrice'])} FCFA',
                        style: TextStyle(
                          fontWeight: FontWeight.bold,
                          color: AppTheme.color.primaryColor,
                        ),
                      ),
                    ],
                  ),
                  if (isActive)
                    Column(
                      crossAxisAlignment: CrossAxisAlignment.end,
                      children: [
                        Text(
                          'Prochain versement',
                          style: TextStyle(
                            color: AppTheme.color.brunGris,
                            fontSize: 12,
                          ),
                        ),
                        Text(
                          order['nextPaymentDate'],
                          style: TextStyle(
                            fontWeight: FontWeight.bold,
                            color: AppTheme.color.orangeColor,
                          ),
                        ),
                      ],
                    )
                  else
                    Column(
                      crossAxisAlignment: CrossAxisAlignment.end,
                      children: [
                        Text(
                          'Terminé le',
                          style: TextStyle(
                            color: AppTheme.color.brunGris,
                            fontSize: 12,
                          ),
                        ),
                        Text(
                          order['completionDate'],
                          style: TextStyle(
                            fontWeight: FontWeight.bold,
                            color: AppTheme.color.greenColor,
                          ),
                        ),
                      ],
                    ),
                ],
              ),

              // Bouton pour les commandes actives
              if (isActive) ...[
                const SizedBox(height: 16),
                SizedBox(
                  width: double.infinity,
                  child: ElevatedButton(
                    onPressed: () {
                      Navigator.push(
                        context,
                        MaterialPageRoute(
                          builder:
                              (context) =>
                                  OrderDetailScreen(orderId: order['id']),
                        ),
                      ).then((_) {
                        // Recharger les commandes au retour
                        _loadOrders();
                      });
                    },
                    style: ElevatedButton.styleFrom(
                      padding: const EdgeInsets.symmetric(vertical: 12),
                    ),
                    child: const Text('Effectuer un versement'),
                  ),
                ),
              ],
            ],
          ),
        ),
      ),
    );
  }

  // Méthode pour afficher un dialogue de confirmation
  Future<bool?> _showConfirmDialog({
    required String title,
    required String message,
    String confirmText = 'Confirmer',
    String cancelText = 'Annuler',
  }) {
    return showDialog<bool>(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: Text(title),
          content: Text(message),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(false),
              child: Text(
                cancelText,
                style: TextStyle(color: AppTheme.color.brunGris),
              ),
            ),
            ElevatedButton(
              onPressed: () => Navigator.of(context).pop(true),
              child: Text(confirmText),
            ),
          ],
        );
      },
    );
  }

  // Méthode pour annuler une commande
  Future<void> _cancelOrder(String orderId) async {
    final bool? confirmed = await _showConfirmDialog(
      title: 'Annuler la commande',
      message:
          'Êtes-vous sûr de vouloir annuler cette commande ? Cette action est irréversible.',
      confirmText: 'Annuler la commande',
      cancelText: 'Retour',
    );

    if (confirmed == true) {
      // Logique pour annuler la commande
      setState(() {
        _activeOrders.removeWhere((order) => order['id'] == orderId);
      });

      // Afficher un message de confirmation
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: const Text('Commande annulée avec succès'),
          backgroundColor: AppTheme.color.redColor,
        ),
      );
    }
  }

  // Méthode pour rafraîchir la liste des commandes
  Future<void> _refreshOrders() async {
    setState(() {
      _isLoading = true;
    });

    await _loadOrders();
  }
}
