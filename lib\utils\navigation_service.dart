import 'package:flutter/material.dart';
import 'package:callitris/utils/custom_page_route.dart';

// Navigation avec animation de glissement vers la droite
routeAnimation(BuildContext context, Widget widget) =>
    Navigator.push(context, SlideRightRoute(page: widget));

// Navigation avec animation de fondu
routeFadeAnimation(BuildContext context, Widget widget) =>
    Navigator.push(context, FadeRoute(page: widget));

// Navigation avec animation d'échelle
routeScaleAnimation(BuildContext context, Widget widget) =>
    Navigator.push(context, ScaleRoute(page: widget));

// Navigation avec animation de glissement vers le haut
routeSlideBottomAnimation(BuildContext context, Widget widget) =>
    Navigator.push(context, SlideBottomRoute(page: widget));

// Navigation avec remplacement (pour les écrans de connexion par exemple)
routeReplacement(BuildContext context, Widget widget) =>
    Navigator.pushReplacement(context, SlideRightRoute(page: widget));

// Navigation avec effacement de l'historique (pour aller à l'écran d'accueil après connexion)
routeAndRemoveUntil(BuildContext context, Widget widget) =>
    Navigator.pushAndRemoveUntil(
      context,
      SlideRightRoute(page: widget),
      (route) => false,
    );

// Obtenir la largeur de l'écran
width(BuildContext context) => MediaQuery.of(context).size.width;

// Obtenir la hauteur de l'écran
height(BuildContext context) => MediaQuery.of(context).size.height;

// Fermer le clavier
closeKeyboard(BuildContext context) => FocusScope.of(context).unfocus();

// Afficher une boîte de dialogue de confirmation
Future<bool?> showConfirmDialog({
  required BuildContext context,
  required String title,
  required String message,
  String confirmText = 'Confirmer',
  String cancelText = 'Annuler',
}) async {
  return await showDialog<bool>(
    context: context,
    barrierDismissible: false,
    builder: (BuildContext context) {
      return PopScope(
        canPop: false,
        child: AlertDialog(
          title: Text(title),
          content: Text(message),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(false),
              child: Text(cancelText),
            ),
            ElevatedButton(
              onPressed: () => Navigator.of(context).pop(true),
              child: Text(confirmText),
            ),
          ],
        ),
      );
    },
  );
}

// Afficher une boîte de dialogue d'information
Future<void> showInfoDialog({
  required BuildContext context,
  required String title,
  required String message,
  String buttonText = 'OK',
}) async {
  return await showDialog<void>(
    context: context,
    barrierDismissible: false,
    builder: (BuildContext context) {
      return PopScope(
        canPop: false,
        child: AlertDialog(
          title: Text(title),
          content: Text(message),
          actions: [
            ElevatedButton(
              onPressed: () => Navigator.of(context).pop(),
              child: Text(buttonText),
            ),
          ],
        ),
      );
    },
  );
}
