import 'package:flutter/material.dart';
import 'package:webview_flutter/webview_flutter.dart';
import 'package:url_launcher/url_launcher.dart';

class CinetPayPage extends StatefulWidget {
  final String paymentUrl;
  final String transactionId;
  final Function(Map<String, dynamic>) onPaymentResult;

  const CinetPayPage({
    super.key,
    required this.paymentUrl,
    required this.transactionId,
    required this.onPaymentResult,
  });

  @override
  State<CinetPayPage> createState() => _CinetPayPageState();
}

class _CinetPayPageState extends State<CinetPayPage>
    with WidgetsBindingObserver {
  late final WebViewController _controller;
  bool _isLoading = true;
  bool _hasError = false;
  String? _errorMessage;
  bool _isWaitingForWaveReturn = false;

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addObserver(this);
    _initializeWebView();
  }

  @override
  void dispose() {
    WidgetsBinding.instance.removeObserver(this);
    super.dispose();
  }

  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {
    super.didChangeAppLifecycleState(state);

    // Détecter quand l'utilisateur revient dans l'app après être allé dans Wave
    if (state == AppLifecycleState.resumed && _isWaitingForWaveReturn) {
      print('📱 Utilisateur de retour dans l\'app après Wave');
      _handleReturnFromWave();
    }
  }

  void _initializeWebView() {
    _controller =
        WebViewController()
          ..setJavaScriptMode(JavaScriptMode.unrestricted)
          ..setBackgroundColor(const Color(0x00000000))
          ..setUserAgent(
            'Mozilla/5.0 (Linux; Android 10; SM-G973F) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.120 Mobile Safari/537.36',
          )
          ..enableZoom(false)
          ..setNavigationDelegate(
            NavigationDelegate(
              onPageStarted: (String url) {
                print('🌐 Page started: $url');
                setState(() {
                  _isLoading = true;
                  _hasError = false;
                });
                _handleUrlChange(url);
              },
              onPageFinished: (String url) {
                print('✅ Page finished: $url');
                setState(() {
                  _isLoading = false;
                });
                _disableScrolling();
              },
              onWebResourceError: (WebResourceError error) {
                print(
                  '❌ WebView Error: ${error.description} (Code: ${error.errorCode})',
                );
                print('❌ Failed URL: ${error.url}');

                // Ne pas traiter comme une erreur fatale si c'est juste un problème d'URL scheme
                if (error.errorCode == -10 &&
                    error.url != null &&
                    _shouldHandleExternally(error.url!)) {
                  print(
                    '🔄 Erreur d\'URL scheme détectée, tentative de gestion externe...',
                  );
                  _handleExternalUrl(error.url!);
                  return; // Ne pas afficher d'erreur
                }

                setState(() {
                  _isLoading = false;
                  _hasError = true;
                  _errorMessage =
                      'Erreur de chargement: ${error.description}\nCode: ${error.errorCode}\nURL: ${error.url}';
                });
              },
              onNavigationRequest: (NavigationRequest request) {
                print('🔄 Navigation request: ${request.url}');
                _handleUrlChange(request.url);

                // Gérer les URL schemes spéciaux (intent://, market://, etc.)
                if (_shouldHandleExternally(request.url)) {
                  _handleExternalUrl(request.url);
                  return NavigationDecision.prevent;
                }

                return NavigationDecision.navigate;
              },
              onHttpError: (HttpResponseError error) {
                print('🚫 HTTP Error: ${error.response?.statusCode}');
              },
            ),
          );

    // Configurer les permissions et paramètres avancés
    _controller.setOnConsoleMessage((JavaScriptConsoleMessage message) {
      print('📱 Console: ${message.message}');
    });

    // Charger l'URL avec des headers personnalisés
    _loadUrlWithHeaders();
  }

  void _loadUrlWithHeaders() {
    final headers = {
      'Accept':
          'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
      'Accept-Language': 'fr-FR,fr;q=0.9,en;q=0.8',
      'Accept-Encoding': 'gzip, deflate, br',
      'DNT': '1',
      'Connection': 'keep-alive',
      'Upgrade-Insecure-Requests': '1',
    };

    print('🚀 Loading URL: ${widget.paymentUrl}');
    print('📋 Headers: $headers');

    _controller.loadRequest(Uri.parse(widget.paymentUrl), headers: headers);
  }

  /// Désactive le défilement dans la WebView
  void _disableScrolling() {
    _controller.runJavaScript('''
      // Désactiver le défilement sur le body et html
      document.body.style.overflow = 'hidden';
      document.documentElement.style.overflow = 'hidden';

      // Désactiver les événements de défilement
      document.addEventListener('touchmove', function(e) {
        e.preventDefault();
      }, { passive: false });

      document.addEventListener('wheel', function(e) {
        e.preventDefault();
      }, { passive: false });

      // Fixer la hauteur du viewport
      document.body.style.height = '100vh';
      document.body.style.maxHeight = '100vh';
      document.documentElement.style.height = '100vh';
      document.documentElement.style.maxHeight = '100vh';

      // Désactiver le zoom
      var meta = document.createElement('meta');
      meta.name = 'viewport';
      meta.content = 'width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no';
      document.getElementsByTagName('head')[0].appendChild(meta);
    ''');
  }

  void _handleUrlChange(String url) {
    print('Navigation vers: $url');

    // Vérifier les URLs de callback CinetPay
    if (_isPaymentSuccessUrl(url)) {
      // Paiement réussi
      _handlePaymentSuccess();
    } else if (_isPaymentCancelUrl(url)) {
      // Paiement annulé
      _handlePaymentCancel();
    } else if (_isPaymentErrorUrl(url)) {
      // Paiement échoué
      _handlePaymentError();
    }
  }

  /// Vérifie si l'URL indique un paiement réussi
  bool _isPaymentSuccessUrl(String url) {
    final successPatterns = [
      'cinetpay.com/return',
      'cinetpay.com/success',
      'pay.wave.com/success',
      'wave.com/success',
      '/return',
      '/success',
      'status=success',
      'payment_status=completed',
      'payment_status=success',
      'transaction_status=success',
    ];

    final lowerUrl = url.toLowerCase();
    return successPatterns.any((pattern) => lowerUrl.contains(pattern));
  }

  /// Vérifie si l'URL indique un paiement annulé
  bool _isPaymentCancelUrl(String url) {
    final cancelPatterns = [
      'cinetpay.com/cancel',
      'pay.wave.com/cancel',
      'wave.com/cancel',
      '/cancel',
      'status=cancel',
      'payment_status=cancelled',
      'transaction_status=cancelled',
    ];

    final lowerUrl = url.toLowerCase();
    return cancelPatterns.any((pattern) => lowerUrl.contains(pattern));
  }

  /// Vérifie si l'URL indique un paiement échoué
  bool _isPaymentErrorUrl(String url) {
    final errorPatterns = [
      'cinetpay.com/error',
      'pay.wave.com/error',
      'wave.com/error',
      '/error',
      'status=error',
      'status=failed',
      'payment_status=failed',
      'transaction_status=failed',
    ];

    final lowerUrl = url.toLowerCase();
    return errorPatterns.any((pattern) => lowerUrl.contains(pattern));
  }

  /// Vérifie si une URL doit être gérée par une application externe
  bool _shouldHandleExternally(String url) {
    final uri = Uri.tryParse(url);
    if (uri == null) return false;

    // URL schemes qui nécessitent une application externe
    final externalSchemes = [
      'intent', // Android intent URLs
      'market', // Google Play Store
      'tel', // Numéros de téléphone
      'sms', // SMS
      'mailto', // Email
      'whatsapp', // WhatsApp
      'fb', // Facebook
      'twitter', // Twitter
      'instagram', // Instagram
      'wave', // Wave (paiement mobile)
      'orange', // Orange Money
      'mtn', // MTN Mobile Money
      'moov', // Moov Money
    ];

    return externalSchemes.contains(uri.scheme.toLowerCase());
  }

  /// Gère les URL qui doivent être ouvertes dans des applications externes
  Future<void> _handleExternalUrl(String url) async {
    print('🔗 Tentative d\'ouverture d\'URL externe: $url');

    try {
      // Cas spécial pour les intent URLs Android
      if (url.startsWith('intent://')) {
        await _handleAndroidIntent(url);
        return;
      }

      // Cas spécial pour Wave avec URL intégrée
      if (url.startsWith('wave://capture/')) {
        await _handleWaveUrl(url);
        return;
      }

      // Pour les autres URL schemes
      final uri = Uri.parse(url);
      if (await canLaunchUrl(uri)) {
        await launchUrl(uri, mode: LaunchMode.externalApplication);
        print('✅ URL externe ouverte avec succès');
      } else {
        print('❌ Impossible d\'ouvrir l\'URL externe: $url');
        _showExternalUrlError(url);
      }
    } catch (e) {
      print('❌ Erreur lors de l\'ouverture de l\'URL externe: $e');
      _showExternalUrlError(url);
    }
  }

  /// Gère spécifiquement les URL Wave avec capture
  Future<void> _handleWaveUrl(String waveUrl) async {
    try {
      print('🌊 Traitement de l\'URL Wave: $waveUrl');

      // Extraire l'URL Wave réelle de wave://capture/https://pay.wave.com/...
      if (waveUrl.startsWith('wave://capture/https://')) {
        final realWaveUrl = waveUrl.substring('wave://capture/'.length);
        print('🔄 URL Wave extraite: $realWaveUrl');

        // Essayer d'ouvrir l'URL Wave réelle dans le navigateur
        final uri = Uri.parse(realWaveUrl);
        if (await canLaunchUrl(uri)) {
          await launchUrl(uri, mode: LaunchMode.externalApplication);
          print('✅ URL Wave ouverte dans le navigateur');
          return;
        }
      }

      // Si l'extraction échoue, essayer d'ouvrir Wave directement
      final waveAppUri = Uri.parse('wave://');
      if (await canLaunchUrl(waveAppUri)) {
        await launchUrl(waveAppUri, mode: LaunchMode.externalApplication);
        print('✅ Application Wave ouverte directement');

        // Informer l'utilisateur qu'il doit compléter le paiement dans Wave
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text(
                'Veuillez compléter le paiement dans l\'application Wave',
              ),
              backgroundColor: Colors.blue,
              duration: Duration(seconds: 5),
            ),
          );

          // Démarrer un timer pour vérifier le retour de l'utilisateur
          _startPaymentReturnTimer();
        }
      } else {
        throw Exception('Application Wave non accessible');
      }
    } catch (e) {
      print('❌ Erreur lors du traitement Wave: $e');
      _showWaveInstallDialog();
    }
  }

  /// Gère spécifiquement les intent URLs Android
  Future<void> _handleAndroidIntent(String intentUrl) async {
    try {
      // Extraire l'URL HTTPS de l'intent
      final regex = RegExp(r'intent://([^#]+)#Intent;scheme=([^;]+);');
      final match = regex.firstMatch(intentUrl);

      if (match != null) {
        final path = match.group(1);
        final scheme = match.group(2);
        final httpsUrl = '$scheme://$path';

        print('🔄 Conversion intent -> HTTPS: $httpsUrl');

        final uri = Uri.parse(httpsUrl);
        if (await canLaunchUrl(uri)) {
          await launchUrl(uri, mode: LaunchMode.externalApplication);
          print('✅ Intent URL convertie et ouverte');
          return;
        }
      }

      // Si la conversion échoue, essayer d'ouvrir l'intent directement
      final uri = Uri.parse(intentUrl);
      if (await canLaunchUrl(uri)) {
        await launchUrl(uri, mode: LaunchMode.externalApplication);
        print('✅ Intent URL ouverte directement');
      } else {
        throw Exception('Impossible d\'ouvrir l\'intent URL');
      }
    } catch (e) {
      print('❌ Erreur lors du traitement de l\'intent: $e');
      _showExternalUrlError(intentUrl);
    }
  }

  /// Affiche une erreur pour les URL externes non supportées
  void _showExternalUrlError(String url) {
    if (!mounted) return;

    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(
          'Application requise non installée pour: ${Uri.parse(url).scheme}',
        ),
        backgroundColor: Colors.orange,
        action: SnackBarAction(label: 'Continuer', onPressed: () {}),
      ),
    );
  }

  /// Affiche un dialogue pour installer Wave
  void _showWaveInstallDialog() {
    if (!mounted) return;

    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (BuildContext context) {
        return AlertDialog(
          title: const Text('Application Wave requise'),
          content: const Text(
            'Pour effectuer ce paiement, vous devez avoir l\'application Wave installée. '
            'Voulez-vous l\'installer depuis le Google Play Store ?',
          ),
          actions: [
            TextButton(
              onPressed: () {
                Navigator.of(context).pop();
              },
              child: const Text('Annuler'),
            ),
            ElevatedButton(
              onPressed: () async {
                Navigator.of(context).pop();

                // Ouvrir le Play Store pour installer Wave
                final playStoreUri = Uri.parse(
                  'market://details?id=com.wave.personal',
                );
                final playStoreHttpsUri = Uri.parse(
                  'https://play.google.com/store/apps/details?id=com.wave.personal',
                );

                try {
                  if (await canLaunchUrl(playStoreUri)) {
                    await launchUrl(
                      playStoreUri,
                      mode: LaunchMode.externalApplication,
                    );
                  } else if (await canLaunchUrl(playStoreHttpsUri)) {
                    await launchUrl(
                      playStoreHttpsUri,
                      mode: LaunchMode.externalApplication,
                    );
                  } else {
                    if (mounted) {
                      ScaffoldMessenger.of(context).showSnackBar(
                        const SnackBar(
                          content: Text('Impossible d\'ouvrir le Play Store'),
                          backgroundColor: Colors.red,
                        ),
                      );
                    }
                  }
                } catch (e) {
                  print('Erreur lors de l\'ouverture du Play Store: $e');
                }
              },
              style: ElevatedButton.styleFrom(
                backgroundColor: const Color(0xFF2E7D32),
              ),
              child: const Text('Installer Wave'),
            ),
          ],
        );
      },
    );
  }

  void _handlePaymentSuccess() {
    print('🎉 Paiement réussi détecté !');

    // Fermer la WebView et retourner le résultat de succès
    final result = {
      'success': true,
      'status': 'ACCEPTED',
      'transaction_id': widget.transactionId,
      'message': '🎉 Paiement effectué avec succès !',
    };

    widget.onPaymentResult(result);
    Navigator.of(context).pop(result);
  }

  void _handlePaymentCancel() {
    print('❌ Paiement annulé détecté');

    final result = {
      'success': false,
      'status': 'CANCELLED',
      'transaction_id': widget.transactionId,
      'message': 'Paiement annulé par l\'utilisateur',
    };

    widget.onPaymentResult(result);
    Navigator.of(context).pop(result);
  }

  void _handlePaymentError() {
    print('💥 Erreur de paiement détectée');

    final result = {
      'success': false,
      'status': 'FAILED',
      'transaction_id': widget.transactionId,
      'message': 'Erreur lors du paiement. Veuillez réessayer.',
    };

    widget.onPaymentResult(result);
    Navigator.of(context).pop(result);
  }

  Future<void> _openInExternalBrowser() async {
    try {
      final uri = Uri.parse(widget.paymentUrl);
      if (await canLaunchUrl(uri)) {
        await launchUrl(uri, mode: LaunchMode.externalApplication);

        // Vérifier si le widget est encore monté avant d'utiliser le contexte
        if (!mounted) return;

        // Fermer la WebView et retourner un résultat en attente
        widget.onPaymentResult({
          'success': false,
          'status': 'EXTERNAL_BROWSER',
          'transaction_id': widget.transactionId,
          'message':
              'Paiement ouvert dans le navigateur externe. Veuillez vérifier le statut manuellement.',
        });
        Navigator.of(context).pop();
      } else {
        if (!mounted) return;
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Impossible d\'ouvrir le navigateur externe'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } catch (e) {
      print('Erreur lors de l\'ouverture du navigateur: $e');
      if (!mounted) return;
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('Erreur: $e'), backgroundColor: Colors.red),
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.white,
      appBar: AppBar(
        title: const Text(
          'Paiement sécurisé',
          style: TextStyle(
            fontSize: 18,
            fontWeight: FontWeight.w600,
            color: Colors.white,
          ),
        ),
        backgroundColor: const Color(0xFF2E7D32),
        elevation: 0,
        iconTheme: const IconThemeData(color: Colors.white),
        leading: IconButton(
          icon: const Icon(Icons.close),
          onPressed: () {
            _showExitConfirmation();
          },
        ),
        actions: [
          // Bouton pour ouvrir dans le navigateur externe
          IconButton(
            icon: const Icon(Icons.open_in_browser),
            tooltip: 'Ouvrir dans le navigateur',
            onPressed: _openInExternalBrowser,
          ),
          if (_hasError)
            IconButton(
              icon: const Icon(Icons.refresh),
              tooltip: 'Recharger',
              onPressed: () {
                setState(() {
                  _hasError = false;
                  _isLoading = true;
                });
                _loadUrlWithHeaders(); // Utiliser la méthode avec headers
              },
            ),
        ],
      ),
      body: SizedBox(
        height: MediaQuery.of(context).size.height,
        width: MediaQuery.of(context).size.width,
        child: Stack(
          children: [
            if (!_hasError)
              SizedBox(
                height: MediaQuery.of(context).size.height * 1.1,
                width: MediaQuery.of(context).size.width,
                child: SingleChildScrollView(
                  physics: const ClampingScrollPhysics(),
                  child: SizedBox(
                    height: MediaQuery.of(context).size.height * 1.3,
                    width: MediaQuery.of(context).size.width,
                    child: WebViewWidget(controller: _controller),
                  ),
                ),
              )
            else
              _buildErrorWidget(),

            if (_isLoading)
              Container(
                color: Colors.white,
                height: MediaQuery.of(context).size.height,
                width: MediaQuery.of(context).size.width,
                child: const Center(
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      CircularProgressIndicator(color: Color(0xFF2E7D32)),
                      SizedBox(height: 16),
                      Text(
                        'Chargement de la page de paiement...',
                        style: TextStyle(fontSize: 16, color: Colors.grey),
                      ),
                    ],
                  ),
                ),
              ),
          ],
        ),
      ),
      bottomNavigationBar: Container(
        height: 100,
        decoration: const BoxDecoration(
          color: Color(0xFF2E7D32),
          boxShadow: [
            BoxShadow(
              color: Colors.black12,
              offset: Offset(0, -2),
              blurRadius: 4,
            ),
          ],
        ),
        child: Material(
          color: Colors.transparent,
          child: InkWell(
            onTap: () {
              _showExitConfirmation();
            },
            child: const Align(
              alignment: Alignment.topCenter,
              child: Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(Icons.arrow_back, color: Colors.white, size: 30),
                  SizedBox(width: 10),
                  Text(
                    'Revenir sur l\'application Callitris Pay',
                    style: TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.w600,
                      color: Colors.white,
                    ),
                  ),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildErrorWidget() {
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(32.0),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.error_outline, size: 64, color: Colors.red.shade300),
            const SizedBox(height: 24),
            const Text(
              'Erreur de chargement',
              style: TextStyle(
                fontSize: 20,
                fontWeight: FontWeight.w600,
                color: Colors.black87,
              ),
            ),
            const SizedBox(height: 16),
            Text(
              _errorMessage ?? 'Impossible de charger la page de paiement',
              style: const TextStyle(
                fontSize: 14,
                color: Colors.grey,
                height: 1.4,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 32),
            SizedBox(
              width: double.infinity,
              child: ElevatedButton.icon(
                onPressed: _openInExternalBrowser,
                icon: const Icon(Icons.open_in_browser, color: Colors.white),
                label: const Text(
                  'Ouvrir dans le navigateur',
                  style: TextStyle(color: Colors.white),
                ),
                style: ElevatedButton.styleFrom(
                  backgroundColor: const Color(0xFF1976D2),
                  padding: const EdgeInsets.symmetric(vertical: 12),
                ),
              ),
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                Expanded(
                  child: OutlinedButton(
                    onPressed: () => Navigator.of(context).pop(),
                    child: const Text('Annuler'),
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: ElevatedButton(
                    onPressed: () {
                      setState(() {
                        _hasError = false;
                        _isLoading = true;
                      });
                      _loadUrlWithHeaders();
                    },
                    style: ElevatedButton.styleFrom(
                      backgroundColor: const Color(0xFF2E7D32),
                    ),
                    child: const Text('Réessayer'),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  void _showExitConfirmation() {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (BuildContext context) {
        return AlertDialog(
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(16),
          ),
          title: Row(
            children: [
              Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: Colors.orange.shade100,
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Icon(
                  Icons.warning_amber_rounded,
                  color: Colors.orange.shade700,
                  size: 24,
                ),
              ),
              const SizedBox(width: 12),
              const Expanded(
                child: Text(
                  'Annuler le paiement',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.w600,
                    color: Colors.black87,
                  ),
                ),
              ),
            ],
          ),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              const Text(
                'Êtes-vous sûr de vouloir quitter cette page ?',
                style: TextStyle(
                  fontSize: 16,
                  color: Colors.black87,
                  height: 1.4,
                ),
              ),
              const SizedBox(height: 12),
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: Colors.red.shade50,
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(color: Colors.red.shade200),
                ),
                child: Row(
                  children: [
                    Icon(
                      Icons.info_outline,
                      color: Colors.red.shade600,
                      size: 20,
                    ),
                    const SizedBox(width: 8),
                    const Expanded(
                      child: Text(
                        'Votre paiement sera annulé et vous devrez recommencer.',
                        style: TextStyle(
                          fontSize: 14,
                          color: Colors.black87,
                          height: 1.3,
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
          actionsPadding: const EdgeInsets.fromLTRB(16, 0, 16, 16),
          actions: [
            Row(
              children: [
                Expanded(
                  child: OutlinedButton(
                    onPressed: () => Navigator.of(context).pop(),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Color(0xFF2E7D32),
                      foregroundColor: Colors.white,
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(8),
                      ),
                      padding: const EdgeInsets.symmetric(vertical: 12),
                      elevation: 2,
                    ),
                    child: const Text(
                      'Non, continuer',
                      style: TextStyle(fontWeight: FontWeight.w600),
                    ),
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: ElevatedButton(
                    onPressed: () {
                      Navigator.of(context).pop(); // Fermer le dialog
                      widget.onPaymentResult({
                        'success': false,
                        'status': 'CANCELLED',
                        'transaction_id': widget.transactionId,
                        'message': 'Paiement annulé par l\'utilisateur',
                      });
                      Navigator.of(context).pop(); // Fermer la page de paiement
                    },
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.red.shade600,
                      foregroundColor: Colors.white,
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(8),
                      ),
                      padding: const EdgeInsets.symmetric(vertical: 12),
                      elevation: 2,
                    ),
                    child: const Text(
                      'Oui, annuler',
                      style: TextStyle(fontWeight: FontWeight.w600),
                    ),
                  ),
                ),
              ],
            ),
          ],
        );
      },
    );
  }

  /// Démarre le timer pour attendre le retour de Wave
  void _startPaymentReturnTimer() {
    setState(() {
      _isWaitingForWaveReturn = true;
    });
  }

  /// Gère le retour de l'utilisateur depuis Wave
  void _handleReturnFromWave() {
    setState(() {
      _isWaitingForWaveReturn = false;
    });

    // Afficher un dialogue pour demander le statut du paiement
    _showPaymentStatusDialog();
  }

  /// Affiche un dialogue pour demander le statut du paiement Wave
  void _showPaymentStatusDialog() {
    if (!mounted) return;

    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (BuildContext context) {
        return AlertDialog(
          title: const Row(
            children: [
              Icon(Icons.payment, color: Color(0xFF2E7D32)),
              SizedBox(width: 12),
              Text('Statut du paiement'),
            ],
          ),
          content: const Text(
            'Avez-vous complété le paiement dans l\'application Wave ?',
          ),
          actions: [
            TextButton(
              onPressed: () {
                Navigator.of(context).pop();
                _handlePaymentCancel();
              },
              child: const Text('Non, annuler'),
            ),
            ElevatedButton(
              onPressed: () {
                Navigator.of(context).pop();
                _handlePaymentSuccess();
              },
              style: ElevatedButton.styleFrom(
                backgroundColor: const Color(0xFF2E7D32),
              ),
              child: const Text(
                'Oui, paiement effectué',
                style: TextStyle(color: Colors.white),
              ),
            ),
          ],
        );
      },
    );
  }
}
