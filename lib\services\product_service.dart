import 'dart:convert';
import 'package:http/http.dart' as http;
import '../config/api_config.dart';

class ProductService {
  // Récupérer la liste des produits
  static Future<Map<String, dynamic>> getProducts() async {
    try {
      final response = await http.get(
        Uri.parse('${ApiConfig.baseUrl}/products.php'),
        headers: {
          'Content-Type': 'application/json',
        },
      );
      
      if (response.statusCode == 200) {
        return jsonDecode(response.body);
      } else {
        return {
          'success': false,
          'message': 'Erreur lors de la récupération des produits.',
        };
      }
    } catch (e) {
      return {
        'success': false,
        'message': 'Erreur de connexion. Veuillez vérifier votre connexion internet.',
      };
    }
  }
}
