class User {
  final String id;
  final String nom;
  final String prenom;
  final String telephone;
  final DateTime dateNaissance;
  final String ville;
  final String commune;
  final String sexe;
  final String? email;
  
  User({
    required this.id,
    required this.nom,
    required this.prenom,
    required this.telephone,
    required this.dateNaissance,
    required this.ville,
    required this.commune,
    required this.sexe,
    this.email,
  });
  
  factory User.fromJson(Map<String, dynamic> json) {
    return User(
      id: json['id'],
      nom: json['nom'],
      prenom: json['prenom'],
      telephone: json['telephone'],
      dateNaissance: DateTime.parse(json['dateNaissance']),
      ville: json['ville'],
      commune: json['commune'],
      sexe: json['sexe'],
      email: json['email'],
    );
  }
  
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'nom': nom,
      'prenom': prenom,
      'telephone': telephone,
      'dateNaissance': dateNaissance.toIso8601String(),
      'ville': ville,
      'commune': commune,
      'sexe': sexe,
      'email': email,
    };
  }
}