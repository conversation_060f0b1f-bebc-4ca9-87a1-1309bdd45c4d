import 'package:flutter/material.dart';
import 'package:callitris/models/cinetpay_transaction.dart';
import 'package:callitris/services/transaction_history_service.dart';
import 'package:callitris/widgets/transaction_status_widget.dart';
import 'package:callitris/widgets/navigation_menu_button.dart';
import 'package:callitris/theme/app_theme.dart';

/// Écran pour afficher l'historique des transactions CinetPay
class TransactionHistoryScreen extends StatefulWidget {
  const TransactionHistoryScreen({super.key});

  @override
  State<TransactionHistoryScreen> createState() =>
      _TransactionHistoryScreenState();
}

class _TransactionHistoryScreenState extends State<TransactionHistoryScreen>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;
  List<CinetPayTransaction> _allTransactions = [];
  List<CinetPayTransaction> _filteredTransactions = [];
  bool _isLoading = true;
  Map<String, dynamic> _stats = {};

  // Filtres
  TransactionStatus? _selectedStatus;
  TransactionType? _selectedType;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 4, vsync: this);
    _tabController.addListener(_onTabChanged);
    _loadTransactions();
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  void _onTabChanged() {
    setState(() {
      switch (_tabController.index) {
        case 0:
          _selectedStatus = null;
          break;
        case 1:
          _selectedStatus = TransactionStatus.completed;
          break;
        case 2:
          _selectedStatus = TransactionStatus.failed;
          break;
        case 3:
          _selectedStatus = TransactionStatus.pending;
          break;
      }
      _applyFilters();
    });
  }

  Future<void> _loadTransactions() async {
    setState(() => _isLoading = true);

    try {
      final transactions = await TransactionHistoryService.getTransactions();
      final stats = await TransactionHistoryService.getTransactionStats();

      setState(() {
        _allTransactions = transactions;
        _stats = stats;
        _applyFilters();
        _isLoading = false;
      });
    } catch (e) {
      setState(() => _isLoading = false);
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Erreur lors du chargement: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  void _applyFilters() {
    _filteredTransactions =
        _allTransactions.where((transaction) {
          if (_selectedStatus != null &&
              transaction.status != _selectedStatus) {
            return false;
          }
          if (_selectedType != null && transaction.type != _selectedType) {
            return false;
          }
          return true;
        }).toList();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppTheme.color.secondaryColor,
      appBar: AppBar(
        title: const Text(
          'Historique des paiements',
          style: TextStyle(
            fontSize: 18,
            fontWeight: FontWeight.w600,
            color: Colors.white,
          ),
        ),
        backgroundColor: const Color(0xFF2E7D32),
        elevation: 0,
        iconTheme: const IconThemeData(color: Colors.white),
        actions: [
          NavigationMenuButton(iconColor: const Color.fromARGB(255, 0, 0, 0)),
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: _loadTransactions,
          ),
        ],
        bottom: TabBar(
          controller: _tabController,
          indicatorColor: Colors.white,
          labelColor: Colors.white,
          unselectedLabelColor: Colors.white70,
          labelStyle: const TextStyle(
            fontSize: 12,
            fontWeight: FontWeight.w600,
          ),
          tabs: [
            Tab(text: 'Toutes (${_allTransactions.length})'),
            Tab(text: 'Réussies (${_stats['completed'] ?? 0})'),
            Tab(text: 'Échouées (${_stats['failed'] ?? 0})'),
            Tab(text: 'En attente (${_stats['pending'] ?? 0})'),
          ],
        ),
      ),
      body:
          _isLoading
              ? const Center(child: CircularProgressIndicator())
              : Column(
                children: [
                  // Statistiques
                  if (_stats.isNotEmpty) _buildStatsCard(),

                  // Liste des transactions
                  Expanded(
                    child: TabBarView(
                      controller: _tabController,
                      children: [
                        _buildTransactionList(),
                        _buildTransactionList(),
                        _buildTransactionList(),
                        _buildTransactionList(),
                      ],
                    ),
                  ),
                ],
              ),
    );
  }

  Widget _buildStatsCard() {
    return Container(
      margin: const EdgeInsets.all(16),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            'Résumé des transactions',
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.w600,
              color: Color(0xFF0A0A0A),
            ),
          ),
          const SizedBox(height: 12),

          Row(
            children: [
              Expanded(
                child: _buildStatItem(
                  'Total',
                  '${_stats['total'] ?? 0}',
                  Colors.blue,
                ),
              ),
              Expanded(
                child: _buildStatItem(
                  'Montant total',
                  '${(_stats['totalAmount'] ?? 0.0).toStringAsFixed(0)} FCFA',
                  const Color(0xFF4CAF50),
                ),
              ),
            ],
          ),

          const SizedBox(height: 8),

          Row(
            children: [
              Expanded(
                child: _buildStatItem(
                  'Taux de réussite',
                  '${(_stats['successRate'] ?? 0.0).toStringAsFixed(1)}%',
                  Colors.orange,
                ),
              ),
              Expanded(
                child: _buildStatItem(
                  'Montant payé',
                  '${(_stats['completedAmount'] ?? 0.0).toStringAsFixed(0)} FCFA',
                  const Color(0xFF2E7D32),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildStatItem(String label, String value, Color color) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          label,
          style: TextStyle(fontSize: 12, color: Colors.grey.shade600),
        ),
        const SizedBox(height: 4),
        Text(
          value,
          style: TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.w700,
            color: color,
          ),
        ),
      ],
    );
  }

  Widget _buildTransactionList() {
    return RefreshIndicator(
      onRefresh: _loadTransactions,
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: TransactionListWidget(
          transactions: _filteredTransactions,
          onTransactionTap: _showTransactionDetails,
          emptyMessage: _getEmptyMessage(),
        ),
      ),
    );
  }

  String _getEmptyMessage() {
    switch (_tabController.index) {
      case 1:
        return 'Aucune transaction réussie';
      case 2:
        return 'Aucune transaction échouée';
      case 3:
        return 'Aucune transaction en attente';
      default:
        return 'Aucune transaction trouvée';
    }
  }

  void _showTransactionDetails(CinetPayTransaction transaction) {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder:
          (context) => DraggableScrollableSheet(
            initialChildSize: 0.7,
            maxChildSize: 0.9,
            minChildSize: 0.5,
            builder:
                (context, scrollController) => Container(
                  decoration: const BoxDecoration(
                    color: Colors.white,
                    borderRadius: BorderRadius.vertical(
                      top: Radius.circular(20),
                    ),
                  ),
                  child: Column(
                    children: [
                      // Handle
                      Container(
                        margin: const EdgeInsets.only(top: 8),
                        width: 40,
                        height: 4,
                        decoration: BoxDecoration(
                          color: Colors.grey.shade300,
                          borderRadius: BorderRadius.circular(2),
                        ),
                      ),

                      // Header
                      Padding(
                        padding: const EdgeInsets.all(16),
                        child: Row(
                          children: [
                            const Text(
                              'Détails de la transaction',
                              style: TextStyle(
                                fontSize: 18,
                                fontWeight: FontWeight.w600,
                                color: Color(0xFF0A0A0A),
                              ),
                            ),
                            const Spacer(),
                            IconButton(
                              icon: const Icon(Icons.close),
                              onPressed: () => Navigator.pop(context),
                            ),
                          ],
                        ),
                      ),

                      // Content
                      Expanded(
                        child: SingleChildScrollView(
                          controller: scrollController,
                          padding: const EdgeInsets.symmetric(horizontal: 16),
                          child: TransactionStatusWidget(
                            transaction: transaction,
                            showDetails: true,
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
          ),
    );
  }
}
