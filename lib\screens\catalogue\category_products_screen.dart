import 'package:flutter/material.dart';
import 'package:callitris/utils/appTheme.dart';
import 'package:callitris/screens/boutique/product_detail_screen.dart';

import '../../services/catalogue_service.dart';

class CategoryProductsScreen extends StatefulWidget {
  final String categoryId;
  final String categoryName;
  final List<Map<String, dynamic>>? initialProducts;

  const CategoryProductsScreen({
    super.key,
    required this.categoryId,
    required this.categoryName,
    this.initialProducts,
  });

  @override
  State<CategoryProductsScreen> createState() => _CategoryProductsScreenState();
}

class _CategoryProductsScreenState extends State<CategoryProductsScreen> {
  late List<Map<String, dynamic>> _products;
  bool _isLoading = true;
  String _sortBy = 'popularity'; // popularity, priceAsc, priceDesc
  bool _showCashOnly = false;
  bool _showInstallmentOnly = false;

  @override
  void initState() {
    super.initState();
    if (widget.initialProducts != null && widget.initialProducts!.isNotEmpty) {
      _products = widget.initialProducts!;
      _isLoading = false;
      _sortProducts();
    } else {
      _loadProducts();
    }
  }

  Future<void> _loadProducts() async {
    setState(() {
      _isLoading = true;
    });

    try {
      // Si nous avons des produits initiaux, les utiliser
      if (widget.initialProducts != null &&
          widget.initialProducts!.isNotEmpty) {
        _products = widget.initialProducts!;
      } else {
        // Sinon, charger les produits depuis l'API
        final products = await CatalogueService.getProductsByCategory(
          widget.categoryId,
        );

        if (products.isNotEmpty) {
          print('Produits trouvés, formatage en cours... $products');
          _products =
              products.map((product) {
                // Traiter l'URL de l'image
                String imageUrl =
                    product['photo_kit'] ?? 'assets/images/product_default.jpg';
                if (imageUrl.contains('public/img') &&
                    !imageUrl.startsWith('http')) {
                  // Traiter l'URL de l'image
                  String imageUrl =
                      product['photo_kit'] ??
                      'assets/images/product_default.jpg';
                  if (imageUrl.contains('public/img') &&
                      !imageUrl.startsWith('http')) {
                    imageUrl =
                        'https://app.callitris-distribution.com/app/${imageUrl.replaceAll('../../', '')}';
                  }
                }

                return {
                  'id': product['id_produit'].toString(),
                  'name': product['nom_prod'],
                  'price':
                      int.tryParse(product['pv_unitaire']?.toString() ?? '0') ??
                      0,
                  'cashPrice':
                      (int.tryParse(
                            product['pv_unitaire']?.toString() ?? '0',
                          ) ??
                          0) *
                      0.85, // 15% de réduction
                  'dailyPrice': _parseToInt(product['journalier_cat']),
                  'imageUrl': imageUrl,
                  'rating': 4.5, // Valeur par défaut
                  'inStock': product['in_stock'] == 1,
                  'description':
                      product['description_prod'] ??
                      'Aucune description disponible',
                  'categoryId': product['categorie_id'].toString(),
                  'popularity': 80, // Valeur par défaut
                  'livraison': product['livraison'] ?? 'indisponible',
                  'delai_livraison': product['delai_livraison'] ?? 'pas de délai',
                  'garantie': product['garantie'] ?? 'aucune',
                };
              }).toList();
        } else {
          _products = [];
        }
      }

      _sortProducts();
    } catch (e) {
      print('Erreur lors du chargement des produits: $e');
      _products = [];
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  // Méthode utilitaire pour convertir différents types en int
  int _parseToInt(dynamic value) {
    if (value == null) return 0;
    if (value is int) return value;
    if (value is double) return value.toInt();
    if (value is String) return int.tryParse(value) ?? 0;
    return 0;
  }

  void _sortProducts() {
    switch (_sortBy) {
      case 'popularity':
        _products.sort((a, b) => b['popularity'].compareTo(a['popularity']));
        break;
      case 'priceAsc':
        _products.sort((a, b) => a['price'].compareTo(b['price']));
        break;
      case 'priceDesc':
        _products.sort((a, b) => b['price'].compareTo(a['price']));
        break;
    }

    // Filtrer les produits selon les options sélectionnées
    if (_showCashOnly || _showInstallmentOnly) {
      // Pas de filtrage réel ici car tous les produits ont les deux options
    }
  }

  @override
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(widget.categoryName),
        actions: [
          IconButton(
            icon: const Icon(Icons.search),
            onPressed: () {
              // Ouvrir la recherche
            },
          ),
          IconButton(
            icon: const Icon(Icons.filter_list),
            onPressed: () {
              _showFilterBottomSheet();
            },
          ),
        ],
      ),
      body:
          _isLoading
              ? const Center(child: CircularProgressIndicator())
              : Column(
                children: [
                  // Options de tri et filtrage
                  _buildSortingOptions(),

                  // Liste des produits
                  Expanded(
                    child: RefreshIndicator(
                      onRefresh: () async {
                        await _loadProducts();
                      },
                      child:
                          _products.isEmpty
                              ? _buildEmptyState()
                              : GridView.builder(
                                padding: const EdgeInsets.all(16),
                                gridDelegate:
                                    const SliverGridDelegateWithFixedCrossAxisCount(
                                      crossAxisCount: 2,
                                      childAspectRatio: 0.68,
                                      crossAxisSpacing: 16,
                                      mainAxisSpacing: 16,
                                    ),
                                itemCount: _products.length,
                                itemBuilder: (context, index) {
                                  return _buildProductCard(_products[index]);
                                },
                              ),
                    ),
                  ),
                ],
              ),
    );
  }

  Widget _buildEmptyState() {
    return SingleChildScrollView(
      physics: const AlwaysScrollableScrollPhysics(),
      child: Center(
        child: Padding(
          padding: const EdgeInsets.all(24.0),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(
                Icons.inventory_2_outlined,
                size: 80,
                color: Colors.grey[300],
              ),
              const SizedBox(height: 16),
              Text(
                'Aucun produit disponible',
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                  color: Colors.grey[600],
                ),
              ),
              const SizedBox(height: 8),
              Text(
                'Cette catégorie ne contient pas encore de produits',
                style: TextStyle(fontSize: 14, color: Colors.grey[500]),
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 24),
              ElevatedButton.icon(
                onPressed: () {
                  Navigator.pop(context);
                },
                icon: const Icon(Icons.arrow_back),
                label: const Text('Retour aux catégories'),
                style: ElevatedButton.styleFrom(
                  backgroundColor: AppTheme.color.primaryColor,
                  foregroundColor: Colors.white,
                  padding: const EdgeInsets.symmetric(
                    horizontal: 16,
                    vertical: 12,
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildSortingOptions() {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      decoration: BoxDecoration(
        color: Colors.white,
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 5,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Row(
        children: [
          const Text(
            'Trier par:',
            style: TextStyle(fontSize: 14, fontWeight: FontWeight.w500),
          ),
          const SizedBox(width: 8),
          DropdownButton<String>(
            value: _sortBy,
            underline: const SizedBox(),
            icon: const Icon(Icons.arrow_drop_down),
            items: [
              DropdownMenuItem(
                value: 'popularity',
                child: Text(
                  'Popularité',
                  style: TextStyle(
                    color: AppTheme.color.textColor,
                    fontSize: 14,
                  ),
                ),
              ),
              DropdownMenuItem(
                value: 'priceAsc',
                child: Text(
                  'Prix croissant',
                  style: TextStyle(
                    color: AppTheme.color.textColor,
                    fontSize: 14,
                  ),
                ),
              ),
              DropdownMenuItem(
                value: 'priceDesc',
                child: Text(
                  'Prix décroissant',
                  style: TextStyle(
                    color: AppTheme.color.textColor,
                    fontSize: 14,
                  ),
                ),
              ),
            ],
            onChanged: (value) {
              if (value != null) {
                setState(() {
                  _sortBy = value;
                  _sortProducts();
                });
              }
            },
          ),
          const Spacer(),
          TextButton.icon(
            onPressed: () {
              _showFilterBottomSheet();
            },
            icon: const Icon(Icons.filter_list, size: 18),
            label: const Text('Filtrer'),
            style: TextButton.styleFrom(
              foregroundColor: AppTheme.color.primaryColor,
              padding: const EdgeInsets.symmetric(horizontal: 8),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildProductCard(Map<String, dynamic> product) {
    // Assurons-nous que toutes les valeurs numériques sont du bon type
    final int price =
        product['price'] is int
            ? product['price']
            : (product['price'] as double).toInt();
    final int cashPrice =
        product['cashPrice'] is int
            ? product['cashPrice']
            : (product['cashPrice'] as double).toInt();
    final int dailyPrice =
        product['dailyPrice'] is int
            ? product['dailyPrice']
            : (product['dailyPrice'] as double).toInt();
    final double rating =
        product['rating'] is double
            ? product['rating']
            : (product['rating'] as int).toDouble();

    // Vérifier si l'URL de l'image est une URL complète ou un chemin relatif
    String imageUrl = product['imageUrl'];
    print('URL de l\'image avant traitement: $imageUrl');
    final bool isNetworkImage =
        imageUrl.startsWith('http://') ||
        imageUrl.startsWith('https://') ||
        imageUrl.contains('public/img') ||
        imageUrl.startsWith('/');

    // Construire l'URL correcte pour les images
    String finalImageUrl = imageUrl;
    if (isNetworkImage && !imageUrl.startsWith('http')) {
      // Si c'est une image réseau mais pas une URL complète

      imageUrl = imageUrl.replaceAll('../../', '');

      // Supprime un "/public" en trop s'il y en a deux à la suite
      if (imageUrl.contains('/public/public')) {
        imageUrl = imageUrl.replaceFirst('/public', '');
      }

      finalImageUrl = 'https://app.callitris-distribution.com/app/$imageUrl';
    }
    print('URL de l\'image: $finalImageUrl');

    return GestureDetector(
      onTap: () {
        Navigator.push(
          context,
          MaterialPageRoute(
            builder:
                (context) => ProductDetailScreen(
                  product: {
                    ...product,
                    'price': price,
                    'cashPrice': cashPrice,
                    'dailyPrice': dailyPrice,
                    'rating': rating,
                    'imageUrl': finalImageUrl, // Utiliser l'URL corrigée
                  },
                ),
          ),
        );
      },
      child: Container(
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(16),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withValues(alpha: 0.05),
              blurRadius: 10,
              offset: const Offset(0, 4),
            ),
          ],
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Image du produit
            Stack(
              children: [
                ClipRRect(
                  borderRadius: const BorderRadius.vertical(
                    top: Radius.circular(16),
                  ),
                  child:
                      isNetworkImage
                          ? Image.network(
                            finalImageUrl,
                            width: double.infinity,
                            height: 110,
                            fit: BoxFit.cover,
                            errorBuilder: (context, error, stackTrace) {
                              return Container(
                                height: 110,
                                color: Colors.grey[200],
                                child: Center(
                                  child: Icon(
                                    Icons.image_not_supported,
                                    color: Colors.grey[400],
                                  ),
                                ),
                              );
                            },
                          )
                          : Image.asset(
                            imageUrl,
                            width: double.infinity,
                            height: 110,
                            fit: BoxFit.cover,
                            errorBuilder: (context, error, stackTrace) {
                              return Container(
                                height: 110,
                                color: Colors.grey[200],
                                child: Center(
                                  child: Icon(
                                    Icons.image_not_supported,
                                    color: Colors.grey[400],
                                  ),
                                ),
                              );
                            },
                          ),
                ),
                // Badge de réduction
                Positioned(
                  top: 8,
                  right: 8,
                  child: Container(
                    padding: const EdgeInsets.symmetric(
                      horizontal: 8,
                      vertical: 4,
                    ),
                    decoration: BoxDecoration(
                      color: AppTheme.color.orangeColor,
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: const Text(
                      '-15%',
                      style: TextStyle(
                        color: Colors.white,
                        fontSize: 10,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                ),
              ],
            ),

            Expanded(
              child: Padding(
                padding: const EdgeInsets.all(8),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Text(
                      product['name'],
                      style: const TextStyle(
                        fontWeight: FontWeight.bold,
                        fontSize: 12,
                      ),
                      maxLines: 2,
                      overflow: TextOverflow.ellipsis,
                    ),
                    const SizedBox(height: 4),
                    Text(
                      '${_formatPrice(price)} FCFA',
                      style: TextStyle(
                        fontSize: 12,
                        fontWeight: FontWeight.bold,
                        color: AppTheme.color.primaryColor,
                      ),
                    ),
                    const SizedBox(height: 1),
                    Text(
                      '${_formatPrice(dailyPrice)} FCFA/jour',
                      style: TextStyle(
                        fontSize: 10,
                        color: AppTheme.color.brunGris,
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  String _formatPrice(int price) {
    return price.toString().replaceAllMapped(
      RegExp(r'(\d{1,3})(?=(\d{3})+(?!\d))'),
      (Match m) => '${m[1]} ',
    );
  }

  void _showFilterBottomSheet() {
    showModalBottomSheet(
      context: context,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
      ),
      builder: (context) {
        return StatefulBuilder(
          builder: (context, setState) {
            return Padding(
              padding: const EdgeInsets.all(20),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                mainAxisSize: MainAxisSize.min,
                children: [
                  const Text(
                    'Filtrer les produits',
                    style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                  ),
                  const SizedBox(height: 20),
                  const Text(
                    'Options de paiement',
                    style: TextStyle(fontSize: 16, fontWeight: FontWeight.w500),
                  ),
                  const SizedBox(height: 12),

                  CheckboxListTile(
                    title: const Text('Paiement cash (réduction 15%)'),
                    value: _showCashOnly,
                    onChanged: (value) {
                      setState(() {
                        _showCashOnly = value ?? false;
                        if (_showCashOnly) {
                          _showInstallmentOnly = false;
                        }
                      });
                    },
                    activeColor: AppTheme.color.primaryColor,
                    contentPadding: EdgeInsets.zero,
                    controlAffinity: ListTileControlAffinity.leading,
                    dense: true,
                  ),
                  CheckboxListTile(
                    title: const Text('Paiement échelonné'),
                    value: _showInstallmentOnly,
                    onChanged: (value) {
                      setState(() {
                        _showInstallmentOnly = value ?? false;
                        if (_showInstallmentOnly) {
                          _showCashOnly = false;
                        }
                      });
                    },
                    activeColor: AppTheme.color.primaryColor,
                    contentPadding: EdgeInsets.zero,
                    controlAffinity: ListTileControlAffinity.leading,
                    dense: true,
                  ),
                  const SizedBox(height: 20),
                  Row(
                    children: [
                      Expanded(
                        child: OutlinedButton(
                          onPressed: () {
                            setState(() {
                              _showCashOnly = false;
                              _showInstallmentOnly = false;
                            });
                          },
                          style: OutlinedButton.styleFrom(
                            padding: const EdgeInsets.symmetric(vertical: 12),
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(8),
                            ),
                          ),
                          child: const Text('Réinitialiser'),
                        ),
                      ),
                      const SizedBox(width: 16),
                      Expanded(
                        child: ElevatedButton(
                          onPressed: () {
                            Navigator.pop(context);
                            // Appliquer les filtres
                            this.setState(() {
                              _sortProducts();
                            });
                          },
                          style: ElevatedButton.styleFrom(
                            backgroundColor: AppTheme.color.primaryColor,
                            foregroundColor: Colors.white,
                            padding: const EdgeInsets.symmetric(vertical: 12),
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(8),
                            ),
                          ),
                          child: const Text('Appliquer'),
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            );
          },
        );
      },
    );
  }
}
