<?php
/**
 * Gestionnaire de callbacks Wave pour Callitris
 * 
 * Ce fichier doit être placé sur votre serveur backend à l'adresse :
 * https://dev-mani.io/teams/client-api.callitris-distribution.com/callback/wave/
 * 
 * Fonctionnalités :
 * - Reçoit les callbacks de Wave après paiement
 * - Valide les données de paiement
 * - Met à jour le statut de la commande
 * - Redirige l'utilisateur vers l'application mobile via deep link
 */

header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization');

// Gestion des requêtes OPTIONS (CORS preflight)
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit();
}

// Configuration
define('APP_SCHEME', 'callitris');
define('LOG_FILE', __DIR__ . '/logs/wave_callbacks.log');

// Fonction de logging
function logMessage($message) {
    $timestamp = date('Y-m-d H:i:s');
    $logEntry = "[$timestamp] $message" . PHP_EOL;
    
    // Créer le dossier logs s'il n'existe pas
    $logDir = dirname(LOG_FILE);
    if (!is_dir($logDir)) {
        mkdir($logDir, 0755, true);
    }
    
    file_put_contents(LOG_FILE, $logEntry, FILE_APPEND | LOCK_EX);
}

// Fonction pour valider les données Wave
function validateWaveData($data) {
    $requiredFields = ['transaction_id', 'status', 'amount'];
    
    foreach ($requiredFields as $field) {
        if (!isset($data[$field]) || empty($data[$field])) {
            return false;
        }
    }
    
    return true;
}

// Fonction pour mettre à jour le statut de la commande
function updateOrderStatus($transactionId, $status, $amount, $orderId = null) {
    try {
        // Ici, vous devez implémenter la logique pour mettre à jour votre base de données
        // Exemple de connexion à votre base de données
        
        /*
        $pdo = new PDO('mysql:host=localhost;dbname=callitris', $username, $password);
        
        $sql = "UPDATE commandes SET 
                payment_status = :status, 
                payment_transaction_id = :transaction_id,
                payment_amount = :amount,
                payment_date = NOW()
                WHERE id = :order_id OR payment_reference = :transaction_id";
        
        $stmt = $pdo->prepare($sql);
        $stmt->execute([
            ':status' => $status,
            ':transaction_id' => $transactionId,
            ':amount' => $amount,
            ':order_id' => $orderId
        ]);
        */
        
        logMessage("Statut de commande mis à jour - Transaction: $transactionId, Statut: $status, Montant: $amount");
        return true;
        
    } catch (Exception $e) {
        logMessage("Erreur lors de la mise à jour de la commande: " . $e->getMessage());
        return false;
    }
}

// Fonction pour générer un deep link vers l'application
function generateDeepLink($type, $params = []) {
    $baseUrl = APP_SCHEME . '://payment/' . $type;
    
    if (!empty($params)) {
        $queryString = http_build_query($params);
        $baseUrl .= '?' . $queryString;
    }
    
    return $baseUrl;
}

// Fonction pour rediriger vers l'application mobile
function redirectToApp($deepLink) {
    // Pour Android et iOS, nous utilisons une page HTML avec JavaScript
    // pour tenter d'ouvrir l'app, sinon rediriger vers le store
    
    $html = '<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Redirection vers Callitris</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            text-align: center;
            padding: 50px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 400px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .logo {
            width: 80px;
            height: 80px;
            margin: 0 auto 20px;
            background: #007bff;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 24px;
            font-weight: bold;
        }
        .message {
            margin: 20px 0;
            color: #333;
        }
        .btn {
            display: inline-block;
            padding: 12px 24px;
            background: #007bff;
            color: white;
            text-decoration: none;
            border-radius: 5px;
            margin: 10px;
        }
        .btn:hover {
            background: #0056b3;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="logo">C</div>
        <h2>Retour vers Callitris</h2>
        <div class="message">
            <p>Votre paiement a été traité. Vous allez être redirigé vers l\'application Callitris.</p>
            <p>Si la redirection ne fonctionne pas automatiquement, cliquez sur le bouton ci-dessous.</p>
        </div>
        <a href="' . htmlspecialchars($deepLink) . '" class="btn">Ouvrir Callitris</a>
    </div>
    
    <script>
        // Tentative de redirection automatique
        setTimeout(function() {
            window.location.href = "' . htmlspecialchars($deepLink) . '";
        }, 1000);
        
        // Fallback pour iOS
        var userAgent = navigator.userAgent || navigator.vendor || window.opera;
        if (/iPad|iPhone|iPod/.test(userAgent) && !window.MSStream) {
            setTimeout(function() {
                window.location.href = "https://apps.apple.com/app/callitris/id123456789"; // Remplacez par votre App Store URL
            }, 3000);
        }
        
        // Fallback pour Android
        if (/android/i.test(userAgent)) {
            setTimeout(function() {
                window.location.href = "https://play.google.com/store/apps/details?id=com.callitris.app"; // Remplacez par votre Play Store URL
            }, 3000);
        }
    </script>
</body>
</html>';
    
    echo $html;
    exit();
}

// Traitement principal
try {
    logMessage("Callback Wave reçu - Method: " . $_SERVER['REQUEST_METHOD'] . ", URI: " . $_SERVER['REQUEST_URI']);
    
    // Récupérer les données selon la méthode HTTP
    $data = [];
    
    if ($_SERVER['REQUEST_METHOD'] === 'POST') {
        $rawInput = file_get_contents('php://input');
        logMessage("POST Data: " . $rawInput);
        
        // Essayer de décoder JSON
        $jsonData = json_decode($rawInput, true);
        if ($jsonData) {
            $data = $jsonData;
        } else {
            // Sinon, utiliser les données POST normales
            $data = $_POST;
        }
    } else {
        // Données GET
        $data = $_GET;
    }
    
    logMessage("Données reçues: " . json_encode($data));
    
    // Déterminer le type de callback basé sur l'URL
    $requestUri = $_SERVER['REQUEST_URI'];
    $pathInfo = parse_url($requestUri, PHP_URL_PATH);
    $pathSegments = explode('/', trim($pathInfo, '/'));
    
    // Extraire le type de callback (success, failure, cancel, notify)
    $callbackType = end($pathSegments);
    
    logMessage("Type de callback détecté: $callbackType");
    
    // Traiter selon le type de callback
    switch ($callbackType) {
        case 'success':
            handleSuccessCallback($data);
            break;
            
        case 'failure':
            handleFailureCallback($data);
            break;
            
        case 'cancel':
            handleCancelCallback($data);
            break;
            
        case 'notify':
            handleNotifyCallback($data);
            break;
            
        default:
            logMessage("Type de callback non reconnu: $callbackType");
            http_response_code(400);
            echo json_encode(['error' => 'Type de callback non reconnu']);
            exit();
    }
    
} catch (Exception $e) {
    logMessage("Erreur dans le traitement du callback: " . $e->getMessage());
    http_response_code(500);
    echo json_encode(['error' => 'Erreur interne du serveur']);
}

// Fonction pour gérer le callback de succès
function handleSuccessCallback($data) {
    logMessage("Traitement du callback de succès");
    
    if (!validateWaveData($data)) {
        logMessage("Données Wave invalides pour le succès");
        http_response_code(400);
        echo json_encode(['error' => 'Données invalides']);
        return;
    }
    
    $transactionId = $data['transaction_id'];
    $amount = $data['amount'];
    $orderId = $data['order_id'] ?? null;
    
    // Mettre à jour le statut de la commande
    if (updateOrderStatus($transactionId, 'success', $amount, $orderId)) {
        logMessage("Commande mise à jour avec succès - Transaction: $transactionId");
        
        // Générer le deep link de succès
        $deepLink = generateDeepLink('success', [
            'transaction_id' => $transactionId,
            'amount' => $amount,
            'order_id' => $orderId,
            'status' => 'success'
        ]);
        
        logMessage("Redirection vers: $deepLink");
        redirectToApp($deepLink);
    } else {
        logMessage("Échec de la mise à jour de la commande");
        http_response_code(500);
        echo json_encode(['error' => 'Erreur lors de la mise à jour']);
    }
}

// Fonction pour gérer le callback d'échec
function handleFailureCallback($data) {
    logMessage("Traitement du callback d'échec");
    
    $transactionId = $data['transaction_id'] ?? 'unknown';
    $errorMessage = $data['error_message'] ?? $data['message'] ?? 'Erreur inconnue';
    $orderId = $data['order_id'] ?? null;
    
    // Mettre à jour le statut de la commande
    updateOrderStatus($transactionId, 'failed', 0, $orderId);
    
    // Générer le deep link d'échec
    $deepLink = generateDeepLink('failure', [
        'transaction_id' => $transactionId,
        'error_message' => $errorMessage,
        'order_id' => $orderId,
        'status' => 'failed'
    ]);
    
    logMessage("Redirection vers: $deepLink");
    redirectToApp($deepLink);
}

// Fonction pour gérer le callback d'annulation
function handleCancelCallback($data) {
    logMessage("Traitement du callback d'annulation");
    
    $transactionId = $data['transaction_id'] ?? 'unknown';
    $orderId = $data['order_id'] ?? null;
    
    // Mettre à jour le statut de la commande
    updateOrderStatus($transactionId, 'cancelled', 0, $orderId);
    
    // Générer le deep link d'annulation
    $deepLink = generateDeepLink('cancel', [
        'transaction_id' => $transactionId,
        'order_id' => $orderId,
        'status' => 'cancelled'
    ]);
    
    logMessage("Redirection vers: $deepLink");
    redirectToApp($deepLink);
}

// Fonction pour gérer les notifications (webhook)
function handleNotifyCallback($data) {
    logMessage("Traitement de la notification Wave");
    
    if (!validateWaveData($data)) {
        logMessage("Données Wave invalides pour la notification");
        http_response_code(400);
        echo json_encode(['error' => 'Données invalides']);
        return;
    }
    
    $transactionId = $data['transaction_id'];
    $status = $data['status'];
    $amount = $data['amount'];
    $orderId = $data['order_id'] ?? null;
    
    // Mettre à jour le statut de la commande
    if (updateOrderStatus($transactionId, $status, $amount, $orderId)) {
        logMessage("Notification traitée avec succès - Transaction: $transactionId, Statut: $status");
        http_response_code(200);
        echo json_encode(['status' => 'success', 'message' => 'Notification traitée']);
    } else {
        logMessage("Échec du traitement de la notification");
        http_response_code(500);
        echo json_encode(['error' => 'Erreur lors du traitement']);
    }
}
?>
