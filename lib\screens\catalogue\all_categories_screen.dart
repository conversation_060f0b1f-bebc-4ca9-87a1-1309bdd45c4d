import 'package:flutter/material.dart';
import 'package:callitris/utils/appTheme.dart';
import 'package:callitris/services/catalogue_service.dart';
import 'package:callitris/screens/catalogue/category_products_screen.dart';

class AllCategoriesScreen extends StatefulWidget {
  const AllCategoriesScreen({super.key});

  @override
  State<AllCategoriesScreen> createState() => _AllCategoriesScreenState();
}

class _AllCategoriesScreenState extends State<AllCategoriesScreen>
    with SingleTickerProviderStateMixin {
  List<Map<String, dynamic>> _categories = [];
  bool _isLoading = true;
  String _searchQuery = '';
  final TextEditingController _searchController = TextEditingController();
  late AnimationController _animationController;
  late Animation<double> _fadeAnimation;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 800),
    );
    _fadeAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _animationController, curve: Curves.easeOut),
    );
    _loadAllCategories();
    _animationController.forward();
  }

  @override
  void dispose() {
    _searchController.dispose();
    _animationController.dispose();
    super.dispose();
  }

  Future<void> _loadAllCategories() async {
    try {
      setState(() {
        _isLoading = true;
      });

      // Récupérer toutes les catégories (sans limite)
      final categories = await CatalogueService.getCategories(100);

      setState(() {
        _categories = categories;
        _isLoading = false;
      });
    } catch (e) {
      print('Erreur lors du chargement des catégories: $e');
      setState(() {
        _isLoading = false;
      });
    }
  }

  List<Map<String, dynamic>> get _filteredCategories {
    if (_searchQuery.isEmpty) {
      return _categories;
    }
    return _categories.where((category) {
      final name = category['nom_cat']?.toString().toLowerCase() ?? '';
      return name.contains(_searchQuery.toLowerCase());
    }).toList();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.white,
      body: FadeTransition(
        opacity: _fadeAnimation,
        child: CustomScrollView(
          slivers: [
            // AppBar moderne avec effet de parallaxe
            SliverAppBar(
              expandedHeight: 100,
              floating: false,
              pinned: true,
              backgroundColor: Colors.white,
              elevation: 0,
              leading: IconButton(
                icon: Icon(
                  Icons.arrow_back_ios_new_rounded,
                  color: AppTheme.color.primaryColor,
                  size: 20,
                ),
                onPressed: () => Navigator.pop(context),
              ),
              flexibleSpace: FlexibleSpaceBar(
                titlePadding: const EdgeInsets.only(left: 20, bottom: 10),
                title: Text(
                  'Toutes les Catégories',
                  style: TextStyle(
                    color: AppTheme.color.primaryColor,
                    fontSize: 20,
                    fontWeight: FontWeight.w600,
                  ),
                ),
                background: Container(
                  decoration: BoxDecoration(
                    gradient: LinearGradient(
                      begin: Alignment.topLeft,
                      end: Alignment.bottomRight,
                      colors: [
                        Colors.white,
                        AppTheme.color.primaryColor.withValues(alpha: 0.05),
                      ],
                    ),
                  ),
                ),
              ),
            ),

            // Barre de recherche
            SliverToBoxAdapter(
              child: Container(
                color: Colors.white,
                padding: const EdgeInsets.fromLTRB(20, 0, 20, 20),
                child: Container(
                  decoration: BoxDecoration(
                    color: Colors.grey.shade50,
                    borderRadius: BorderRadius.circular(16),
                    border: Border.all(color: Colors.grey.shade200, width: 1),
                  ),
                  child: TextField(
                    controller: _searchController,
                    onChanged: (value) {
                      setState(() {
                        _searchQuery = value;
                      });
                    },
                    decoration: InputDecoration(
                      hintText: 'Rechercher une catégorie...',
                      hintStyle: TextStyle(
                        color: Colors.grey.shade500,
                        fontSize: 16,
                      ),
                      prefixIcon: Padding(
                        padding: const EdgeInsets.all(12),
                        child: Icon(
                          Icons.search_rounded,
                          color: AppTheme.color.primaryColor,
                          size: 24,
                        ),
                      ),
                      suffixIcon:
                          _searchQuery.isNotEmpty
                              ? IconButton(
                                icon: Icon(
                                  Icons.clear_rounded,
                                  color: Colors.grey.shade500,
                                  size: 20,
                                ),
                                onPressed: () {
                                  _searchController.clear();
                                  setState(() {
                                    _searchQuery = '';
                                  });
                                },
                              )
                              : null,
                      border: InputBorder.none,
                      contentPadding: const EdgeInsets.symmetric(
                        horizontal: 16,
                        vertical: 16,
                      ),
                    ),
                  ),
                ),
              ),
            ),

            // Contenu principal
            _isLoading
                ? SliverToBoxAdapter(child: _buildLoadingState())
                : _filteredCategories.isEmpty
                ? SliverToBoxAdapter(child: _buildEmptyState())
                : _buildCategoriesGrid(),
          ],
        ),
      ),
    );
  }

  Widget _buildLoadingState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          CircularProgressIndicator(
            valueColor: AlwaysStoppedAnimation<Color>(
              AppTheme.color.primaryColor,
            ),
          ),
          const SizedBox(height: 16),
          Text(
            'Chargement des catégories...',
            style: TextStyle(color: Colors.grey.shade600, fontSize: 16),
          ),
        ],
      ),
    );
  }

  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            _searchQuery.isNotEmpty
                ? Icons.search_off
                : Icons.category_outlined,
            size: 64,
            color: Colors.grey.shade400,
          ),
          const SizedBox(height: 16),
          Text(
            _searchQuery.isNotEmpty
                ? 'Aucune catégorie trouvée'
                : 'Aucune catégorie disponible',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.w500,
              color: Colors.grey.shade600,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            _searchQuery.isNotEmpty
                ? 'Essayez avec d\'autres mots-clés'
                : 'Les catégories seront bientôt disponibles',
            style: TextStyle(fontSize: 14, color: Colors.grey.shade500),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildCategoriesGrid() {
    return SliverPadding(
      padding: const EdgeInsets.all(20),
      sliver: SliverGrid(
        gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
          crossAxisCount: 2,
          childAspectRatio: 0.85,
          crossAxisSpacing: 16,
          mainAxisSpacing: 16,
        ),
        delegate: SliverChildBuilderDelegate((context, index) {
          final category = _filteredCategories[index];
          return _buildModernCategoryCard(category, index);
        }, childCount: _filteredCategories.length),
      ),
    );
  }

  Widget _buildModernCategoryCard(Map<String, dynamic> category, int index) {
    final String categoryName = category['nom_cat'] ?? 'Catégorie sans nom';
    int productCount = 0;

    // Gradients colorés modernes (même style que catalogue_screen)
    final List<List<Color>> gradients = [
      [const Color(0xFF667eea), const Color(0xFF764ba2)], // Violet-Bleu
      [const Color(0xFFf093fb), const Color(0xFFf5576c)], // Rose-Rouge
      [
        const Color.fromARGB(255, 50, 138, 215),
        const Color.fromARGB(255, 63, 192, 198),
      ], // Bleu-Cyan
      [
        const Color.fromARGB(255, 39, 176, 85),
        const Color(0xFF38f9d7),
      ], // Vert-Turquoise
      [const Color(0xFFfa709a), const Color(0xFFfee140)],
    ];

    final gradient = gradients[index % gradients.length];

    // Déterminer l'icône en fonction du nom de la catégorie
    IconData icon = Icons.category_outlined;
    final categoryNameLower = categoryName.toLowerCase();

    if (categoryNameLower.contains('electronique') ||
        categoryNameLower.contains('telephone')) {
      icon = Icons.phone_android;
    } else if (categoryNameLower.contains('electroménager') ||
        categoryNameLower.contains('ustensile')) {
      icon = Icons.kitchen;
    } else if (categoryNameLower.contains('textile') ||
        categoryNameLower.contains('couture')) {
      icon = Icons.shopping_bag;
    } else if (categoryNameLower.contains('mobilier') ||
        categoryNameLower.contains('chantier')) {
      icon = Icons.chair;
    } else if (categoryNameLower.contains('coiffure') ||
        categoryNameLower.contains('acessoire')) {
      icon = Icons.spa;
    } else if (categoryNameLower.contains('jouet') ||
        categoryNameLower.contains('scolaire')) {
      icon = Icons.sports_soccer;
    } else if (categoryNameLower.contains('moto')) {
      icon = Icons.motorcycle;
    } else if (categoryNameLower.contains('vivres') ||
        categoryNameLower.contains('alimentaire')) {
      icon = Icons.restaurant;
    }

    return GestureDetector(
      onTap: () async {
        // Afficher un indicateur de chargement
        showDialog(
          context: context,
          barrierDismissible: false,
          builder:
              (context) => Center(
                child: Container(
                  padding: const EdgeInsets.all(20),
                  decoration: BoxDecoration(
                    color: Colors.white,
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Column(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      CircularProgressIndicator(
                        valueColor: AlwaysStoppedAnimation<Color>(gradient[0]),
                      ),
                      const SizedBox(height: 16),
                      Text(
                        'Chargement des produits...',
                        style: TextStyle(
                          color: AppTheme.color.textColor,
                          fontSize: 14,
                        ),
                      ),
                    ],
                  ),
                ),
              ),
        );

        try {
          final products = await CatalogueService.getProductsByCategoryNew(
            category['id_categorie']?.toString() ?? '',
          );
          // Fermer l'indicateur de chargement
          if (mounted) Navigator.pop(context);

          if (mounted) {
            Navigator.push(
              context,
              MaterialPageRoute(
                builder:
                    (context) => CategoryProductsScreen(
                      categoryId: category['id_categorie']?.toString() ?? '',
                      categoryName: categoryName,
                      initialProducts:
                          products
                              .map(
                                (product) => {
                                  'id': product['id_kit'].toString(),
                                  'name':
                                      product['nom_produit'] ??
                                      product['nom_kit'] ??
                                      'Produit sans nom',
                                  'price':
                                      int.tryParse(
                                        product['montant_total_kit']
                                                ?.toString() ??
                                            product['prix_kit']?.toString() ??
                                            '0',
                                      ) ??
                                      0,
                                  'cashPrice':
                                      (int.tryParse(
                                            product['montant_total_kit']
                                                    ?.toString() ??
                                                product['prix_kit']
                                                    ?.toString() ??
                                                '0',
                                          ) ??
                                          0) *
                                      0.85,
                                  'dailyPrice':
                                      int.tryParse(
                                        product['cout_journalier_kit']
                                                ?.toString() ??
                                            product['journalier_kit']
                                                ?.toString() ??
                                            '0',
                                      ) ??
                                      0,
                                  'imageUrl': formatImageUrl(
                                    product['photo_kit'] ??
                                        'assets/images/product_default.jpg',
                                  ),
                                  'rating': 4.5,
                                  'inStock': true,
                                  'description':
                                      product['description_kit'] ??
                                      'Aucune description disponible',
                                  'categoryId':
                                      category['id_categorie'].toString(),
                                  'popularity': 80,
                                  'livraison':
                                      product['livraison'] ??
                                      'indisponible',
                                  'delai_livraison':
                                      product['delai_livraison'] ?? 'pas de délai',
                                  'garantie': product['garantie'] ?? 'aucune',
                                },
                              )
                              .toList(),
                    ),
              ),
            );
          }
        } catch (e) {
          // Fermer l'indicateur de chargement en cas d'erreur
          if (mounted) Navigator.pop(context);

          // Afficher un message d'erreur
          if (mounted) {
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Text('Erreur lors du chargement des produits'),
                backgroundColor: Colors.red,
              ),
            );
          }
        }
      },
      child: Container(
        decoration: BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
            colors: gradient,
          ),
          borderRadius: BorderRadius.circular(20),
          boxShadow: [
            BoxShadow(
              color: gradient[0].withValues(alpha: 0.3),
              blurRadius: 15,
              offset: const Offset(0, 8),
            ),
          ],
        ),
        child: Stack(
          children: [
            // Éléments décoratifs en arrière-plan (même style que catalogue_screen)
            Positioned(
              top: -20,
              right: -20,
              child: Container(
                width: 80,
                height: 80,
                decoration: BoxDecoration(
                  color: Colors.white.withValues(alpha: 0.1),
                  shape: BoxShape.circle,
                ),
              ),
            ),
            Positioned(
              bottom: -10,
              left: -10,
              child: Container(
                width: 40,
                height: 40,
                decoration: BoxDecoration(
                  color: Colors.white.withValues(alpha: 0.1),
                  shape: BoxShape.circle,
                ),
              ),
            ),

            // Badge de nombre de produits en haut à droite
            if (productCount > 0)
              Positioned(
                top: 12,
                right: 12,
                child: Container(
                  padding: const EdgeInsets.symmetric(
                    horizontal: 8,
                    vertical: 4,
                  ),
                  decoration: BoxDecoration(
                    color: Colors.white.withValues(alpha: 0.2),
                    borderRadius: BorderRadius.circular(12),
                    border: Border.all(
                      color: Colors.white.withValues(alpha: 0.3),
                      width: 1,
                    ),
                  ),
                  child: Text(
                    '$productCount',
                    style: const TextStyle(
                      color: Colors.white,
                      fontSize: 12,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
              ),

            // Contenu principal
            Padding(
              padding: const EdgeInsets.all(20),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Icône principale avec style moderne
                  Container(
                    width: 50,
                    height: 50,
                    decoration: BoxDecoration(
                      color: Colors.white.withValues(alpha: 0.2),
                      borderRadius: BorderRadius.circular(15),
                      border: Border.all(
                        color: Colors.white.withValues(alpha: 0.3),
                        width: 1,
                      ),
                    ),
                    child: Icon(icon, color: Colors.white, size: 28),
                  ),

                  const Spacer(),

                  // Nom de la catégorie
                  Text(
                    categoryName,
                    style: const TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                      color: Colors.white,
                      height: 1.2,
                    ),
                    maxLines: 2,
                    overflow: TextOverflow.ellipsis,
                  ),

                  const SizedBox(height: 4),

                  // Sous-titre avec nombre de produits
                  Text(
                    'Découvrir les articles',
                    style: TextStyle(
                      fontSize: 12,
                      color: Colors.white.withValues(alpha: 0.8),
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  static String formatImageUrl(String imageUrl) {
    if (imageUrl.isEmpty) return '';

    // Si c'est déjà une URL complète
    if (imageUrl.startsWith('http://') || imageUrl.startsWith('https://')) {
      return imageUrl;
    }

    String cleanPath = imageUrl;

    // Enlever préfixes inutiles
    if (cleanPath.startsWith('app/')) {
      cleanPath = cleanPath.substring(4);
    }
    if (cleanPath.startsWith('public/')) {
      cleanPath = cleanPath.substring(7);
    }
    if (cleanPath.startsWith('../../')) {
      cleanPath = cleanPath.substring(6);
    }

    // Éviter les doublons de /public
    cleanPath = cleanPath.replaceAll(RegExp(r'(\/public)+\/'), '/');

    // Déterminer si c'est une image de kit
    if (cleanPath.contains('img/kit/')) {
      return 'https://app.callitris-distribution.com/app/$cleanPath';
    } else {
      return 'https://app.callitris-distribution.com/app/$cleanPath';
    }
  }
}
