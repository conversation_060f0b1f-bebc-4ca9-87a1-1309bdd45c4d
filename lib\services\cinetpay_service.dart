import 'dart:convert';
import 'package:flutter/material.dart';
import 'package:callitris/config/cinetpay_config.dart';
import 'package:callitris/config/api_config.dart';
import 'package:callitris/services/auth_service.dart';
import 'package:callitris/models/cinetpay_transaction.dart';
import 'package:callitris/screens/payment/cinetpay_page.dart';
import 'package:http/http.dart' as http;

/// Service principal pour gérer les transactions CinetPay
class CinetPayService {
  static const String _tag = 'CinetPayService';

  /// Initialise un paiement CinetPay
  static Future<Map<String, dynamic>> initializePayment({
    required BuildContext context,
    required double amount,
    required String description,
    required String customerName,
    required String customerEmail,
    String? customerPhone,
    String currency = CinetPayConfig.defaultCurrency,
    Map<String, dynamic>? metadata,
  }) async {
    try {
      // Validation des paramètres
      if (!CinetPayConfig.isAmountValid(amount)) {
        return {
          'success': false,
          'message':
              'Montant invalide. Minimum: ${CinetPayConfig.minimumAmount / 100} $currency',
        };
      }

      if (!CinetPayConfig.isCurrencySupported(currency)) {
        return {'success': false, 'message': 'Devise non supportée: $currency'};
      }

      // Récupérer les données utilisateur
      final userData = await AuthService.getUserData();
      if (userData == null) {
        return {'success': false, 'message': 'Utilisateur non connecté'};
      }

      // Générer un ID de transaction unique
      final transactionId = CinetPayConfig.generateTransactionId();

      // Créer l'objet de transaction
      final transaction = CinetPayTransaction(
        id: transactionId,
        amount: amount,
        currency: currency,
        description: description,
        customerName: customerName,
        customerEmail: customerEmail,
        customerPhone: customerPhone,
        status: TransactionStatus.pending,
        createdAt: DateTime.now(),
        metadata: metadata ?? {},
      );
      // Configurer CinetPay
      final cinetPayConfig = {
        'apikey': CinetPayConfig.apiKey,
        'site_id': CinetPayConfig.siteId,
        'transaction_id': transactionId,
        'amount': CinetPayConfig.convertToCentimes(amount),
        'currency': currency,
        'description': description,
        'customer_name': customerName,
        'customer_surname': customerName,
        'customer_email': customerEmail,
        'customer_phone_number': customerPhone,
        'customer_address': '',
        'customer_city': '',
        'customer_country': 'CI',
        'customer_state': '',
        'customer_zip_code': '',
        'return_url': CinetPayConfig.returnUrl,
        'notify_url': CinetPayConfig.notifyUrl,
        'cancel_url': CinetPayConfig.cancelUrl,
        'channels': 'MOBILE_MONEY',
        'metadata': jsonEncode(metadata ?? {}),
      };

      print(
        '$_tag: Initialisation du paiement - ID: $transactionId, Montant: $amount $currency',
      );

      return {
        'success': true,
        'transaction': transaction,
        'config': cinetPayConfig,
        'message': 'Paiement initialisé avec succès',
      };
    } catch (e) {
      print('$_tag: Erreur lors de l\'initialisation du paiement: $e');
      return {
        'success': false,
        'message': 'Erreur lors de l\'initialisation du paiement: $e',
      };
    }
  }

  /// Lance le processus de paiement CinetPay via backend
  static Future<Map<String, dynamic>> processPayment({
    required BuildContext context,
    required double amount,
    required String customerPhone,
    required CinetPayTransaction transaction,
  }) async {
    try {
      print(
        '$_tag: Initialisation du paiement via backend pour ${transaction.id}',
      );

      // Vérifier le contexte avant l'appel async
      if (!context.mounted) {
        return {'success': false, 'message': 'Contexte non disponible'};
      }

      // Récupérer les informations utilisateur et commande
      final userData = await AuthService.getUserData();
      final token = await AuthService.getAuthToken();

      if (userData == null || token == null) {
        return {
          'success': false,
          'message': 'Informations utilisateur manquantes pour le paiement',
        };
      }

      final clientId = userData['id_client']?.toString();
      if (clientId == null) {
        return {'success': false, 'message': 'ID client manquant'};
      }

      // Extraire l'ID de commande des métadonnées de la transaction
      final commandeId = transaction.metadata['orderId']?.toString();
      // if (commandeId == null) {
      //   return {
      //     'success': false,
      //     'message': 'ID de commande manquant dans les métadonnées',
      //   };
      // }

      // Appeler l'API backend pour initialiser le paiement
      final initResult = await _WithBackend(
        amount: amount,
        customerPhone: customerPhone,
        commandeId: commandeId ?? '',
        clientId: clientId,
        token: token,
      );

      if (!initResult['success']) {
        return {
          'success': false,
          'message':
              initResult['message'] ??
              'Erreur lors de l\'initialisation du paiement',
        };
      }

      final paymentUrl = initResult['payment_url'];
      if (paymentUrl == null || paymentUrl.isEmpty) {
        return {
          'success': false,
          'message': 'URL de paiement non reçue du serveur',
        };
      }

      print('$_tag: URL de paiement reçue: $paymentUrl');

      // Vérifier le contexte avant la navigation
      if (!context.mounted) {
        return {
          'success': false,
          'message': 'Contexte non disponible pour la navigation',
        };
      }

      // Ouvrir la WebView avec l'URL de paiement
      final result = await Navigator.push<Map<String, dynamic>>(
        context,
        MaterialPageRoute(
          builder:
              (context) => CinetPayPage(
                paymentUrl: paymentUrl,
                transactionId: transaction.id,
                onPaymentResult: (result) {
                  // Le résultat sera géré par le Navigator.pop dans CinetPayPage
                },
              ),
        ),
      );

      if (result != null) {
        // Mettre à jour le statut de la transaction
        if (result['success']) {
          transaction.status = TransactionStatus.completed;
          transaction.operatorId = result['operator_id'];
          transaction.paymentMethod = result['payment_method'];
          transaction.completedAt = DateTime.now();

          print(
            '$_tag: ✅ Paiement réussi - Transaction ${transaction.id} complétée',
          );

          // Afficher le message de succès avec ScaffoldMessenger
          if (context.mounted) {
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Row(
                  children: [
                    const Icon(Icons.check_circle, color: Colors.white),
                    const SizedBox(width: 12),
                    Expanded(
                      child: Text(
                        result['message'] ??
                            '🎉 Paiement effectué avec succès !',
                        style: const TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                    ),
                  ],
                ),
                backgroundColor: const Color(0xFF4CAF50),
                duration: const Duration(seconds: 4),
                behavior: SnackBarBehavior.floating,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(10),
                ),
                margin: const EdgeInsets.all(16),
              ),
            );
          }
        } else {
          transaction.status = TransactionStatus.failed;
          transaction.failureReason = result['message'];

          print('$_tag: ❌ Paiement échoué - ${result['message']}');

          // Afficher le message d'erreur
          if (context.mounted) {
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Row(
                  children: [
                    const Icon(Icons.error_outline, color: Colors.white),
                    const SizedBox(width: 12),
                    Expanded(
                      child: Text(
                        result['message'] ?? 'Erreur lors du paiement',
                        style: const TextStyle(fontSize: 16),
                      ),
                    ),
                  ],
                ),
                backgroundColor: Colors.red,
                duration: const Duration(seconds: 4),
                behavior: SnackBarBehavior.floating,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(10),
                ),
                margin: const EdgeInsets.all(16),
              ),
            );
          }
        }

        return result;
      } else {
        // L'utilisateur a fermé la page de paiement
        transaction.status = TransactionStatus.cancelled;

        print('$_tag: Paiement annulé par l\'utilisateur');

        final cancelResult = {
          'success': false,
          'message': CinetPayConfig.errorMessages['PAYMENT_CANCELLED']!,
        };

        // Afficher le message d'annulation
        if (context.mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Row(
                children: [
                  const Icon(Icons.cancel_outlined, color: Colors.white),
                  const SizedBox(width: 12),
                  const Expanded(
                    child: Text(
                      'Paiement annulé',
                      style: TextStyle(fontSize: 16),
                    ),
                  ),
                ],
              ),
              backgroundColor: Colors.orange,
              duration: const Duration(seconds: 3),
              behavior: SnackBarBehavior.floating,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(10),
              ),
              margin: const EdgeInsets.all(16),
            ),
          );
        }

        return cancelResult;
      }
    } catch (e) {
      print('$_tag: Erreur lors du processus de paiement: $e');
      transaction.status = TransactionStatus.failed;
      transaction.failureReason = e.toString();

      return {'success': false, 'message': 'Erreur lors du paiement: $e'};
    }
  }

  /// Initialise le paiement via l'API backend
  static Future<Map<String, dynamic>> _WithBackend({
    required double amount,
    required String customerPhone,
    required String commandeId,
    required String clientId,
    required String token,
  }) async {
    try {
      print(
        '$_tag: Appel API backend - URL: ${ApiConfig.initCinetPayEndpoint}',
      );
      print(
        '$_tag: Paramètres - Montant: $amount (type: ${amount.runtimeType}), Téléphone: $customerPhone',
      );

      // Validation côté client avant envoi
      if (amount <= 0) {
        print('$_tag: ERREUR - Montant invalide: $amount');
        return {'success': false, 'message': 'Montant invalide: $amount'};
      }

      // Validation et formatage du numéro de téléphone
      if (customerPhone.isEmpty) {
        print('$_tag: ERREUR - Numéro de téléphone manquant');
        return {
          'success': false,
          'message': 'Numéro de téléphone requis pour le paiement',
        };
      }

      // Formater le numéro de téléphone pour la Côte d'Ivoire
      String formattedPhone = CinetPayConfig.formatPhoneNumber(customerPhone);
      if (!CinetPayConfig.isValidPhoneNumber(formattedPhone)) {
        print(
          '$_tag: ERREUR - Numéro de téléphone invalide: $customerPhone -> $formattedPhone',
        );
        return {
          'success': false,
          'message':
              'Numéro de téléphone invalide. Format attendu: +225XXXXXXXX',
        };
      }

      // Créer le requestBody avec tous les paramètres requis
      final requestBody = {
        'amount': amount,
        'phone': formattedPhone,
        'commande_id': commandeId == '' ? null : commandeId,
        'cle': token == '' ? null : token,
        'clientId': clientId,
      };

      print('$_tag: Corps de la requête: ${jsonEncode(requestBody)}');
      print(
        '$_tag: Validation - amount: ${requestBody['amount']} (type: ${requestBody['amount'].runtimeType})',
      );
      print(
        '$_tag: Validation - phone: ${requestBody['phone']} (formaté depuis: $customerPhone)',
      );
      print('$_tag: Validation - commande_id: ${requestBody['commande_id']}');
      print('$_tag: Validation - clientId: ${requestBody['clientId']}');
      print(
        '$_tag: Validation - cle (token): ${token.length > 10 ? '${token.substring(0, 10)}...' : token}',
      );

      final response = await http
          .post(
            Uri.parse(ApiConfig.initCinetPayEndpoint),
            headers: {
              'Content-Type': 'application/json',
              'Accept': 'application/json',
            },
            body: jsonEncode(requestBody),
          )
          .timeout(Duration(seconds: CinetPayConfig.apiTimeout));

      print(
        '$_tag: Réponse backend (${response.statusCode}): ${response.body}',
      );

      if (response.statusCode == 200) {
        try {
          // Nettoyer la réponse au cas où il y aurait des JSON concaténés
          String cleanBody = response.body;

          // Détecter et gérer les JSON concaténés
          if (cleanBody.contains('}{')) {
            print(
              '$_tag: JSON concaténés détectés, utilisation du dernier: $cleanBody',
            );
            final parts = cleanBody.split('}{');
            cleanBody = '{${parts.last}';
          }

          final data = jsonDecode(cleanBody);

          if (data['success'] == true && data['payment_url'] != null) {
            print(
              '$_tag: Initialisation réussie - URL: ${data['payment_url']}',
            );
            return {
              'success': true,
              'payment_url': data['payment_url'],
              'transaction_id': data['transaction_id'],
            };
          } else {
            print(
              '$_tag: Échec de l\'initialisation - Message: ${data['message']}',
            );
            return {
              'success': false,
              'message':
                  data['message'] ??
                  'Erreur lors de l\'initialisation du paiement',
            };
          }
        } catch (jsonError) {
          print('$_tag: Erreur de décodage JSON: $jsonError');
          return {
            'success': false,
            'message': 'Réponse serveur invalide: ${response.body}',
          };
        }
      } else {
        print('$_tag: Erreur HTTP ${response.statusCode}: ${response.body}');
        return {
          'success': false,
          'message': 'Erreur serveur: ${response.statusCode}',
        };
      }
    } catch (e) {
      print('$_tag: Erreur lors de l\'appel backend: $e');
      return {'success': false, 'message': 'Erreur de connexion: $e'};
    }
  }

  /// Vérifie le statut d'une transaction
  static Future<Map<String, dynamic>> checkTransactionStatus(
    String transactionId,
  ) async {
    try {
      print('$_tag: Vérification du statut de la transaction: $transactionId');

      final response = await http
          .post(
            Uri.parse('https://api-checkout.cinetpay.com/v2/payment/check'),
            headers: {'Content-Type': 'application/json'},
            body: jsonEncode({
              'apikey': CinetPayConfig.apiKey,
              'site_id': CinetPayConfig.siteId,
              'transaction_id': transactionId,
            }),
          )
          .timeout(Duration(seconds: CinetPayConfig.apiTimeout));

      print(
        '$_tag: Réponse de vérification (${response.statusCode}): ${response.body}',
      );

      if (response.statusCode == 200) {
        final data = jsonDecode(response.body);

        if (data['code'] == '00') {
          return {
            'success': true,
            'status': data['data']['status'],
            'amount': data['data']['amount'],
            'currency': data['data']['currency'],
            'operator_id': data['data']['operator_id'],
            'payment_method': data['data']['payment_method'],
            'message': 'Statut récupéré avec succès',
          };
        } else {
          return {
            'success': false,
            'message': data['message'] ?? 'Erreur lors de la vérification',
          };
        }
      } else {
        return {
          'success': false,
          'message': 'Erreur serveur: ${response.statusCode}',
        };
      }
    } catch (e) {
      print('$_tag: Erreur lors de la vérification: $e');
      return {'success': false, 'message': 'Erreur de connexion: $e'};
    }
  }
}
