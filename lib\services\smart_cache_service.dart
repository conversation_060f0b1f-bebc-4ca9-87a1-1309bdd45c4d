import 'dart:async';
import 'dart:convert';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:rxdart/rxdart.dart';
import 'package:connectivity_plus/connectivity_plus.dart';

/// Service de cache intelligent avec mise à jour automatique
class SmartCacheService {
  static const String _tag = '[SmartCacheService]';

  // Instance singleton
  static SmartCacheService? _instance;
  static SmartCacheService get instance => _instance ??= SmartCacheService._();

  SmartCacheService._() {
    _initializeService();
  }

  // Cache en mémoire
  final Map<String, CacheItem> _memoryCache = {};

  // Streams pour la réactivité
  final BehaviorSubject<Map<String, dynamic>> _cacheUpdatesSubject =
      BehaviorSubject<Map<String, dynamic>>.seeded({});

  // Stream public pour écouter les mises à jour du cache
  Stream<Map<String, dynamic>> get cacheUpdatesStream =>
      _cacheUpdatesSubject.stream;

  // Timers pour les mises à jour automatiques
  final Map<String, Timer> _refreshTimers = {};

  // Configuration par défaut
  static const Duration _defaultTtl = Duration(minutes: 5);
  static const Duration _defaultRefreshInterval = Duration(minutes: 2);

  /// Initialise le service de cache
  void _initializeService() {
    print('$_tag: Initialisation du service de cache intelligent...');

    // Nettoyer le cache expiré toutes les minutes
    Timer.periodic(const Duration(minutes: 1), (timer) {
      _cleanExpiredCache();
    });

    // Écouter les changements de connectivité
    Connectivity().onConnectivityChanged.listen((
      List<ConnectivityResult> results,
    ) {
      final isConnected =
          results.isNotEmpty &&
          !results.every((result) => result == ConnectivityResult.none);
      if (isConnected) {
        print('$_tag: Connexion rétablie - Rafraîchissement du cache');
        _refreshAllCacheItems();
      }
    });

    print('$_tag: Service de cache initialisé');
  }

  /// Initialise le service de cache (méthode publique)
  Future<void> initialize() async {
    // Le service est déjà initialisé dans le constructeur via _initializeService()
    return Future.value();
  }

  /// Stocke des données dans le cache avec TTL et rafraîchissement automatique
  Future<void> set<T>(
    String key,
    T data, {
    Duration? ttl,
    Duration? refreshInterval,
    Future<T> Function()? refreshFunction,
    bool persistToDisk = true,
  }) async {
    try {
      final cacheItem = CacheItem<T>(
        key: key,
        data: data,
        timestamp: DateTime.now(),
        ttl: ttl ?? _defaultTtl,
        refreshFunction: refreshFunction,
      );

      // Stocker en mémoire
      _memoryCache[key] = cacheItem;

      // Persister sur disque si demandé
      if (persistToDisk) {
        await _persistToDisk(key, data);
      }

      // Configurer le rafraîchissement automatique si une fonction est fournie
      if (refreshFunction != null) {
        _setupAutoRefresh(
          key,
          refreshInterval ?? _defaultRefreshInterval,
          refreshFunction,
        );
      }

      // Notifier les listeners
      _notifyCacheUpdate(key, data);

      print('$_tag: Données mises en cache pour la clé: $key');
    } catch (e) {
      print('$_tag: Erreur lors de la mise en cache de $key: $e');
    }
  }

  /// Récupère des données du cache
  Future<T?> get<T>(String key, {bool checkDisk = true}) async {
    try {
      // Vérifier d'abord le cache mémoire
      final memoryItem = _memoryCache[key];
      if (memoryItem != null && !memoryItem.isExpired) {
        return memoryItem.data as T?;
      }

      // Si expiré en mémoire, essayer de rafraîchir automatiquement
      if (memoryItem != null &&
          memoryItem.isExpired &&
          memoryItem.refreshFunction != null) {
        _refreshCacheItem(key);
        return memoryItem.data
            as T?; // Retourner les anciennes données en attendant
      }

      // Vérifier le stockage persistant si demandé
      if (checkDisk) {
        final diskData = await _loadFromDisk<T>(key);
        if (diskData != null) {
          // Remettre en cache mémoire
          await set(key, diskData, persistToDisk: false);
          return diskData;
        }
      }

      return null;
    } catch (e) {
      print('$_tag: Erreur lors de la récupération de $key: $e');
      return null;
    }
  }

  /// Récupère des données avec fallback et rafraîchissement automatique
  Future<T> getOrFetch<T>(
    String key,
    Future<T> Function() fetchFunction, {
    Duration? ttl,
    Duration? refreshInterval,
    bool forceRefresh = false,
    bool useStaleWhileRevalidate = true,
  }) async {
    try {
      // Si pas de rafraîchissement forcé, essayer le cache d'abord
      if (!forceRefresh) {
        final cachedData = await get<T>(key);
        if (cachedData != null) {
          // Si les données sont fraîches, les retourner directement
          final cacheItem = _memoryCache[key];
          if (cacheItem != null && !cacheItem.isExpired) {
            return cachedData;
          }

          // Si stale-while-revalidate est activé, retourner les données expirées
          // et rafraîchir en arrière-plan
          if (useStaleWhileRevalidate && cacheItem != null) {
            _refreshCacheItem(key); // Rafraîchir en arrière-plan
            return cachedData;
          }
        }
      }

      // Récupérer les nouvelles données avec retry intelligent
      final freshData = await _fetchWithRetry(fetchFunction);

      // Mettre en cache avec rafraîchissement automatique
      await set(
        key,
        freshData,
        ttl: ttl,
        refreshInterval: refreshInterval,
        refreshFunction: fetchFunction,
      );

      return freshData;
    } catch (e) {
      print('$_tag: Erreur lors de getOrFetch pour $key: $e');

      // En cas d'erreur, essayer de retourner les données en cache même expirées
      final cachedData = await get<T>(key);
      if (cachedData != null) {
        return cachedData;
      }

      rethrow;
    }
  }

  /// Récupère des données avec retry intelligent
  Future<T> _fetchWithRetry<T>(
    Future<T> Function() fetchFunction, {
    int maxRetries = 3,
    Duration initialDelay = const Duration(milliseconds: 500),
  }) async {
    int attempt = 0;
    Duration delay = initialDelay;

    while (attempt < maxRetries) {
      try {
        return await fetchFunction();
      } catch (e) {
        attempt++;
        if (attempt >= maxRetries) {
          rethrow;
        }

        // Délai exponentiel avec jitter
        final jitter = Duration(
          milliseconds:
              (delay.inMilliseconds *
                      0.1 *
                      (DateTime.now().millisecondsSinceEpoch % 100) /
                      100)
                  .round(),
        );
        await Future.delayed(delay + jitter);
        delay = Duration(milliseconds: (delay.inMilliseconds * 1.5).round());
      }
    }

    throw Exception('Échec après $maxRetries tentatives');
  }

  /// Invalide une entrée du cache
  Future<void> invalidate(String key) async {
    try {
      // Supprimer du cache mémoire
      _memoryCache.remove(key);

      // Arrêter le rafraîchissement automatique
      _refreshTimers[key]?.cancel();
      _refreshTimers.remove(key);

      // Supprimer du stockage persistant
      await _removeFromDisk(key);

      print('$_tag: Cache invalidé pour la clé: $key');
    } catch (e) {
      print('$_tag: Erreur lors de l\'invalidation de $key: $e');
    }
  }

  /// Invalide tout le cache
  Future<void> invalidateAll() async {
    try {
      // Vider le cache mémoire
      _memoryCache.clear();

      // Arrêter tous les timers
      for (final timer in _refreshTimers.values) {
        timer.cancel();
      }
      _refreshTimers.clear();

      // Vider le stockage persistant
      await _clearDiskCache();

      print('$_tag: Tout le cache a été invalidé');
    } catch (e) {
      print('$_tag: Erreur lors de l\'invalidation complète: $e');
    }
  }

  /// Configure le rafraîchissement automatique pour une clé
  void _setupAutoRefresh<T>(
    String key,
    Duration interval,
    Future<T> Function() refreshFunction,
  ) {
    // Annuler le timer existant s'il y en a un
    _refreshTimers[key]?.cancel();

    // Créer un nouveau timer
    _refreshTimers[key] = Timer.periodic(interval, (timer) async {
      await _refreshCacheItem(key);
    });
  }

  /// Rafraîchit un élément du cache
  Future<void> _refreshCacheItem(String key) async {
    try {
      final cacheItem = _memoryCache[key];
      if (cacheItem?.refreshFunction != null) {
        final freshData = await _fetchWithRetry(cacheItem!.refreshFunction!);

        // Mettre à jour le cache
        await set(
          key,
          freshData,
          ttl: cacheItem.ttl,
          refreshFunction: cacheItem.refreshFunction,
        );

        print('$_tag: Cache rafraîchi pour la clé: $key');
      }
    } catch (e) {
      print('$_tag: Erreur lors du rafraîchissement de $key: $e');
    }
  }

  /// Rafraîchit tous les éléments du cache qui ont une fonction de rafraîchissement
  Future<void> _refreshAllCacheItems() async {
    final keys =
        _memoryCache.keys
            .where((key) => _memoryCache[key]?.refreshFunction != null)
            .toList();

    for (final key in keys) {
      await _refreshCacheItem(key);
    }
  }

  /// Nettoie le cache expiré
  void _cleanExpiredCache() {
    final expiredKeys =
        _memoryCache.keys
            .where((key) => _memoryCache[key]?.isExpired ?? false)
            .toList();

    for (final key in expiredKeys) {
      _memoryCache.remove(key);
      _refreshTimers[key]?.cancel();
      _refreshTimers.remove(key);
    }

    if (expiredKeys.isNotEmpty) {
      print(
        '$_tag: ${expiredKeys.length} entrées expirées supprimées du cache',
      );
    }
  }

  /// Persiste des données sur disque
  Future<void> _persistToDisk<T>(String key, T data) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final jsonData = jsonEncode({
        'data': data,
        'timestamp': DateTime.now().millisecondsSinceEpoch,
      });
      await prefs.setString('cache_$key', jsonData);
    } catch (e) {
      print('$_tag: Erreur lors de la persistance de $key: $e');
    }
  }

  /// Charge des données depuis le disque
  Future<T?> _loadFromDisk<T>(String key) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final jsonString = prefs.getString('cache_$key');

      if (jsonString != null) {
        final jsonData = jsonDecode(jsonString);
        return jsonData['data'] as T?;
      }

      return null;
    } catch (e) {
      print('$_tag: Erreur lors du chargement de $key depuis le disque: $e');
      return null;
    }
  }

  /// Supprime des données du disque
  Future<void> _removeFromDisk(String key) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.remove('cache_$key');
    } catch (e) {
      print('$_tag: Erreur lors de la suppression de $key du disque: $e');
    }
  }

  /// Vide tout le cache disque
  Future<void> _clearDiskCache() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final keys =
          prefs.getKeys().where((key) => key.startsWith('cache_')).toList();

      for (final key in keys) {
        await prefs.remove(key);
      }
    } catch (e) {
      print('$_tag: Erreur lors du vidage du cache disque: $e');
    }
  }

  /// Notifie les listeners d'une mise à jour du cache
  void _notifyCacheUpdate(String key, dynamic data) {
    final currentUpdates = Map<String, dynamic>.from(
      _cacheUpdatesSubject.value,
    );
    currentUpdates[key] = data;
    _cacheUpdatesSubject.add(currentUpdates);
  }

  /// Obtient des statistiques du cache
  Map<String, dynamic> getCacheStats() {
    final totalItems = _memoryCache.length;
    final expiredItems =
        _memoryCache.values.where((item) => item.isExpired).length;
    final activeRefreshers = _refreshTimers.length;

    return {
      'totalItems': totalItems,
      'expiredItems': expiredItems,
      'activeItems': totalItems - expiredItems,
      'activeRefreshers': activeRefreshers,
      'memoryUsage': _memoryCache.keys.toList(),
    };
  }

  /// Nettoie les ressources
  void dispose() {
    for (final timer in _refreshTimers.values) {
      timer.cancel();
    }
    _refreshTimers.clear();
    _memoryCache.clear();
    _cacheUpdatesSubject.close();
  }
}

/// Élément du cache avec métadonnées
class CacheItem<T> {
  final String key;
  final T data;
  final DateTime timestamp;
  final Duration ttl;
  final Future<T> Function()? refreshFunction;

  CacheItem({
    required this.key,
    required this.data,
    required this.timestamp,
    required this.ttl,
    this.refreshFunction,
  });

  bool get isExpired => DateTime.now().difference(timestamp) > ttl;

  Duration get timeUntilExpiry => ttl - DateTime.now().difference(timestamp);
}
