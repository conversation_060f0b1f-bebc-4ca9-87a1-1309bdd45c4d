/// Script de test pour vérifier la gestion des URL schemes
void main() {
  print('🧪 Test de gestion des URL schemes...\n');
  
  // URLs de test
  final testUrls = [
    'https://checkout.cinetpay.com/payment/abc123',
    'intent://play.google.com/store/apps/details?id=com.wave.personal&pcampaignid=web_auto_redirect&web_logged_in=0&redirect_entry_point=dp#Intent;scheme=https;action=android.intent.action.VIEW;package=com.android.vending;end',
    'market://details?id=com.wave.personal',
    'tel:+22501020304',
    'sms:+22501020304',
    'mailto:<EMAIL>',
    'whatsapp://send?phone=22501020304',
    'wave://payment/123',
    'orange://payment/123',
    'mtn://payment/123',
    'moov://payment/123',
  ];
  
  for (final url in testUrls) {
    final shouldHandle = _shouldHandleExternally(url);
    final scheme = Uri.tryParse(url)?.scheme ?? 'unknown';
    
    print('URL: $url');
    print('Scheme: $scheme');
    print('Handle externally: $shouldHandle');
    
    if (url.startsWith('intent://')) {
      final converted = _convertIntentUrl(url);
      print('Converted: $converted');
    }
    
    print('---');
  }
  
  print('🏁 Test terminé.');
}

/// Vérifie si une URL doit être gérée par une application externe
bool _shouldHandleExternally(String url) {
  final uri = Uri.tryParse(url);
  if (uri == null) return false;

  // URL schemes qui nécessitent une application externe
  final externalSchemes = [
    'intent',      // Android intent URLs
    'market',      // Google Play Store
    'tel',         // Numéros de téléphone
    'sms',         // SMS
    'mailto',      // Email
    'whatsapp',    // WhatsApp
    'fb',          // Facebook
    'twitter',     // Twitter
    'instagram',   // Instagram
    'wave',        // Wave (paiement mobile)
    'orange',      // Orange Money
    'mtn',         // MTN Mobile Money
    'moov',        // Moov Money
  ];

  return externalSchemes.contains(uri.scheme.toLowerCase());
}

/// Convertit une intent URL en URL HTTPS
String? _convertIntentUrl(String intentUrl) {
  try {
    // Extraire l'URL HTTPS de l'intent
    final regex = RegExp(r'intent://([^#]+)#Intent;scheme=([^;]+);');
    final match = regex.firstMatch(intentUrl);
    
    if (match != null) {
      final path = match.group(1);
      final scheme = match.group(2);
      return '$scheme://$path';
    }
    
    return null;
  } catch (e) {
    return null;
  }
}
