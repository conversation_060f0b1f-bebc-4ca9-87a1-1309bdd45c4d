import 'dart:convert';
import 'package:http/http.dart' as http;
import '../config/api_config.dart';
import '../utils/image_utils.dart';

class CatalogueService {
  // Récupérer la liste des catégories// Récupérer les derniers produits
  static Future<List<Map<String, dynamic>>> getLatestProducts(int limit) async {
    try {
      // Récupérer les produits du catalogue
      print('Récupération des derniers produits des carnets et catalogues');

      // Récupérer les produits du catalogue
      final catalogueProducts = await getCatalogueProducts();

      // Récupérer les produits des carnets
      final carnetCatalogues = await getCarnetCatalogues();
      List<Map<String, dynamic>> carnetProducts = [];

      // Pour chaque catalogue de carnet, récupérer ses produits
      for (var catalogue in carnetCatalogues.take(3)) {
        // Limiter à 3 catalogues pour éviter trop d'appels API
        final String catalogueId = catalogue['id_carnet']?.toString() ?? '';
        if (catalogueId.isNotEmpty) {
          final products = await getProductsByCatalogue(catalogueId);
          carnetProducts.addAll(products);
        }
      }

      // Combiner les deux listes
      final allProducts = [...catalogueProducts, ...carnetProducts];

      // Trier par date (si disponible) ou par ID (en supposant que les IDs plus élevés sont plus récents)
      allProducts.sort((a, b) {
        // Si les produits ont une date, trier par date
        final dateA = a['date_creation'] ?? a['date_ajout'] ?? '';
        final dateB = b['date_creation'] ?? b['date_ajout'] ?? '';

        if (dateA.isNotEmpty && dateB.isNotEmpty) {
          return dateB.compareTo(
            dateA,
          ); // Ordre décroissant (plus récent d'abord)
        }

        // Sinon, trier par ID (en supposant que les IDs plus élevés sont plus récents)
        final idA = int.tryParse(a['id_kit']?.toString() ?? '0') ?? 0;
        final idB = int.tryParse(b['id_kit']?.toString() ?? '0') ?? 0;

        return idB.compareTo(idA); // Ordre décroissant
      });

      // Limiter le nombre de produits
      final limitedProducts = allProducts.take(limit).toList();

      // Formater les données des produits
      return limitedProducts.map((product) {
        // Traiter l'URL de l'image
        String imageUrl =
            product['photo_kit'] ??
            product['image'] ??
            'assets/images/product_default.jpg';
        imageUrl = ImageUtils.formatImageUrl(imageUrl);

        return {
          'id':
              product['id_kit']?.toString() ?? product['id']?.toString() ?? '',
          'name':
              product['option_kit'] ??
              product['nom_kit'] ??
              product['name'] ??
              'Produit sans nom',
          'price':
              int.tryParse(
                product['montant_total_kit']?.toString() ??
                    product['prix_kit']?.toString() ??
                    product['price']?.toString() ??
                    '0',
              ) ??
              0,
          'dailyPrice':
              int.tryParse(
                product['cout_journalier_kit']?.toString() ??
                    product['journalier_kit']?.toString() ??
                    product['dailyPrice']?.toString() ??
                    '0',
              ) ??
              0,
          'imageUrl': imageUrl,
          'description':
              product['description_kit'] ??
              product['description'] ??
              'Aucune description disponible',
          'duration':
              int.tryParse(
                product['duree_id']?.toString() ??
                    product['duration']?.toString() ??
                    '0',
              ) ??
              0,
          // Conserver les données originales pour la compatibilité
          ...Map<String, dynamic>.from(product),
        };
      }).toList();
    } catch (e) {
      print('Exception lors de la récupération des derniers produits: $e');
      return [];
    }
  }

  static Future<List<Map<String, dynamic>>> getCategories([int? limit]) async {
    try {
      print(
        'Appel API: https://api.callitris-distribution.com/client-api.callitris-distribution.com/categorie/get_categories.php',
      );
      // Construire l'URL avec ou sans limite
      String url =
          'https://api.callitris-distribution.com/client-api.callitris-distribution.com/categorie/get_categories.php';
      if (limit != null) {
        url += '?limit=$limit';
      }

      final response = await http
          .get(Uri.parse(url))
          .timeout(const Duration(seconds: 15));

      print('Statut de la réponse: ${response.statusCode}');

      if (response.statusCode == 200) {
        final jsonResponse = jsonDecode(response.body);
        if (jsonResponse.containsKey('categories')) {
          return List<Map<String, dynamic>>.from(jsonResponse['categories']);
        }
      }
      return [];
    } catch (e) {
      print('Exception lors de la récupération des catégories: $e');
      return [];
    }
  }

  // Récupérer la liste des produits du catalogue
  static Future<List<Map<String, dynamic>>> getCatalogueProducts() async {
    try {
      // Utiliser la nouvelle API pour récupérer les produits du catalogue
      print(
        'Appel API: https://api.callitris-distribution.com/client-api.callitris-distribution.com/kit/getKitcatalogue.php',
      );
      final response = await http
          .get(
            Uri.parse(
              'https://api.callitris-distribution.com/client-api.callitris-distribution.com/kit/getKitcatalogue.php',
            ),
          )
          .timeout(const Duration(seconds: 15));

      print('Statut de la réponse: ${response.statusCode}');

      if (response.statusCode == 200) {
        final jsonResponse = jsonDecode(response.body);

        // Gérer le cas d'une liste imbriquée [[{...}]]
        if (jsonResponse is List && jsonResponse.isNotEmpty) {
          if (jsonResponse[0] is List) {
            // C'est une liste imbriquée, extraire la liste interne
            return List<Map<String, dynamic>>.from(jsonResponse[0]);
          } else {
            // C'est une liste simple d'objets
            return List<Map<String, dynamic>>.from(jsonResponse);
          }
        } else if (jsonResponse is Map) {
          // Si c'est un objet, vérifier s'il contient une clé 'kits'
          if (jsonResponse.containsKey('kits')) {
            return List<Map<String, dynamic>>.from(jsonResponse['kits']);
          } else if (jsonResponse.containsKey('catalogue')) {
            return List<Map<String, dynamic>>.from(jsonResponse['catalogue']);
          } else {
            // Si l'objet ne contient pas de clé 'kits', vérifier s'il y a d'autres clés qui pourraient contenir les données
            final possibleKeys = ['data', 'results', 'items', 'products'];
            for (final key in possibleKeys) {
              if (jsonResponse.containsKey(key) && jsonResponse[key] is List) {
                return List<Map<String, dynamic>>.from(jsonResponse[key]);
              }
            }
          }
        }

        // Si on ne peut pas déterminer la structure, retourner une liste vide
        print('Structure de réponse non reconnue: $jsonResponse');
        return [];
      }
      return [];
    } catch (e) {
      print('Exception lors de la récupération des produits du catalogue: $e');
      return [];
    }
  }

  // Récupérer les produits d'une catégorie spécifique
  static Future<List<Map<String, dynamic>>> getProductsByCategory(
    String categoryId,
  ) async {
    try {
      // Utiliser directement la nouvelle API
      return getProductsByCategoryNewApi(categoryId);
    } catch (e) {
      print('Exception lors de la récupération des produits par catégorie: $e');
      return [];
    }
  }

  // Récupérer les produits d'une catégorie spécifique avec la nouvelle API
  static Future<List<Map<String, dynamic>>> getProductsByCategoryNewApi(
    String categoryId,
  ) async {
    try {
      print(
        'Appel API: https://api.callitris-distribution.com/client-api.callitris-distribution.com/kit/getKitcatalogue.php?livret_id=$categoryId',
      );
      final response = await http
          .get(
            Uri.parse(
              'https://api.callitris-distribution.com/client-api.callitris-distribution.com/kit/getKitcatalogue.php?livret_id=$categoryId',
            ),
          )
          .timeout(const Duration(seconds: 15));

      print('Statut de la réponse: ${response.statusCode}');
      print('Contenu de la réponse: ${response.body}');

      if (response.statusCode == 200) {
        final jsonResponse = jsonDecode(response.body);

        // Gérer le cas d'une liste imbriquée [[{...}]]
        if (jsonResponse is List && jsonResponse.isNotEmpty) {
          if (jsonResponse[0] is List) {
            // C'est une liste imbriquée, extraire la liste interne
            return List<Map<String, dynamic>>.from(jsonResponse[0]);
          } else {
            // C'est une liste simple d'objets
            return List<Map<String, dynamic>>.from(jsonResponse);
          }
        } else if (jsonResponse is Map) {
          // Si c'est un objet, vérifier s'il contient une clé 'kits'
          if (jsonResponse.containsKey('kits')) {
            return List<Map<String, dynamic>>.from(jsonResponse['kits']);
          } else {
            // Si l'objet ne contient pas de clé 'kits', vérifier s'il y a d'autres clés qui pourraient contenir les données
            final possibleKeys = [
              'data',
              'results',
              'items',
              'products',
              'catalogue',
            ];
            for (final key in possibleKeys) {
              if (jsonResponse.containsKey(key) && jsonResponse[key] is List) {
                return List<Map<String, dynamic>>.from(jsonResponse[key]);
              }
            }

            // Si aucune clé connue n'est trouvée, retourner l'objet lui-même s'il semble être un produit
            if (jsonResponse.containsKey('id_kit') ||
                jsonResponse.containsKey('nom_kit')) {
              return [Map<String, dynamic>.from(jsonResponse)];
            }
          }
        }

        // Si on ne peut pas déterminer la structure, retourner une liste vide
        print('Structure de réponse non reconnue: $jsonResponse');
        return [];
      }
      return [];
    } catch (e) {
      print('Exception lors de la récupération des produits par catégorie: $e');
      return [];
    }
  }

  static Future<List<Map<String, dynamic>>> getProductsByCategoryNew(
    String categoryId,
  ) async {
    try {
      print(
        'Appel API: https://api.callitris-distribution.com/client-api.callitris-distribution.com/kit/getKitCategorie.php?livret_id=$categoryId',
      );
      final response = await http
          .get(
            Uri.parse(
              'https://api.callitris-distribution.com/client-api.callitris-distribution.com/kit/getKitCategorie.php?livret_id=$categoryId',
            ),
          )
          .timeout(const Duration(seconds: 15));

      print('Statut de la réponse: ${response.statusCode}');
      print('Contenu de la réponse: ${response.body}');

      if (response.statusCode == 200) {
        final jsonResponse = jsonDecode(response.body);

        // Gérer le cas d'une liste imbriquée [[{...}]]
        if (jsonResponse is List && jsonResponse.isNotEmpty) {
          if (jsonResponse[0] is List) {
            // C'est une liste imbriquée, extraire la liste interne
            return List<Map<String, dynamic>>.from(jsonResponse[0]);
          } else {
            // C'est une liste simple d'objets
            return List<Map<String, dynamic>>.from(jsonResponse);
          }
        } else if (jsonResponse is Map) {
          // Si c'est un objet, vérifier s'il contient une clé 'kits'
          if (jsonResponse.containsKey('kits')) {
            return List<Map<String, dynamic>>.from(jsonResponse['kits']);
          } else {
            // Si l'objet ne contient pas de clé 'kits', vérifier s'il y a d'autres clés qui pourraient contenir les données
            final possibleKeys = [
              'data',
              'results',
              'items',
              'products',
              'catalogue',
            ];
            for (final key in possibleKeys) {
              if (jsonResponse.containsKey(key) && jsonResponse[key] is List) {
                return List<Map<String, dynamic>>.from(jsonResponse[key]);
              }
            }

            // Si aucune clé connue n'est trouvée, retourner l'objet lui-même s'il semble être un produit
            if (jsonResponse.containsKey('id_kit') ||
                jsonResponse.containsKey('nom_kit')) {
              return [Map<String, dynamic>.from(jsonResponse)];
            }
          }
        }

        // Si on ne peut pas déterminer la structure, retourner une liste vide
        print('Structure de réponse non reconnue: $jsonResponse');
        return [];
      }
      return [];
    } catch (e) {
      print('Exception lors de la récupération des produits par catégorie: $e');
      return [];
    }
  }

  // Récupérer les catalogues du carnet catalogue
  static Future<List<Map<String, dynamic>>> getCarnetCatalogues() async {
    try {
      print(
        'Appel API: https://api.callitris-distribution.com/client-api.callitris-distribution.com/carnet/get_carnetCatalogue.php',
      );
      final response = await http
          .get(
            Uri.parse(
              'https://api.callitris-distribution.com/client-api.callitris-distribution.com/carnet/get_carnetCatalogue.php',
            ),
          )
          .timeout(const Duration(seconds: 15));

      print('Statut de la réponse: ${response.statusCode}');
      print('Contenu de la réponse: ${response.body}');

      if (response.statusCode == 200) {
        final jsonResponse = jsonDecode(response.body);
        if (jsonResponse.containsKey('categories')) {
          return List<Map<String, dynamic>>.from(jsonResponse['categories']);
        }
      }
      return [];
    } catch (e) {
      print('Exception lors de la récupération des catalogues: $e');
      return [];
    }
  }

  // Récupérer les produits d'un catalogue spécifique
  static Future<List<Map<String, dynamic>>> getProductsByCatalogue(
    String catalogueId,
  ) async {
    try {
      print(
        'Appel API: https://api.callitris-distribution.com/client-api.calliris-distribution.com/kit/getKitcatalogue.php?livret_id=$catalogueId',
      );
      final response = await http
          .get(
            Uri.parse(
              'https://api.callitris-distribution.com/client-api.callitris-distribution.com/kit/getKitcatalogue.php?livret_id=$catalogueId',
            ),
          )
          .timeout(const Duration(seconds: 15));

      print('Statut de la réponse: ${response.statusCode}');
      print('Contenu de la réponse: ${response.body}');

      if (response.statusCode == 200) {
        final jsonResponse = jsonDecode(response.body);

        print(' Structure de la réponse: ${jsonResponse.runtimeType}');
        print(' Contenu de la réponse: $jsonResponse');

        // Gérer la structure de réponse imbriquée [[{...}]]
        if (jsonResponse is List && jsonResponse.isNotEmpty) {
          final List<Map<String, dynamic>> products = [];

          for (var outerItem in jsonResponse) {
            if (outerItem is List) {
              // C'est un tableau imbriqué, extraire les éléments
              for (var innerItem in outerItem) {
                if (innerItem is Map<String, dynamic>) {
                  products.add(innerItem);
                } else if (innerItem is Map) {
                  // Convertir Map vers Map<String, dynamic>
                  products.add(Map<String, dynamic>.from(innerItem));
                } else {
                  print(
                    ' Élément interne ignoré (type non supporté): ${innerItem.runtimeType} - $innerItem',
                  );
                }
              }
            } else if (outerItem is Map<String, dynamic>) {
              // Structure simple, ajouter directement
              products.add(outerItem);
            } else if (outerItem is Map) {
              // Convertir Map vers Map<String, dynamic>
              products.add(Map<String, dynamic>.from(outerItem));
            } else {
              print(
                ' Élément externe ignoré (type non supporté): ${outerItem.runtimeType} - $outerItem',
              );
            }
          }

          print(' Nombre de produits valides extraits: ${products.length}');
          return products;
        } else if (jsonResponse is Map && jsonResponse.containsKey('kits')) {
          final kits = jsonResponse['kits'];
          if (kits is List) {
            return _convertToProductList(kits);
          }
        } else if (jsonResponse is Map &&
            jsonResponse.containsKey('produits')) {
          final produits = jsonResponse['produits'];
          if (produits is List) {
            return _convertToProductList(produits);
          }
        } else if (jsonResponse is Map) {
          print(
            ' Réponse est un Map avec les clés: ${jsonResponse.keys.toList()}',
          );
          // Essayer de trouver une liste de produits dans les clés communes
          for (String key in [
            'data',
            'items',
            'results',
            'products',
            'catalogue',
          ]) {
            if (jsonResponse.containsKey(key) && jsonResponse[key] is List) {
              return _convertToProductList(jsonResponse[key]);
            }
          }
        }
      }
      return [];
    } catch (e) {
      print('Exception lors de la récupération des produits du catalogue: $e');
      return [];
    }
  }

  // Méthode utilitaire pour convertir une liste en List<Map<String, dynamic>>
  static List<Map<String, dynamic>> _convertToProductList(List items) {
    final List<Map<String, dynamic>> products = [];
    for (var item in items) {
      if (item is Map<String, dynamic>) {
        products.add(item);
      } else if (item is Map) {
        // Convertir Map vers Map<String, dynamic>
        products.add(Map<String, dynamic>.from(item));
      } else {
        print(
          ' Élément ignoré (type non supporté): ${item.runtimeType} - $item',
        );
      }
    }
    print(' Nombre de produits valides extraits: ${products.length}');
    return products;
  }

  // Récupérer les produits en vedette pour le carrousel
  static Future<List<Map<String, dynamic>>> getFeaturedProducts() async {
    try {
      print('Appel API: ${ApiConfig.getFeaturedProductsEndpoint}');
      final response = await http
          .get(Uri.parse(ApiConfig.getFeaturedProductsEndpoint))
          .timeout(const Duration(seconds: 15));

      print('Statut de la réponse: ${response.statusCode}');
      print('Contenu de la réponse: ${response.body}');

      if (response.statusCode == 200) {
        final jsonResponse = jsonDecode(response.body);

        // Vérifier la structure de la réponse
        if (jsonResponse is List) {
          return _formatProductsData(jsonResponse);
        } else if (jsonResponse is Map) {
          if (jsonResponse.containsKey('products')) {
            return _formatProductsData(jsonResponse['products']);
          } else if (jsonResponse.containsKey('featured')) {
            return _formatProductsData(jsonResponse['featured']);
          } else {
            // Parcourir toutes les clés pour trouver une liste
            for (var key in jsonResponse.keys) {
              if (jsonResponse[key] is List) {
                return _formatProductsData(jsonResponse[key]);
              }
            }
          }
        }
      }

      // Si aucune donnée n'est disponible, retourner une liste vide
      return [];
    } catch (e) {
      print('Exception lors de la récupération des produits en vedette: $e');
      return [];
    }
  }

  // Formater les données des produits
  static List<Map<String, dynamic>> _formatProductsData(List<dynamic> data) {
    return data.map((product) {
      // Traiter l'URL de l'image
      String imageUrl =
          product['photo_kit'] ??
          product['image'] ??
          'assets/images/product_default.jpg';
      imageUrl = ImageUtils.formatImageUrl(imageUrl);

      // Calculer le prix journalier si non disponible
      int totalPrice =
          int.tryParse(
            product['montant_total_kit']?.toString() ??
                product['prix_kit']?.toString() ??
                product['price']?.toString() ??
                '0',
          ) ??
          0;

      int dailyPrice =
          int.tryParse(
            product['cout_journalier_kit']?.toString() ??
                product['journalier_kit']?.toString() ??
                product['daily_price']?.toString() ??
                '0',
          ) ??
          0;

      // Si le prix journalier n'est pas disponible, le calculer (20 jours par défaut)
      if (dailyPrice == 0 && totalPrice > 0) {
        dailyPrice = (totalPrice / 20).round();
      }

      // Déterminer la remise (discount)
      String discount = product['discount']?.toString() ?? '';
      if (discount.isEmpty && product['remise'] != null) {
        int remise = int.tryParse(product['remise'].toString()) ?? 0;
        if (remise > 0) {
          discount = '$remise%';
        }
      }

      return {
        'id': product['id_kit']?.toString() ?? product['id']?.toString() ?? '',
        'name':
            product['nom_kit'] ??
            product['nom_produit'] ??
            product['name'] ??
            'Produit sans nom',
        'price': totalPrice,
        'dailyPrice': dailyPrice,
        'imageUrl': imageUrl,
        'discount': discount,
        'description':
            product['description_kit'] ??
            product['description'] ??
            'Aucune description disponible',
      };
    }).toList();
  }
}
