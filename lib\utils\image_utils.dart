import 'package:flutter/material.dart';

class ImageUtils {
  // Formater l'URL de l'image pour qu'elle soit utilisable
  static String formatImageUrl(String url) {
    if (url.isEmpty) {
      return ''; // Retourner vide pour utiliser le placeholder d'icône
    }

    print("URL avant formatage: $url");

    // Si l'URL est déjà complète, la retourner telle quelle
    if (url.startsWith('http://') || url.startsWith('https://')) {
      return url;
    }

    // Si l'URL est un chemin d'asset, la retourner telle quelle
    if (url.startsWith('assets/')) {
      return url;
    }

    // Approche directe : extraire uniquement la partie img/kit/filename.jpg
    String finalPath = "";

    // Rechercher le motif "img/kit/" dans l'URL
    if (url.contains("img/kit/")) {
      int startIndex = url.indexOf("img/kit/");
      finalPath = url.substring(startIndex);
    }
    // Rechercher le motif "img/" dans l'URL
    else if (url.contains("img/")) {
      int startIndex = url.indexOf("img/");
      finalPath = url.substring(startIndex);
    }
    // Si aucun motif n'est trouvé, utiliser l'URL nettoyée des préfixes communs
    else {
      finalPath = url;
      if (finalPath.contains("../../")) {
        finalPath = finalPath.replaceAll("../../", "");
      }
      if (finalPath.contains("app/")) {
        finalPath = finalPath.replaceAll("app/", "");
      }
      if (finalPath.contains("public/")) {
        // Remplacer toutes les occurrences de "public/" par une seule
        while (finalPath.contains("public/public/")) {
          finalPath = finalPath.replaceAll("public/public/", "public/");
        }
      }
    }

    // Construire l'URL finale
    String finalUrl =
        'https://app.callitris-distribution.com/app/public/$finalPath';

    // Vérifier si l'URL finale contient un doublon de "public/"
    while (finalUrl.contains("public/public/")) {
      finalUrl = finalUrl.replaceAll("public/public/", "public/");
    }

    print("URL après formatage: $finalUrl");
    return finalUrl;
  }

  // Widget pour afficher une image avec gestion des erreurs (assets et réseau)
  static Widget networkImageWithErrorHandling({
    required String imageUrl,
    double? width,
    double? height,
    BoxFit fit = BoxFit.cover,
    Color? placeholderColor,
    IconData placeholderIcon = Icons.image_not_supported,
    double? placeholderIconSize,
  }) {
    // Widget de placeholder en cas d'erreur
    Widget buildPlaceholder() {
      double iconSize = placeholderIconSize ?? 24.0;
      if (placeholderIconSize == null && width != null && height != null) {
        iconSize = width < height ? width / 2 : height / 2;
      }

      return Container(
        width: width,
        height: height,
        color: placeholderColor ?? Colors.grey[200],
        child: Icon(placeholderIcon, color: Colors.grey[400], size: iconSize),
      );
    }

    // Si l'URL est vide, retourner directement le placeholder
    if (imageUrl.isEmpty) {
      return buildPlaceholder();
    }

    // Si c'est un asset, utiliser Image.asset
    if (imageUrl.startsWith('assets/')) {
      return Image.asset(
        imageUrl,
        width: width,
        height: height,
        fit: fit,
        errorBuilder: (context, error, stackTrace) {
          print('Erreur de chargement d\'asset: $imageUrl - $error');
          return buildPlaceholder();
        },
      );
    }

    // Sinon, utiliser Image.network pour les URLs
    return Image.network(
      imageUrl,
      width: width,
      height: height,
      fit: fit,
      errorBuilder: (context, error, stackTrace) {
        print('Erreur de chargement d\'image réseau: $imageUrl - $error');
        return buildPlaceholder();
      },
      loadingBuilder: (context, child, loadingProgress) {
        if (loadingProgress == null) {
          return child;
        }
        return Container(
          width: width,
          height: height,
          color: placeholderColor ?? Colors.grey[200],
          child: Center(
            child: CircularProgressIndicator(
              value:
                  loadingProgress.expectedTotalBytes != null
                      ? loadingProgress.cumulativeBytesLoaded /
                          loadingProgress.expectedTotalBytes!
                      : null,
              strokeWidth: 2,
            ),
          ),
        );
      },
    );
  }

  // Ajouter cette méthode pour tester le formatage d'URL
  // Ajouter cette méthode pour tester le formatage d'URL
  static void testFormatImageUrl() {
    List<String> testUrls = [
      "../../public/img/kit/b5521452f85e7f1ddc53099c97da2cb4cd8cf0b0Pack 08.jpg",
      "../../public/img/kit/a798046bca4c73c4145b30e9a6ee4728e0615d07Pack 01.jpg",
      "../../public/img/kit/732d1ea2bdb9e617ea7249acb75e9fa61b45d82dPack 12.jpg",
    ];

    for (String testUrl in testUrls) {
      String formattedUrl = formatImageUrl(testUrl);
      print("TEST - URL originale: $testUrl");
      print("TEST - URL formatée: $formattedUrl");

      // Vérifier si l'URL formatée contient un doublon de "public/"
      if (formattedUrl.contains("public/public/")) {
        print(
          "ERREUR - L'URL formatée contient toujours un doublon de 'public/'",
        );
      } else {
        print(
          "SUCCÈS - L'URL formatée ne contient pas de doublon de 'public/'",
        );
      }
    }
  }
}
