import 'dart:convert';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:callitris/models/cinetpay_transaction.dart';
import 'package:callitris/services/auth_service.dart';

/// Service pour gérer l'historique des transactions CinetPay
class TransactionHistoryService {
  static const String _transactionsKey = 'cinetpay_transactions';
  static const String _tag = 'TransactionHistoryService';
  
  /// Sauvegarde une transaction dans l'historique local
  static Future<bool> saveTransaction(CinetPayTransaction transaction) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final userData = await AuthService.getUserData();
      
      if (userData == null) {
        print('$_tag: Impossible de sauvegarder - utilisateur non connecté');
        return false;
      }
      
      final userId = userData['id_client'].toString();
      final userTransactionsKey = '${_transactionsKey}_$userId';
      
      // Récupérer les transactions existantes
      final existingTransactions = await getTransactions();
      
      // Ajouter ou mettre à jour la transaction
      final updatedTransactions = <CinetPayTransaction>[];
      bool found = false;
      
      for (final existing in existingTransactions) {
        if (existing.id == transaction.id) {
          updatedTransactions.add(transaction);
          found = true;
        } else {
          updatedTransactions.add(existing);
        }
      }
      
      if (!found) {
        updatedTransactions.add(transaction);
      }
      
      // Trier par date de création (plus récent en premier)
      updatedTransactions.sort((a, b) => b.createdAt.compareTo(a.createdAt));
      
      // Limiter à 100 transactions maximum
      if (updatedTransactions.length > 100) {
        updatedTransactions.removeRange(100, updatedTransactions.length);
      }
      
      // Sauvegarder
      final transactionsJson = updatedTransactions
          .map((t) => t.toJson())
          .toList();
      
      final success = await prefs.setString(
        userTransactionsKey,
        jsonEncode(transactionsJson),
      );
      
      print('$_tag: Transaction ${transaction.id} sauvegardée: $success');
      return success;
      
    } catch (e) {
      print('$_tag: Erreur lors de la sauvegarde: $e');
      return false;
    }
  }
  
  /// Récupère toutes les transactions de l'utilisateur connecté
  static Future<List<CinetPayTransaction>> getTransactions() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final userData = await AuthService.getUserData();
      
      if (userData == null) {
        print('$_tag: Impossible de récupérer - utilisateur non connecté');
        return [];
      }
      
      final userId = userData['id_client'].toString();
      final userTransactionsKey = '${_transactionsKey}_$userId';
      
      final transactionsString = prefs.getString(userTransactionsKey);
      
      if (transactionsString == null) {
        return [];
      }
      
      final List<dynamic> transactionsJson = jsonDecode(transactionsString);
      
      return transactionsJson
          .map((json) => CinetPayTransaction.fromJson(json))
          .toList();
      
    } catch (e) {
      print('$_tag: Erreur lors de la récupération: $e');
      return [];
    }
  }
  
  /// Récupère une transaction spécifique par son ID
  static Future<CinetPayTransaction?> getTransaction(String transactionId) async {
    try {
      final transactions = await getTransactions();
      
      for (final transaction in transactions) {
        if (transaction.id == transactionId) {
          return transaction;
        }
      }
      
      return null;
      
    } catch (e) {
      print('$_tag: Erreur lors de la récupération de la transaction $transactionId: $e');
      return null;
    }
  }
  
  /// Récupère les transactions par statut
  static Future<List<CinetPayTransaction>> getTransactionsByStatus(
    TransactionStatus status,
  ) async {
    try {
      final transactions = await getTransactions();
      
      return transactions
          .where((t) => t.status == status)
          .toList();
      
    } catch (e) {
      print('$_tag: Erreur lors de la récupération par statut: $e');
      return [];
    }
  }
  
  /// Récupère les transactions par type
  static Future<List<CinetPayTransaction>> getTransactionsByType(
    TransactionType type,
  ) async {
    try {
      final transactions = await getTransactions();
      
      return transactions
          .where((t) => t.type == type)
          .toList();
      
    } catch (e) {
      print('$_tag: Erreur lors de la récupération par type: $e');
      return [];
    }
  }
  
  /// Récupère les transactions pour une commande spécifique
  static Future<List<CinetPayTransaction>> getTransactionsForOrder(
    String orderId,
  ) async {
    try {
      final transactions = await getTransactions();
      
      return transactions
          .where((t) => t.orderId == orderId)
          .toList();
      
    } catch (e) {
      print('$_tag: Erreur lors de la récupération pour la commande $orderId: $e');
      return [];
    }
  }
  
  /// Supprime une transaction de l'historique
  static Future<bool> deleteTransaction(String transactionId) async {
    try {
      final transactions = await getTransactions();
      
      final updatedTransactions = transactions
          .where((t) => t.id != transactionId)
          .toList();
      
      if (updatedTransactions.length == transactions.length) {
        // Transaction non trouvée
        return false;
      }
      
      final prefs = await SharedPreferences.getInstance();
      final userData = await AuthService.getUserData();
      
      if (userData == null) {
        return false;
      }
      
      final userId = userData['id_client'].toString();
      final userTransactionsKey = '${_transactionsKey}_$userId';
      
      final transactionsJson = updatedTransactions
          .map((t) => t.toJson())
          .toList();
      
      final success = await prefs.setString(
        userTransactionsKey,
        jsonEncode(transactionsJson),
      );
      
      print('$_tag: Transaction $transactionId supprimée: $success');
      return success;
      
    } catch (e) {
      print('$_tag: Erreur lors de la suppression: $e');
      return false;
    }
  }
  
  /// Efface tout l'historique des transactions
  static Future<bool> clearAllTransactions() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final userData = await AuthService.getUserData();
      
      if (userData == null) {
        return false;
      }
      
      final userId = userData['id_client'].toString();
      final userTransactionsKey = '${_transactionsKey}_$userId';
      
      final success = await prefs.remove(userTransactionsKey);
      
      print('$_tag: Historique effacé: $success');
      return success;
      
    } catch (e) {
      print('$_tag: Erreur lors de l\'effacement: $e');
      return false;
    }
  }
  
  /// Calcule les statistiques des transactions
  static Future<Map<String, dynamic>> getTransactionStats() async {
    try {
      final transactions = await getTransactions();
      
      if (transactions.isEmpty) {
        return {
          'total': 0,
          'completed': 0,
          'failed': 0,
          'pending': 0,
          'totalAmount': 0.0,
          'completedAmount': 0.0,
        };
      }
      
      int completed = 0;
      int failed = 0;
      int pending = 0;
      double totalAmount = 0.0;
      double completedAmount = 0.0;
      
      for (final transaction in transactions) {
        totalAmount += transaction.amount;
        
        switch (transaction.status) {
          case TransactionStatus.completed:
            completed++;
            completedAmount += transaction.amount;
            break;
          case TransactionStatus.failed:
            failed++;
            break;
          case TransactionStatus.pending:
          case TransactionStatus.processing:
            pending++;
            break;
          default:
            break;
        }
      }
      
      return {
        'total': transactions.length,
        'completed': completed,
        'failed': failed,
        'pending': pending,
        'totalAmount': totalAmount,
        'completedAmount': completedAmount,
        'successRate': transactions.isNotEmpty ? (completed / transactions.length) * 100 : 0.0,
      };
      
    } catch (e) {
      print('$_tag: Erreur lors du calcul des statistiques: $e');
      return {};
    }
  }
}
