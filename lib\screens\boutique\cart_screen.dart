import 'package:flutter/material.dart';
import 'package:callitris/utils/appTheme.dart';

class CartScreen extends StatefulWidget {
  const CartScreen({super.key});

  @override
  State<CartScreen> createState() => _CartScreenState();
}

class _CartScreenState extends State<CartScreen> {
  final bool _isLoading = false;
  List<Map<String, dynamic>> _cartItems = [
    {
      'id': '1',
      'name': 'Smartphone Samsung Galaxy A54',
      'price': 250000,
      'dailyPrice': 12500,
      'duration': 20,
      'quantity': 1,
      'imageUrl': 'assets/images/phone1.jpg',
    },
    {
      'id': '2',
      'name': 'Téléviseur LG 43 pouces Smart TV',
      'price': 180000,
      'dailyPrice': 9000,
      'duration': 20,
      'quantity': 1,
      'imageUrl': 'assets/images/tv1.jpg',
    },
  ];

  String _formatPrice(dynamic price) {
    // Convertir en entier si nécessaire
    final int priceInt = price is int ? price : price.toInt();
    final String priceString = priceInt.toString();
    final StringBuffer result = StringBuffer();

    for (int i = 0; i < priceString.length; i++) {
      if (i > 0 && (priceString.length - i) % 3 == 0) {
        result.write(' ');
      }
      result.write(priceString[i]);
    }

    return result.toString();
  }

  int _calculateTotal() {
    return _cartItems.fold(
      0,
      (total, item) => total + (item['price'] * item['quantity'] as int),
    );
  }

  void _updateQuantity(Map<String, dynamic> item, int change) {
    setState(() {
      final int newQuantity = item['quantity'] + change;
      if (newQuantity > 0) {
        item['quantity'] = newQuantity;
      } else {
        _removeItem(item);
      }
    });
  }

  void _removeItem(Map<String, dynamic> item) {
    setState(() {
      _cartItems.removeWhere((cartItem) => cartItem['id'] == item['id']);
    });
  }

  void _showClearCartDialog() {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (BuildContext context) {
        return AlertDialog(
          title: Text(
            'Vider le panier',
            style: TextStyle(color: AppTheme.color.primaryColor),
          ),
          content: const Text('Êtes-vous sûr de vouloir vider votre panier ?'),
          actions: [
            TextButton(
              onPressed: () {
                Navigator.of(context).pop();
              },
              child: Text(
                'Annuler',
                style: TextStyle(color: AppTheme.color.brunGris),
              ),
            ),
            ElevatedButton(
              onPressed: () {
                Navigator.of(context).pop();
                setState(() {
                  _cartItems = [];
                });
              },
              style: ElevatedButton.styleFrom(
                backgroundColor: AppTheme.color.redColor,
              ),
              child: const Text('Vider'),
            ),
          ],
        );
      },
    );
  }

  void _proceedToCheckout() {
    if (_cartItems.isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Votre panier est vide'),
          backgroundColor: Colors.red,
        ),
      );
      return;
    }

    // Naviguer vers l'écran de paiement
    // TODO: Implémenter la navigation vers l'écran de paiement
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: const Text('Redirection vers le paiement...'),
        backgroundColor: AppTheme.color.greenColor,
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Mon panier'),
        actions: [
          if (_cartItems.isNotEmpty)
            IconButton(
              icon: const Icon(Icons.delete_outline),
              onPressed: _showClearCartDialog,
              tooltip: 'Vider le panier',
            ),
        ],
      ),
      body:
          _isLoading
              ? const Center(child: CircularProgressIndicator())
              : _cartItems.isEmpty
              ? _buildEmptyCart()
              : _buildCartContent(),
      bottomNavigationBar: _cartItems.isEmpty ? null : _buildBottomBar(),
    );
  }

  Widget _buildEmptyCart() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(Icons.shopping_cart_outlined, size: 80, color: Colors.grey[400]),
          const SizedBox(height: 16),
          Text(
            'Votre panier est vide',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: AppTheme.color.brunGris,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'Parcourez notre boutique pour trouver des produits',
            style: TextStyle(color: AppTheme.color.brunGris),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 24),
          ElevatedButton(
            onPressed: () {
              Navigator.of(context).pop();
            },
            child: const Text('Voir la boutique'),
          ),
        ],
      ),
    );
  }

  Widget _buildCartContent() {
    return ListView(
      padding: const EdgeInsets.all(16),
      children: [
        // En-tête
        Text(
          'Articles (${_cartItems.length})',
          style: const TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
        ),
        const SizedBox(height: 16),

        // Liste des articles
        ..._cartItems.map((item) => _buildCartItem(item)),

        const SizedBox(height: 16),

        // Résumé de la commande
        _buildOrderSummary(),
      ],
    );
  }

  Widget _buildCartItem(Map<String, dynamic> item) {
    return Card(
      margin: const EdgeInsets.only(bottom: 16),
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: const EdgeInsets.all(12),
        child: Row(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Image du produit
            ClipRRect(
              borderRadius: BorderRadius.circular(8),
              child: Image.asset(
                item['imageUrl'],
                width: 80,
                height: 80,
                fit: BoxFit.cover,
                errorBuilder: (context, error, stackTrace) {
                  return Container(
                    width: 80,
                    height: 80,
                    color: Colors.grey[200],
                    child: Icon(
                      Icons.image_not_supported,
                      color: AppTheme.color.brunGris,
                      size: 30,
                    ),
                  );
                },
              ),
            ),
            const SizedBox(width: 12),

            // Informations sur le produit
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    item['name'],
                    style: const TextStyle(
                      fontWeight: FontWeight.bold,
                      fontSize: 16,
                    ),
                    maxLines: 2,
                    overflow: TextOverflow.ellipsis,
                  ),
                  const SizedBox(height: 8),
                  Text(
                    '${_formatPrice(item['price'])} FCFA',
                    style: TextStyle(
                      color: AppTheme.color.primaryColor,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(height: 4),
                  Text(
                    'Versement: ${_formatPrice(item['dailyPrice'])} FCFA × ${item['duration']} jours',
                    style: TextStyle(
                      color: AppTheme.color.brunGris,
                      fontSize: 12,
                    ),
                  ),
                  const SizedBox(height: 8),

                  // Contrôles de quantité
                  Row(
                    children: [
                      Container(
                        decoration: BoxDecoration(
                          border: Border.all(color: Colors.grey[300]!),
                          borderRadius: BorderRadius.circular(4),
                        ),
                        child: Row(
                          children: [
                            IconButton(
                              icon: const Icon(Icons.remove, size: 16),
                              onPressed: () => _updateQuantity(item, -1),
                              constraints: const BoxConstraints(
                                minWidth: 32,
                                minHeight: 32,
                              ),
                              padding: EdgeInsets.zero,
                            ),
                            Text(
                              '${item['quantity']}',
                              style: const TextStyle(
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                            IconButton(
                              icon: const Icon(Icons.add, size: 16),
                              onPressed: () => _updateQuantity(item, 1),
                              constraints: const BoxConstraints(
                                minWidth: 32,
                                minHeight: 32,
                              ),
                              padding: EdgeInsets.zero,
                            ),
                          ],
                        ),
                      ),
                      const Spacer(),
                      IconButton(
                        icon: Icon(
                          Icons.delete_outline,
                          color: AppTheme.color.redColor,
                        ),
                        onPressed: () => _removeItem(item),
                        tooltip: 'Supprimer',
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildOrderSummary() {
    return Card(
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Résumé de la commande',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
                color: AppTheme.color.brunGris,
              ),
            ),
            const SizedBox(height: 16),

            // Sous-total
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  'Sous-total',
                  style: TextStyle(color: AppTheme.color.brunGris),
                ),
                Text(
                  '${_formatPrice(_calculateTotal())} FCFA',
                  style: const TextStyle(fontWeight: FontWeight.bold),
                ),
              ],
            ),
            const SizedBox(height: 8),

            // Frais de livraison
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  'Frais de livraison',
                  style: TextStyle(color: AppTheme.color.brunGris),
                ),
                Text(
                  '2 000 FCFA',
                  style: const TextStyle(fontWeight: FontWeight.bold),
                ),
              ],
            ),
            const SizedBox(height: 16),

            // Total
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  'Total',
                  style: TextStyle(
                    color: AppTheme.color.brunGris,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                Text(
                  '${_formatPrice(_calculateTotal() + 2000)} FCFA',
                  style: TextStyle(
                    color: AppTheme.color.primaryColor,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildBottomBar() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 10,
            offset: const Offset(0, -5),
          ),
        ],
      ),
      child: Row(
        children: [
          Expanded(
            child: Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                const Text(
                  'Total',
                  style: TextStyle(color: Colors.grey, fontSize: 14),
                ),
                Text(
                  '${_formatPrice(_calculateTotal() + 2000)} FCFA',
                  style: TextStyle(
                    fontSize: 20,
                    fontWeight: FontWeight.bold,
                    color: AppTheme.color.primaryColor,
                  ),
                ),
              ],
            ),
          ),
          ElevatedButton(
            onPressed: _proceedToCheckout,
            style: ElevatedButton.styleFrom(
              padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(10),
              ),
            ),
            child: const Text(
              'Passer la commande',
              style: TextStyle(fontSize: 16),
            ),
          ),
        ],
      ),
    );
  }
}
