import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:callitris/utils/appTheme.dart';
import 'package:callitris/screens/auth/login_screen.dart';
import 'package:callitris/screens/auth/privacy_policy_screen.dart';
import 'package:callitris/config/api_config.dart';
import 'package:http/http.dart' as http;
import 'dart:convert';

import '../../services/auth_service.dart';

class RegisterScreen extends StatefulWidget {
  const RegisterScreen({super.key});

  @override
  State<RegisterScreen> createState() => _RegisterScreenState();
}

class _RegisterScreenState extends State<RegisterScreen>
    with SingleTickerProviderStateMixin {
  final _formKey = GlobalKey<FormState>();
  final _nomController = TextEditingController();
  final _prenomController = TextEditingController();
  final _telephoneController = TextEditingController();

  String _sexe = 'Homme';
  String? _selectedLocalite;
  List<Map<String, dynamic>> _localites = [];
  bool _isLoading = false;
  bool _isLoadingLocalites = false;
  bool _acceptTerms = false;

  late AnimationController _animationController;
  late Animation<double> _fadeAnimation;
  late Animation<Offset> _slideAnimation;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 500),
    );
    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(_animationController);
    _slideAnimation = Tween<Offset>(
      begin: const Offset(0, 0.1),
      end: Offset.zero,
    ).animate(
      CurvedAnimation(parent: _animationController, curve: Curves.easeOut),
    );

    _animationController.forward();
    _loadLocalites(); // Charger les localités au démarrage
  }

  @override
  void dispose() {
    _nomController.dispose();
    _prenomController.dispose();
    _telephoneController.dispose();
    _animationController.dispose();
    super.dispose();
  }

  /// Charge la liste des localités depuis l'API
  Future<void> _loadLocalites() async {
    setState(() {
      _isLoadingLocalites = true;
    });

    try {
      final response = await http.get(
        Uri.parse('${ApiConfig.baseUrl}/local/get_localites.php'),
      );

      if (response.statusCode == 200) {
        print('Réponse brute de l\'API: ${response.body}');
        final dynamic jsonResponse = json.decode(response.body);
        print('Réponse décodée: $jsonResponse');
        print('Type de la réponse: ${jsonResponse.runtimeType}');

        List<dynamic> data;

        // Vérifier si la réponse est directement un tableau ou un objet contenant un tableau
        if (jsonResponse is List) {
          data = jsonResponse;
        } else if (jsonResponse is Map<String, dynamic>) {
          // Chercher le tableau dans l'objet (clés possibles: 'data', 'localites', 'results', etc.)
          if (jsonResponse.containsKey('data')) {
            data = jsonResponse['data'] as List<dynamic>;
          } else if (jsonResponse.containsKey('localites')) {
            data = jsonResponse['localites'] as List<dynamic>;
          } else if (jsonResponse.containsKey('results')) {
            data = jsonResponse['results'] as List<dynamic>;
          } else {
            // Si aucune clé connue, prendre la première valeur qui est une liste
            final listValue = jsonResponse.values.firstWhere(
              (value) => value is List,
              orElse: () => [],
            );
            data = listValue as List<dynamic>;
          }
        } else {
          throw Exception(
            'Format de réponse inattendu: ${jsonResponse.runtimeType}',
          );
        }

        print('Données extraites: $data');

        if (data.isEmpty) {
          throw Exception('Aucune localité trouvée');
        }

        // Traiter et dédupliquer les localités
        final processedLocalites = <Map<String, dynamic>>[];
        final seenNames = <String>{};

        for (final item in data) {
          // Essayer différentes clés possibles pour l'ID
          final id =
              (item['id'] ?? item['id_local'] ?? item['localite_id'] ?? '')
                  .toString();

          // Essayer différentes clés possibles pour le nom
          final nom =
              (item['nom'] ??
                      item['nom_local'] ??
                      item['name'] ??
                      item['localite_nom'] ??
                      '')
                  .toString()
                  .trim();

          print('🔍 Traitement: ID="$id", Nom="$nom" depuis $item');

          // Ignorer les éléments sans nom ou avec nom vide
          if (nom.isEmpty) {
            print('⚠️ Localité ignorée (nom vide): $item');
            continue;
          }

          // Exclure spécifiquement 'Agence DALOA'
          if (nom == 'Agence DALOA') {
            print('⚠️ Localité exclue: $nom');
            continue;
          }

          // Éviter les doublons de noms
          if (seenNames.contains(nom)) {
            print('⚠️ Localité dupliquée ignorée: $nom');
            continue;
          }

          seenNames.add(nom);
          processedLocalites.add({'id': id, 'nom': nom});
          print(' Localité ajoutée: ID="$id", Nom="$nom"');
        }

        print(' Localités après déduplication: $processedLocalites');

        setState(() {
          _localites = processedLocalites;
          _isLoadingLocalites = false;

          // Vérifier si la localité sélectionnée existe toujours
          if (_selectedLocalite != null &&
              !_localites.any(
                (localite) => localite['nom'] == _selectedLocalite,
              )) {
            print(
              '⚠️ Localité sélectionnée "$_selectedLocalite" n\'existe plus, réinitialisation',
            );
            _selectedLocalite = null;
          }
        });

        print(
          'Localités traitées: $_localites (${_localites.length} éléments)',
        );
      } else {
        throw Exception('Erreur HTTP ${response.statusCode}: ${response.body}');
      }
    } catch (e) {
      setState(() {
        _isLoadingLocalites = false;
      });
      print('Erreur lors du chargement des localités: $e');
      print('Stack trace: ${StackTrace.current}');

      // Afficher un message d'erreur à l'utilisateur
      if (mounted) {
        String errorMessage = 'Erreur lors du chargement des localités';

        if (e.toString().contains('SocketException')) {
          errorMessage = 'Problème de connexion réseau';
        } else if (e.toString().contains('TimeoutException')) {
          errorMessage = 'Délai d\'attente dépassé';
        } else if (e.toString().contains('FormatException')) {
          errorMessage = 'Format de données invalide';
        }

        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(errorMessage),
            backgroundColor: Colors.red,
            action: SnackBarAction(
              label: 'Réessayer',
              textColor: Colors.white,
              onPressed: () {
                _loadLocalites(); // Réessayer le chargement
              },
            ),
          ),
        );
      }
    }
  }

  bool _validateForm() {
    if (_formKey.currentState!.validate()) {
      if (_selectedLocalite == null || _selectedLocalite!.isEmpty) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: const Text('Veuillez sélectionner votre localité'),
            backgroundColor: AppTheme.color.redColor,
            behavior: SnackBarBehavior.floating,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(10),
            ),
            margin: const EdgeInsets.all(16),
          ),
        );
        return false;
      }
      if (!_acceptTerms) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: const Text(
              'Veuillez accepter les conditions d\'utilisation',
            ),
            backgroundColor: AppTheme.color.redColor,
            behavior: SnackBarBehavior.floating,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(10),
            ),
            margin: const EdgeInsets.all(16),
          ),
        );
        return false;
      }
      return true;
    }
    return false;
  }

  void _register() async {
    if (!_validateForm()) {
      return;
    }

    setState(() {
      _isLoading = true;
    });

    try {
      print('Données d\'inscription:');
      print('Nom: ${_nomController.text.trim()}');
      print('Prénom: ${_prenomController.text.trim()}');
      print('Téléphone: ${_telephoneController.text.trim()}');
      print('Localité sélectionnée: $_selectedLocalite');
      print('Sexe: $_sexe');

      // Trouver l'ID de la localité sélectionnée
      final selectedLocaliteData = _localites.firstWhere(
        (localite) => localite['nom'] == _selectedLocalite,
        orElse: () => {'id': '1', 'nom': 'Défaut'},
      );

      final result = await AuthService.registerClient(
        nom: _nomController.text.trim(),
        prenom: _prenomController.text.trim(),
        telephone: _telephoneController.text.trim(),
        localId: selectedLocaliteData['id']!,
        zoneId: '1', // Valeur par défaut pour la zone
        domicile: _selectedLocalite ?? 'Non spécifié',
        sexe: _sexe,
      );

      setState(() {
        _isLoading = false;
      });

      if (result['success'] == true) {
        if (mounted) {
          await _showSuccessDialog(result['code_client'] ?? 'N/A');
        }
      } else {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(
                result['message'] ?? 'Erreur lors de l\'inscription',
              ),
              backgroundColor: AppTheme.color.redColor,
              duration: const Duration(seconds: 5),
            ),
          );
        }
      }
    } catch (e) {
      setState(() {
        _isLoading = false;
      });
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Erreur: ${e.toString()}'),
            backgroundColor: AppTheme.color.redColor,
            duration: const Duration(seconds: 5),
          ),
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.white,
      appBar: AppBar(
        backgroundColor: Colors.transparent,
        elevation: 0,
        leading: IconButton(
          icon: Icon(
            Icons.arrow_back_ios_new_rounded,
            color: AppTheme.color.primaryColor,
            size: 20,
          ),
          onPressed: () => Navigator.of(context).pop(),
        ),
        title: Text(
          'Inscription',
          style: TextStyle(
            color: AppTheme.color.primaryColor,
            fontSize: 18,
            fontWeight: FontWeight.w700,
          ),
        ),
        centerTitle: true,
        systemOverlayStyle: SystemUiOverlayStyle.dark,
      ),
      body: SafeArea(
        child: FadeTransition(
          opacity: _fadeAnimation,
          child: SlideTransition(
            position: _slideAnimation,
            child: Form(
              key: _formKey,
              child: SingleChildScrollView(
                physics: const BouncingScrollPhysics(),
                padding: const EdgeInsets.symmetric(
                  horizontal: 24,
                  vertical: 16,
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // Titre et description
                    Text(
                      'Créer un compte',
                      style: TextStyle(
                        color: AppTheme.color.primaryColor,
                        fontSize: 28,
                        fontWeight: FontWeight.w800,
                        letterSpacing: -0.5,
                      ),
                    ),
                    const SizedBox(height: 8),
                    Text(
                      'Remplissez vos informations pour créer votre compte',
                      style: TextStyle(
                        color: AppTheme.color.brunGris,
                        fontSize: 16,
                        height: 1.4,
                        letterSpacing: -0.2,
                      ),
                    ),
                    const SizedBox(height: 32),

                    // Champs du formulaire
                    _buildFormFields(),

                    const SizedBox(height: 24),

                    // Conditions d'utilisation
                    _buildTermsCheckbox(),

                    const SizedBox(height: 32),

                    // Bouton d'inscription
                    _buildRegisterButton(),

                    const SizedBox(height: 16),
                  ],
                ),
              ),
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildFormFields() {
    return Column(
      children: [
        // Nom
        _buildInputField(
          controller: _nomController,
          label: 'Nom',
          icon: Icons.person_outline_rounded,
          validator: (value) {
            if (value == null || value.isEmpty) {
              return 'Veuillez entrer votre nom';
            }
            return null;
          },
        ),
        const SizedBox(height: 16),

        // Prénom
        _buildInputField(
          controller: _prenomController,
          label: 'Prénom',
          icon: Icons.person_outline_rounded,
          validator: (value) {
            if (value == null || value.isEmpty) {
              return 'Veuillez entrer votre prénom';
            }
            return null;
          },
        ),
        const SizedBox(height: 16),

        // Téléphone
        _buildInputField(
          controller: _telephoneController,
          label: 'Numéro de téléphone',
          icon: Icons.phone_outlined,
          keyboardType: TextInputType.phone,
          validator: (value) {
            if (value == null || value.isEmpty) {
              return 'Veuillez entrer votre numéro de téléphone';
            }
            return null;
          },
        ),
        const SizedBox(height: 16),

        // Sexe
        _buildDropdownField(
          value: _sexe,
          label: 'Sexe',
          icon: Icons.wc_outlined,
          items: ['Homme', 'Femme'],
          onChanged: (String? newValue) {
            if (newValue != null) {
              setState(() {
                _sexe = newValue;
              });
            }
          },
        ),
        const SizedBox(height: 16),

        // Ville/Localité
        _buildLocaliteDropdown(),
      ],
    );
  }

  Widget _buildTermsCheckbox() {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        SizedBox(
          width: 24,
          height: 24,
          child: Checkbox(
            value: _acceptTerms,
            onChanged: (value) {
              setState(() {
                _acceptTerms = value ?? false;
              });
            },
            activeColor: AppTheme.color.primaryColor,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(4),
            ),
          ),
        ),
        const SizedBox(width: 12),
        Expanded(child: _buildTermsAndPrivacyText()),
      ],
    );
  }

  Widget _buildRegisterButton() {
    return Container(
      height: 60,
      width: double.infinity,
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [
            AppTheme.color.primaryColor,
            Color(0xFF0052CC), // Version plus foncée
          ],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: AppTheme.color.primaryColor.withOpacity(0.3),
            blurRadius: 15,
            offset: const Offset(0, 8),
            spreadRadius: 0,
          ),
        ],
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          onTap: _isLoading ? null : _register,
          borderRadius: BorderRadius.circular(16),
          splashColor: Colors.white.withOpacity(0.1),
          highlightColor: Colors.white.withOpacity(0.05),
          child: Ink(
            child: Center(
              child:
                  _isLoading
                      ? SizedBox(
                        width: 24,
                        height: 24,
                        child: CircularProgressIndicator(
                          strokeWidth: 2.5,
                          valueColor: AlwaysStoppedAnimation<Color>(
                            Colors.white,
                          ),
                        ),
                      )
                      : Text(
                        'S\'inscrire',
                        style: const TextStyle(
                          color: Colors.white,
                          fontSize: 17,
                          fontWeight: FontWeight.w700,
                          letterSpacing: 0.3,
                        ),
                      ),
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildInputField({
    required TextEditingController controller,
    required String label,
    required IconData icon,
    TextInputType? keyboardType,
    bool obscureText = false,
    Widget? suffixIcon,
    String? Function(String?)? validator,
  }) {
    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.04),
            blurRadius: 12,
            offset: const Offset(0, 4),
            spreadRadius: 0,
          ),
        ],
      ),
      child: TextFormField(
        controller: controller,
        obscureText: obscureText,
        keyboardType: keyboardType,
        style: const TextStyle(
          fontSize: 16,
          fontWeight: FontWeight.w500,
          color: Color(0xFF2E3A59),
          letterSpacing: -0.2,
        ),
        decoration: InputDecoration(
          labelText: label,
          labelStyle: TextStyle(
            color: AppTheme.color.brunGris,
            fontSize: 15,
            fontWeight: FontWeight.w500,
          ),
          prefixIcon: Padding(
            padding: const EdgeInsets.only(left: 16, right: 12),
            child: Icon(icon, color: AppTheme.color.primaryColor, size: 22),
          ),
          prefixIconConstraints: const BoxConstraints(minWidth: 50),
          suffixIcon:
              suffixIcon != null
                  ? Padding(
                    padding: const EdgeInsets.only(right: 12),
                    child: suffixIcon,
                  )
                  : null,
          border: OutlineInputBorder(
            borderRadius: BorderRadius.circular(16),
            borderSide: BorderSide.none,
          ),
          enabledBorder: OutlineInputBorder(
            borderRadius: BorderRadius.circular(16),
            borderSide: BorderSide(color: Colors.grey.shade200, width: 1.5),
          ),
          focusedBorder: OutlineInputBorder(
            borderRadius: BorderRadius.circular(16),
            borderSide: BorderSide(
              color: AppTheme.color.primaryColor,
              width: 1.5,
            ),
          ),
          errorBorder: OutlineInputBorder(
            borderRadius: BorderRadius.circular(16),
            borderSide: BorderSide(color: AppTheme.color.redColor, width: 1.5),
          ),
          focusedErrorBorder: OutlineInputBorder(
            borderRadius: BorderRadius.circular(16),
            borderSide: BorderSide(color: AppTheme.color.redColor, width: 1.5),
          ),
          contentPadding: const EdgeInsets.symmetric(
            horizontal: 20,
            vertical: 18,
          ),
          filled: true,
          fillColor: Colors.white,
          errorStyle: TextStyle(
            color: AppTheme.color.redColor,
            fontSize: 12,
            fontWeight: FontWeight.w500,
          ),
        ),
        validator: validator,
      ),
    );
  }

  Widget _buildDropdownField({
    required String value,
    required String label,
    required IconData icon,
    required List<String> items,
    required void Function(String?) onChanged,
  }) {
    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.04),
            blurRadius: 12,
            offset: const Offset(0, 4),
            spreadRadius: 0,
          ),
        ],
      ),
      child: DropdownButtonFormField<String>(
        value: value,
        onChanged: onChanged,
        icon: Icon(
          Icons.keyboard_arrow_down_rounded,
          color: AppTheme.color.primaryColor,
          size: 24,
        ),
        style: const TextStyle(
          fontSize: 16,
          fontWeight: FontWeight.w500,
          color: Color(0xFF2E3A59),
          letterSpacing: -0.2,
        ),
        decoration: InputDecoration(
          labelText: label,
          labelStyle: TextStyle(
            color: AppTheme.color.brunGris,
            fontSize: 15,
            fontWeight: FontWeight.w500,
          ),
          prefixIcon: Padding(
            padding: const EdgeInsets.only(left: 16, right: 12),
            child: Icon(icon, color: AppTheme.color.primaryColor, size: 22),
          ),
          prefixIconConstraints: const BoxConstraints(minWidth: 50),
          border: OutlineInputBorder(
            borderRadius: BorderRadius.circular(16),
            borderSide: BorderSide.none,
          ),
          enabledBorder: OutlineInputBorder(
            borderRadius: BorderRadius.circular(16),
            borderSide: BorderSide(color: Colors.grey.shade200, width: 1.5),
          ),
          focusedBorder: OutlineInputBorder(
            borderRadius: BorderRadius.circular(16),
            borderSide: BorderSide(
              color: AppTheme.color.primaryColor,
              width: 1.5,
            ),
          ),
          contentPadding: const EdgeInsets.symmetric(
            horizontal: 20,
            vertical: 18,
          ),
          filled: true,
          fillColor: Colors.white,
        ),
        dropdownColor: Colors.white,
        borderRadius: BorderRadius.circular(16),
        items:
            items.map<DropdownMenuItem<String>>((String value) {
              return DropdownMenuItem<String>(
                value: value,
                child: Text(
                  value,
                  style: const TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.w500,
                    color: Color(0xFF2E3A59),
                  ),
                ),
              );
            }).toList(),
      ),
    );
  }

  Widget _buildTermsAndPrivacyText() {
    return RichText(
      text: TextSpan(
        style: TextStyle(
          color: AppTheme.color.brunGris,
          fontSize: 14,
          height: 1.4,
        ),
        children: [
          const TextSpan(text: 'J\'accepte la '),
          WidgetSpan(
            child: GestureDetector(
              onTap: () {
                // Naviguer vers les conditions d'utilisation
                // Pour l'instant, on utilise la même page que la politique de confidentialité
                Navigator.push(
                  context,
                  MaterialPageRoute(
                    builder: (context) => const PrivacyPolicyScreen(),
                  ),
                );
              },
              child: Text(
                'politique de confidentialité',
                style: TextStyle(
                  color: AppTheme.color.primaryColor,
                  fontSize: 14,
                  fontWeight: FontWeight.w600,
                  decoration: TextDecoration.underline,
                  decorationColor: AppTheme.color.primaryColor,
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  // Méthode pour afficher un dialogue de succès après l'inscription
  Future<void> _showSuccessDialog(String codeClient) async {
    return showDialog<void>(
      context: context,
      barrierDismissible: false,
      builder: (BuildContext context) {
        return Dialog(
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(20),
          ),
          child: Padding(
            padding: const EdgeInsets.all(24.0),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                Container(
                  padding: const EdgeInsets.all(16),
                  decoration: BoxDecoration(
                    color: Colors.green.withOpacity(0.1),
                    shape: BoxShape.circle,
                  ),
                  child: const Icon(
                    Icons.check_circle,
                    color: Colors.green,
                    size: 60,
                  ),
                ),
                const SizedBox(height: 24),
                const Text(
                  'Inscription réussie !',
                  style: TextStyle(fontSize: 20, fontWeight: FontWeight.bold),
                ),
                const SizedBox(height: 16),
                Text(
                  'Votre compte a été créé avec succès. Veillez vous connecter',
                  textAlign: TextAlign.center,
                  style: TextStyle(
                    fontSize: 16,
                    color: AppTheme.color.brunGris,
                  ),
                ),
                const SizedBox(height: 16),
                Container(
                  padding: const EdgeInsets.symmetric(
                    horizontal: 24,
                    vertical: 12,
                  ),
                  decoration: BoxDecoration(
                    color: AppTheme.color.primaryColor.withOpacity(0.1),
                    borderRadius: BorderRadius.circular(12),
                    border: Border.all(
                      color: AppTheme.color.primaryColor.withOpacity(0.3),
                      width: 1,
                    ),
                  ),
                  child: Text(
                    codeClient,
                    style: TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                      color: AppTheme.color.primaryColor,
                      letterSpacing: 1,
                    ),
                  ),
                ),
                const SizedBox(height: 24),
                SizedBox(
                  width: double.infinity,
                  height: 50,
                  child: ElevatedButton(
                    onPressed: () {
                      Navigator.of(context).pushAndRemoveUntil(
                        MaterialPageRoute(
                          builder: (context) => const LoginScreen(),
                        ),
                        (route) => false,
                      );
                    },
                    style: ElevatedButton.styleFrom(
                      backgroundColor: AppTheme.color.primaryColor,
                      foregroundColor: Colors.white,
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(12),
                      ),
                      elevation: 2,
                    ),
                    child: const Text(
                      'Se connecter',
                      style: TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  /// Construit le champ de sélection des localités
  Widget _buildLocaliteDropdown() {
    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.04),
            blurRadius: 12,
            offset: const Offset(0, 4),
            spreadRadius: 0,
          ),
        ],
      ),
      child: DropdownButtonFormField<String>(
        value:
            _selectedLocalite != null &&
                    _localites.any(
                      (localite) => localite['nom'] == _selectedLocalite,
                    )
                ? _selectedLocalite
                : null, // Réinitialiser si la valeur n'existe pas dans la liste
        onChanged:
            _isLoadingLocalites
                ? null
                : (String? newValue) {
                  setState(() {
                    _selectedLocalite = newValue;
                  });
                },
        validator: (value) {
          if (value == null || value.isEmpty) {
            return 'Veuillez sélectionner votre localité';
          }
          return null;
        },
        icon:
            _isLoadingLocalites
                ? SizedBox(
                  width: 20,
                  height: 20,
                  child: CircularProgressIndicator(
                    strokeWidth: 2,
                    valueColor: AlwaysStoppedAnimation<Color>(
                      AppTheme.color.primaryColor,
                    ),
                  ),
                )
                : Icon(
                  Icons.keyboard_arrow_down_rounded,
                  color: AppTheme.color.primaryColor,
                  size: 24,
                ),
        style: const TextStyle(
          fontSize: 16,
          fontWeight: FontWeight.w500,
          color: Color(0xFF2E3A59),
          letterSpacing: -0.2,
        ),
        decoration: InputDecoration(
          labelText: 'Ville/Localité',
          labelStyle: TextStyle(
            color: AppTheme.color.brunGris,
            fontSize: 15,
            fontWeight: FontWeight.w500,
          ),
          prefixIcon: Padding(
            padding: const EdgeInsets.only(left: 16, right: 12),
            child: Icon(
              Icons.location_city_outlined,
              color: AppTheme.color.primaryColor,
              size: 22,
            ),
          ),
          prefixIconConstraints: const BoxConstraints(minWidth: 50),
          border: OutlineInputBorder(
            borderRadius: BorderRadius.circular(16),
            borderSide: BorderSide.none,
          ),
          enabledBorder: OutlineInputBorder(
            borderRadius: BorderRadius.circular(16),
            borderSide: BorderSide(color: Colors.grey.shade200, width: 1.5),
          ),
          focusedBorder: OutlineInputBorder(
            borderRadius: BorderRadius.circular(16),
            borderSide: BorderSide(
              color: AppTheme.color.primaryColor,
              width: 1.5,
            ),
          ),
          errorBorder: OutlineInputBorder(
            borderRadius: BorderRadius.circular(16),
            borderSide: BorderSide(color: AppTheme.color.redColor, width: 1.5),
          ),
          focusedErrorBorder: OutlineInputBorder(
            borderRadius: BorderRadius.circular(16),
            borderSide: BorderSide(color: AppTheme.color.redColor, width: 1.5),
          ),
          contentPadding: const EdgeInsets.symmetric(
            horizontal: 20,
            vertical: 18,
          ),
          filled: true,
          fillColor: Colors.white,
          errorStyle: TextStyle(
            color: AppTheme.color.redColor,
            fontSize: 12,
            fontWeight: FontWeight.w500,
          ),
        ),
        dropdownColor: Colors.white,
        borderRadius: BorderRadius.circular(16),
        hint: Text(
          _isLoadingLocalites ? 'Chargement...' : 'Sélectionnez votre localité',
          style: TextStyle(
            color: AppTheme.color.brunGris,
            fontSize: 16,
            fontWeight: FontWeight.w500,
          ),
        ),
        items:
            _localites
                .where(
                  (localite) =>
                      localite['nom'] != null &&
                      localite['nom'].toString().trim().isNotEmpty,
                )
                .map<DropdownMenuItem<String>>((Map<String, dynamic> localite) {
                  final nom = localite['nom'].toString().trim();
                  return DropdownMenuItem<String>(
                    value: nom,
                    child: Text(
                      nom,
                      style: const TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.w500,
                        color: Color(0xFF2E3A59),
                      ),
                    ),
                  );
                })
                .toList(),
      ),
    );
  }
}
