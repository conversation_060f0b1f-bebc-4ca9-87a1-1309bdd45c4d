import 'package:flutter/material.dart';
import 'package:callitris/services/image_service.dart';
import 'package:callitris/config/api_config.dart';

/// Widget d'image optimisé spécifiquement pour les produits
class OptimizedProductImage extends StatelessWidget {
  final String imageUrl;
  final double? width;
  final double? height;
  final BoxFit fit;
  final BorderRadius? borderRadius;
  final bool showPlaceholder;
  final String sizeCategory;
  final VoidCallback? onTap;

  const OptimizedProductImage({
    super.key,
    required this.imageUrl,
    this.width,
    this.height,
    this.fit = BoxFit.cover,
    this.borderRadius,
    this.showPlaceholder = true,
    this.sizeCategory = 'medium',
    this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    final processedImageUrl = _processImageUrl(imageUrl);
    
    Widget imageWidget = ClipRRect(
      borderRadius: borderRadius ?? BorderRadius.circular(8),
      child: ImageService.instance.optimizedImage(
        imageUrl: processedImageUrl,
        width: width,
        height: height,
        fit: fit,
        sizeCategory: sizeCategory,
        placeholder: showPlaceholder ? 'assets/images/placeholder.jpg' : null,
        errorImage: 'assets/images/placeholder.jpg',
      ),
    );

    if (onTap != null) {
      imageWidget = GestureDetector(
        onTap: onTap,
        child: imageWidget,
      );
    }

    return imageWidget;
  }

  /// Traite l'URL de l'image pour s'assurer qu'elle est correcte
  String _processImageUrl(String url) {
    if (url.isEmpty) {
      return 'assets/images/placeholder.jpg';
    }

    // Si c'est déjà une URL complète, la retourner telle quelle
    if (url.startsWith('http://') || url.startsWith('https://')) {
      return url;
    }

    // Si c'est un chemin relatif, construire l'URL complète
    if (url.startsWith('/')) {
      return '${ApiConfig.baseUrl}$url';
    }

    // Si c'est un chemin relatif sans slash initial
    return '${ApiConfig.baseUrl}/$url';
  }
}

/// Widget d'image de produit avec effet de survol pour les cartes
class ProductCardImage extends StatefulWidget {
  final String imageUrl;
  final double? width;
  final double? height;
  final VoidCallback? onTap;

  const ProductCardImage({
    super.key,
    required this.imageUrl,
    this.width,
    this.height,
    this.onTap,
  });

  @override
  State<ProductCardImage> createState() => _ProductCardImageState();
}

class _ProductCardImageState extends State<ProductCardImage>
    with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _scaleAnimation;
  bool _isHovered = false;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 200),
      vsync: this,
    );
    _scaleAnimation = Tween<double>(
      begin: 1.0,
      end: 1.05,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    ));
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return MouseRegion(
      onEnter: (_) => _onHover(true),
      onExit: (_) => _onHover(false),
      child: GestureDetector(
        onTap: widget.onTap,
        onTapDown: (_) => _onHover(true),
        onTapUp: (_) => _onHover(false),
        onTapCancel: () => _onHover(false),
        child: AnimatedBuilder(
          animation: _scaleAnimation,
          builder: (context, child) {
            return Transform.scale(
              scale: _scaleAnimation.value,
              child: OptimizedProductImage(
                imageUrl: widget.imageUrl,
                width: widget.width,
                height: widget.height,
                sizeCategory: 'small',
                borderRadius: BorderRadius.circular(12),
              ),
            );
          },
        ),
      ),
    );
  }

  void _onHover(bool isHovered) {
    if (_isHovered != isHovered) {
      setState(() {
        _isHovered = isHovered;
      });

      if (isHovered) {
        _animationController.forward();
      } else {
        _animationController.reverse();
      }
    }
  }
}

/// Widget d'image de produit pour les listes avec lazy loading
class LazyProductImage extends StatelessWidget {
  final String imageUrl;
  final double? width;
  final double? height;
  final BoxFit fit;
  final VoidCallback? onTap;

  const LazyProductImage({
    super.key,
    required this.imageUrl,
    this.width,
    this.height,
    this.fit = BoxFit.cover,
    this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    return OptimizedProductImage(
      imageUrl: imageUrl,
      width: width,
      height: height,
      fit: fit,
      sizeCategory: 'small',
      onTap: onTap,
    );
  }
}

/// Widget d'image de produit pour les détails (haute qualité)
class DetailProductImage extends StatelessWidget {
  final String imageUrl;
  final double? width;
  final double? height;
  final VoidCallback? onTap;

  const DetailProductImage({
    super.key,
    required this.imageUrl,
    this.width,
    this.height,
    this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    return Hero(
      tag: 'product_image_$imageUrl',
      child: OptimizedProductImage(
        imageUrl: imageUrl,
        width: width,
        height: height,
        sizeCategory: 'large',
        borderRadius: BorderRadius.circular(16),
        onTap: onTap,
      ),
    );
  }
}

/// Widget d'image miniature pour les aperçus
class ThumbnailProductImage extends StatelessWidget {
  final String imageUrl;
  final double size;
  final VoidCallback? onTap;

  const ThumbnailProductImage({
    super.key,
    required this.imageUrl,
    this.size = 60,
    this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    return OptimizedProductImage(
      imageUrl: imageUrl,
      width: size,
      height: size,
      sizeCategory: 'thumbnail',
      borderRadius: BorderRadius.circular(8),
      onTap: onTap,
    );
  }
}

/// Galerie d'images de produit avec préchargement
class ProductImageGallery extends StatefulWidget {
  final List<String> imageUrls;
  final double height;
  final Function(int index)? onImageTap;

  const ProductImageGallery({
    super.key,
    required this.imageUrls,
    this.height = 300,
    this.onImageTap,
  });

  @override
  State<ProductImageGallery> createState() => _ProductImageGalleryState();
}

class _ProductImageGalleryState extends State<ProductImageGallery> {
  late PageController _pageController;
  int _currentIndex = 0;

  @override
  void initState() {
    super.initState();
    _pageController = PageController();
    _preloadImages();
  }

  @override
  void dispose() {
    _pageController.dispose();
    super.dispose();
  }

  void _preloadImages() {
    // Précharger les images en arrière-plan
    ImageService.instance.preloadImages(
      widget.imageUrls,
      sizeCategory: 'large',
      concurrency: 2,
    );
  }

  @override
  Widget build(BuildContext context) {
    if (widget.imageUrls.isEmpty) {
      return SizedBox(
        height: widget.height,
        child: const Center(
          child: Icon(
            Icons.image_not_supported_outlined,
            size: 64,
            color: Colors.grey,
          ),
        ),
      );
    }

    return Column(
      children: [
        SizedBox(
          height: widget.height,
          child: PageView.builder(
            controller: _pageController,
            onPageChanged: (index) {
              setState(() {
                _currentIndex = index;
              });
            },
            itemCount: widget.imageUrls.length,
            itemBuilder: (context, index) {
              return GestureDetector(
                onTap: () => widget.onImageTap?.call(index),
                child: DetailProductImage(
                  imageUrl: widget.imageUrls[index],
                  width: double.infinity,
                  height: widget.height,
                ),
              );
            },
          ),
        ),
        if (widget.imageUrls.length > 1) ...[
          const SizedBox(height: 16),
          Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: List.generate(
              widget.imageUrls.length,
              (index) => GestureDetector(
                onTap: () {
                  _pageController.animateToPage(
                    index,
                    duration: const Duration(milliseconds: 300),
                    curve: Curves.easeInOut,
                  );
                },
                child: Container(
                  margin: const EdgeInsets.symmetric(horizontal: 4),
                  width: 8,
                  height: 8,
                  decoration: BoxDecoration(
                    shape: BoxShape.circle,
                    color: _currentIndex == index
                        ? Theme.of(context).primaryColor
                        : Colors.grey[300],
                  ),
                ),
              ),
            ),
          ),
        ],
      ],
    );
  }
}
