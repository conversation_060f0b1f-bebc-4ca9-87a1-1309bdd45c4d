# Guide de Débogage - Problème d'Affichage des Publicités

## 🔍 Problème Identifié
Les données des promos et publicités ne s'affichent pas dans l'application.

## ✅ Améliorations Apportées

### 1. Amélioration de la Fonction `_loadPromoProducts()`
- **Logs détaillés** : Ajout de logs avec emojis pour suivre chaque étape
- **Gestion d'erreurs robuste** : Vérification de chaque étape du processus
- **Validation des données** : Contrôle de la présence des champs requis
- **Timeout** : Ajout d'un timeout de 15 secondes pour éviter les blocages
- **Données de fallback** : Utilisation de données de test en cas d'échec

### 2. Fonction de Test `_loadTestPromoData()`
- Données de test avec des images Unsplash valides
- Permet de vérifier que l'affichage fonctionne même si l'API échoue
- Facilite le débogage de l'interface utilisateur

### 3. Interface Utilisateur Améliorée
- **Section vide améliorée** : Message plus informatif quand aucune publicité n'est disponible
- **Bouton de rechargement** : Permet de réessayer le chargement manuellement
- **Messages d'aide** : Instructions pour l'utilisateur

## 🔧 Comment Déboguer

### Étape 1: Vérifier les Logs
Ouvrez la console Flutter et recherchez ces messages :
```
🔄 Chargement des publicités depuis: [URL]
📡 Statut de la réponse: [CODE]
📄 Corps de la réponse: [DONNÉES]
📊 Nombre de publicités reçues: [NOMBRE]
✅ [X] publicités traitées avec succès
```

### Étape 2: Problèmes Courants et Solutions

#### ❌ Token manquant
```
❌ Token manquant pour récupérer les publicités
```
**Solution** : Vérifiez que l'utilisateur est bien connecté et que le token est valide.

#### ❌ Erreur HTTP 401
```
❌ Erreur HTTP: 401
```
**Solution** : Le token d'authentification est invalide ou expiré.

#### ❌ Réponse vide
```
⚠️ Réponse vide de l'API
```
**Solution** : L'API ne retourne aucune donnée. Vérifiez la base de données.

#### ❌ Erreur de parsing JSON
```
❌ Erreur de parsing JSON: [ERREUR]
```
**Solution** : La réponse de l'API n'est pas un JSON valide.

### Étape 3: Tests Manuels

#### Test de l'API
Exécutez le script de test :
```bash
dart test_promo_api.dart
```

#### Test dans l'App
1. Ouvrez l'application
2. Allez sur l'écran d'accueil
3. Tirez vers le bas pour rafraîchir
4. Vérifiez les logs dans la console

## 🛠️ Configuration API

### URLs Utilisées
- **API Base** : `https://api.callitris-distribution.com/client-api.callitris-distribution.com`
- **Endpoint** : `/pubs/getPub.php`
- **Images Base** : `https://api.callitris-distribution.com/app.callititris-distribution.com`

### Structure Attendue de la Réponse
```json
[
  {
    "id_publicite": "1",
    "name": "Nom de la publicité",
    "description": "Description",
    "price": 25000,
    "imageUrl": "chemin/vers/image.jpg",
    "category": "Catégorie",
    "isPromo": true
  }
]
```

## 🔄 Actions de Récupération

### Si l'API échoue
1. **Données de test** : L'app charge automatiquement des données de test
2. **Bouton réessayer** : L'utilisateur peut relancer le chargement
3. **Pull-to-refresh** : Rafraîchissement manuel possible

### Si les images ne s'affichent pas
1. Vérifiez les URLs générées dans les logs
2. Testez les URLs dans un navigateur
3. Vérifiez la configuration `ApiConfig.baseUrl2`

## 📱 Interface Utilisateur

### États d'Affichage
1. **Chargement** : Skeleton loader pendant la récupération
2. **Données présentes** : Carrousel avec les publicités
3. **Aucune donnée** : Message informatif avec bouton de rechargement
4. **Erreur** : Données de test affichées automatiquement

## 🎯 Prochaines Étapes

1. **Tester l'application** avec les nouvelles améliorations
2. **Vérifier les logs** pour identifier le problème exact
3. **Corriger l'API** si nécessaire (token, données, format)
4. **Optimiser les performances** une fois le problème résolu

## 📞 Support

Si le problème persiste après ces améliorations :
1. Partagez les logs complets de la console
2. Testez l'API avec le script de test fourni
3. Vérifiez la connectivité réseau
4. Contrôlez la validité du token d'authentification
