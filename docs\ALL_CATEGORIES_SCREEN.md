# Page Toutes les Catégories

## Vue d'ensemble

Ce document détaille l'implémentation de la page `AllCategoriesScreen` qui permet d'afficher toutes les catégories disponibles dans l'application avec une fonctionnalité de recherche.

## Fonctionnalités

### 1. **Affichage Complet des Catégories**
- Récupération de toutes les catégories sans limitation
- Affichage en liste avec images et informations
- Compteur de produits par catégorie

### 2. **Recherche en Temps Réel**
- Barre de recherche en haut de l'écran
- Filtrage instantané par nom de catégorie
- Bouton de suppression pour vider la recherche

### 3. **Navigation Intuitive**
- Tap sur une catégorie pour voir ses produits
- Retour facile vers l'écran précédent
- Animations fluides

### 4. **États de l'Interface**
- État de chargement avec indicateur
- État vide avec message approprié
- État de recherche sans résultats

## Structure du Code

### Fichier Principal
```
lib/screens/catalogue/all_categories_screen.dart
```

### Dépendances
- `CatalogueService` : Service pour récupérer les catégories
- `CategoryProductsScreen` : Navigation vers les produits d'une catégorie
- `AppTheme` : Thème de l'application

## Implémentation

### 1. **Classe Principale**
```dart
class AllCategoriesScreen extends StatefulWidget {
  const AllCategoriesScreen({super.key});

  @override
  State<AllCategoriesScreen> createState() => _AllCategoriesScreenState();
}
```

### 2. **Variables d'État**
```dart
class _AllCategoriesScreenState extends State<AllCategoriesScreen>
    with SingleTickerProviderStateMixin {
  List<Map<String, dynamic>> _categories = [];
  bool _isLoading = true;
  String _searchQuery = '';
  final TextEditingController _searchController = TextEditingController();
  late AnimationController _animationController;
  late Animation<double> _fadeAnimation;
}
```

### 3. **Chargement des Données**
```dart
Future<void> _loadAllCategories() async {
  try {
    setState(() {
      _isLoading = true;
    });

    // Récupérer toutes les catégories (sans limite)
    final categories = await CatalogueService.getCategories();
    
    setState(() {
      _categories = categories;
      _isLoading = false;
    });
  } catch (e) {
    print('Erreur lors du chargement des catégories: $e');
    setState(() {
      _isLoading = false;
    });
  }
}
```

### 4. **Filtrage des Catégories**
```dart
List<Map<String, dynamic>> get _filteredCategories {
  if (_searchQuery.isEmpty) {
    return _categories;
  }
  return _categories.where((category) {
    final name = category['nom']?.toString().toLowerCase() ?? '';
    return name.contains(_searchQuery.toLowerCase());
  }).toList();
}
```

## Interface Utilisateur

### 1. **AppBar**
- Titre "Toutes les catégories"
- Bouton de retour avec icône iOS
- Couleurs cohérentes avec le thème

### 2. **Barre de Recherche**
```dart
TextField(
  controller: _searchController,
  onChanged: (value) {
    setState(() {
      _searchQuery = value;
    });
  },
  decoration: InputDecoration(
    hintText: 'Rechercher une catégorie...',
    prefixIcon: Icon(Icons.search),
    suffixIcon: _searchQuery.isNotEmpty ? IconButton(...) : null,
    filled: true,
    fillColor: Colors.grey.shade100,
    border: OutlineInputBorder(...),
  ),
)
```

### 3. **Liste des Catégories**
```dart
ListView.builder(
  padding: const EdgeInsets.all(16),
  itemCount: _filteredCategories.length,
  itemBuilder: (context, index) {
    final category = _filteredCategories[index];
    return _buildCategoryCard(category, index);
  },
)
```

### 4. **Carte de Catégorie**
```dart
Widget _buildCategoryCard(Map<String, dynamic> category, int index) {
  return Material(
    child: InkWell(
      onTap: () {
        Navigator.push(
          context,
          MaterialPageRoute(
            builder: (context) => CategoryProductsScreen(
              categoryId: category['id']?.toString() ?? '',
              categoryName: categoryName,
            ),
          ),
        );
      },
      child: Container(
        child: Row(
          children: [
            // Image de la catégorie (60x60)
            Container(...),
            
            // Informations (nom + nombre de produits)
            Expanded(child: Column(...)),
            
            // Flèche de navigation
            Icon(Icons.arrow_forward_ios),
          ],
        ),
      ),
    ),
  );
}
```

## États de l'Interface

### 1. **État de Chargement**
```dart
Widget _buildLoadingState() {
  return Center(
    child: Column(
      children: [
        CircularProgressIndicator(),
        Text('Chargement des catégories...'),
      ],
    ),
  );
}
```

### 2. **État Vide**
```dart
Widget _buildEmptyState() {
  return Center(
    child: Column(
      children: [
        Icon(_searchQuery.isNotEmpty ? Icons.search_off : Icons.category_outlined),
        Text(_searchQuery.isNotEmpty 
          ? 'Aucune catégorie trouvée'
          : 'Aucune catégorie disponible'),
        Text(_searchQuery.isNotEmpty
          ? 'Essayez avec d\'autres mots-clés'
          : 'Les catégories seront bientôt disponibles'),
      ],
    ),
  );
}
```

## Modifications du Service

### CatalogueService
```dart
// Avant
static Future<List<Map<String, dynamic>>> getCategories(int limit) async {

// Après
static Future<List<Map<String, dynamic>>> getCategories([int? limit]) async {
  // Construire l'URL avec ou sans limite
  String url = 'https://dev-mani.io/.../get_categories.php';
  if (limit != null) {
    url += '?limit=$limit';
  }
  
  final response = await http.get(Uri.parse(url));
  // ...
}
```

## Navigation

### Depuis CatalogueScreen
```dart
// Bouton "Voir tout" dans la section catégories
GestureDetector(
  onTap: () {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => const AllCategoriesScreen(),
      ),
    );
  },
  child: Text('Voir tout'),
)
```

### Vers CategoryProductsScreen
```dart
// Tap sur une catégorie
Navigator.push(
  context,
  MaterialPageRoute(
    builder: (context) => CategoryProductsScreen(
      categoryId: category['id']?.toString() ?? '',
      categoryName: categoryName,
    ),
  ),
);
```

## Animations

### 1. **Animation d'Entrée**
```dart
FadeTransition(
  opacity: _fadeAnimation,
  child: Column(...),
)
```

### 2. **Animation des Cartes**
```dart
AnimatedContainer(
  duration: Duration(milliseconds: 300 + (index * 100)),
  child: _buildCategoryCard(category, index),
)
```

## Fonctionnalités Avancées

### 1. **Pull-to-Refresh**
```dart
RefreshIndicator(
  onRefresh: _loadAllCategories,
  color: AppTheme.color.primaryColor,
  child: ListView.builder(...),
)
```

### 2. **Gestion des Images**
```dart
Image.network(
  imageUrl,
  fit: BoxFit.cover,
  errorBuilder: (context, error, stackTrace) {
    return Icon(Icons.category);
  },
)
```

### 3. **Compteur de Produits**
```dart
Text(
  '$productCount produit${productCount > 1 ? 's' : ''}',
  style: TextStyle(color: Colors.grey.shade600),
)
```

## Avantages

### 1. **Expérience Utilisateur**
- Navigation fluide et intuitive
- Recherche instantanée
- Feedback visuel approprié

### 2. **Performance**
- Chargement optimisé
- Animations légères
- Gestion d'erreurs robuste

### 3. **Maintenabilité**
- Code modulaire et réutilisable
- Séparation des responsabilités
- Documentation complète

## Tests Recommandés

### 1. **Tests Fonctionnels**
- Chargement des catégories
- Fonctionnalité de recherche
- Navigation vers les produits

### 2. **Tests d'Interface**
- États de chargement et vide
- Animations et transitions
- Responsive design

### 3. **Tests de Performance**
- Temps de chargement
- Fluidité des animations
- Gestion mémoire

Cette implémentation offre une expérience complète et professionnelle pour la navigation dans les catégories de l'application.
