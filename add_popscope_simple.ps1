# Script PowerShell pour ajouter PopScope à tous les showDialog
# Version simplifiée qui traite les patterns les plus courants

$libPath = "lib"
$dartFiles = Get-ChildItem -Path $libPath -Recurse -Filter "*.dart"

Write-Host "Traitement de $($dartFiles.Count) fichiers Dart..."

foreach ($file in $dartFiles) {
    $content = Get-Content $file.FullName -Raw
    $originalContent = $content
    $modified = $false
    
    # Pattern 1: builder: (context) => AlertDialog(
    $pattern1 = '(\s+builder:\s*\n?\s*\(context\)\s*=>\s*)AlertDialog\('
    $replacement1 = '${1}PopScope(' + "`n" + '            canPop: false,' + "`n" + '            child: AlertDialog('
    
    if ($content -match $pattern1) {
        $content = $content -replace $pattern1, $replacement1
        $modified = $true
        Write-Host "  Pattern 1 modifié dans: $($file.Name)"
    }
    
    # Pattern 2: builder: (context) => const AlertDialog(
    $pattern2 = '(\s+builder:\s*\n?\s*\(context\)\s*=>\s*)const AlertDialog\('
    $replacement2 = '${1}PopScope(' + "`n" + '            canPop: false,' + "`n" + '            child: const AlertDialog('
    
    if ($content -match $pattern2) {
        $content = $content -replace $pattern2, $replacement2
        $modified = $true
        Write-Host "  Pattern 2 modifié dans: $($file.Name)"
    }
    
    # Pattern 3: builder: (BuildContext context) { return AlertDialog(
    $pattern3 = '(\s+builder:\s*\(BuildContext\s+context\)\s*\{\s*\n\s*return\s*)AlertDialog\('
    $replacement3 = '${1}PopScope(' + "`n" + '        canPop: false,' + "`n" + '        child: AlertDialog('
    
    if ($content -match $pattern3) {
        $content = $content -replace $pattern3, $replacement3
        $modified = $true
        Write-Host "  Pattern 3 modifié dans: $($file.Name)"
    }
    
    # Pattern 4: builder: (context) => Dialog(
    $pattern4 = '(\s+builder:\s*\n?\s*\(context\)\s*=>\s*)Dialog\('
    $replacement4 = '${1}PopScope(' + "`n" + '            canPop: false,' + "`n" + '            child: Dialog('
    
    if ($content -match $pattern4) {
        $content = $content -replace $pattern4, $replacement4
        $modified = $true
        Write-Host "  Pattern 4 modifié dans: $($file.Name)"
    }
    
    # Pattern 5: builder: (context) => StatefulBuilder(
    $pattern5 = '(\s+builder:\s*\n?\s*\(context\)\s*=>\s*)StatefulBuilder\('
    $replacement5 = '${1}PopScope(' + "`n" + '            canPop: false,' + "`n" + '            child: StatefulBuilder('
    
    if ($content -match $pattern5) {
        $content = $content -replace $pattern5, $replacement5
        $modified = $true
        Write-Host "  Pattern 5 modifié dans: $($file.Name)"
    }
    
    if ($modified) {
        # Maintenant nous devons ajouter les fermetures de PopScope
        # Compter le nombre de PopScope ajoutés dans ce fichier
        $newPopScopeCount = ($content | Select-String -Pattern "PopScope\(" -AllMatches).Matches.Count
        $originalPopScopeCount = ($originalContent | Select-String -Pattern "PopScope\(" -AllMatches).Matches.Count
        $addedPopScopeCount = $newPopScopeCount - $originalPopScopeCount
        
        Write-Host "  $addedPopScopeCount PopScope ajoutés dans: $($file.Name)"
        
        # Sauvegarder le fichier modifié
        Set-Content -Path $file.FullName -Value $content -Encoding UTF8
        Write-Host "  Fichier sauvegardé: $($file.Name)"
    }
}

Write-Host "`nScript terminé!"
Write-Host "ATTENTION: Vous devez maintenant ajouter manuellement les fermetures '),' pour chaque PopScope ajouté."
Write-Host "Recherchez les erreurs de compilation pour identifier où ajouter les fermetures."
