import 'package:flutter/material.dart';
import 'package:callitris/utils/appTheme.dart';
import 'package:intl/intl.dart';

class CotisationDetailScreen extends StatefulWidget {
  final String cotisationId;

  const CotisationDetailScreen({super.key, required this.cotisationId});

  @override
  State<CotisationDetailScreen> createState() => _CotisationDetailScreenState();
}

class _CotisationDetailScreenState extends State<CotisationDetailScreen>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;
  late Map<String, dynamic> _cotisation;

  // Liste des transactions de l'utilisateur
  final List<Map<String, dynamic>> _transactions = [
    {
      'id': '1',
      'type': 'Versement',
      'amount': 5000,
      'date': '10/06/2023',
      'method': 'Mobile Money',
      'status': 'Réussi',
    },
    {
      'id': '2',
      'type': 'Versement',
      'amount': 10000,
      'date': '03/06/2023',
      'method': 'Carte bancaire',
      'status': 'Réussi',
    },
    {
      'id': '3',
      'type': 'Versement',
      'amount': 5000,
      'date': '27/05/2023',
      'method': 'Mobile Money',
      'status': 'Réussi',
    },
    {
      'id': '4',
      'type': 'Versement',
      'amount': 15000,
      'date': '20/05/2023',
      'method': 'Carte bancaire',
      'status': 'Réussi',
    },
    {
      'id': '5',
      'type': 'Versement',
      'amount': 5000,
      'date': '13/05/2023',
      'method': 'Mobile Money',
      'status': 'Réussi',
    },
    {
      'id': '6',
      'type': 'Versement',
      'amount': 10000,
      'date': '06/05/2023',
      'method': 'Carte bancaire',
      'status': 'Réussi',
    },
    {
      'id': '7',
      'type': 'Versement',
      'amount': 5000,
      'date': '29/04/2023',
      'method': 'Mobile Money',
      'status': 'Réussi',
    },
  ];

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 2, vsync: this);

    // Initialiser les données de la cotisation en fonction de l'ID
    _cotisation = {
      'id': widget.cotisationId,
      'name': 'Cotisation Anniversaire',
      'amount': 5000,
      'frequency': 'Quotidien',
      'nextPayment': '15/06/2023',
      'status': 'active',
      'progress': 0.33, // 7 versements sur 21
      'versementsEffectues': 7,
      'versementsRestants': 14,
      'startDate': '01/01/2023',
      'endDate': '31/12/2023',
      'description': 'Cotisation pour mon anniversaire',
      'totalAmount': 105000, // 5000 * 21
      'collectedAmount': 35000, // 5000 * 7
      'remainingAmount': 70000, // 5000 * 14
      'monnaie': 2500,
    };
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFFF8F9FA),
      appBar: AppBar(
        elevation: 0,
        backgroundColor: Colors.white,
        centerTitle: true,
        title: Text(
          _cotisation['name'],
          style: TextStyle(
            fontSize: 18,
            fontWeight: FontWeight.w600,
            color: AppTheme.color.textColor,
          ),
        ),
        leading: IconButton(
          icon: Icon(
            Icons.arrow_back_ios,
            color: AppTheme.color.textColor,
            size: 20,
          ),
          onPressed: () => Navigator.pop(context),
        ),
        actions: [
          IconButton(
            icon: Icon(Icons.more_vert, color: AppTheme.color.textColor),
            onPressed: () {
              _showOptionsMenu(context);
            },
          ),
        ],
      ),
      body: Column(
        children: [
          _buildCotisationHeader(),
          Container(
            color: Colors.white,
            child: TabBar(
              controller: _tabController,
              labelColor: AppTheme.color.primaryColor,
              unselectedLabelColor: AppTheme.color.brunGris,
              indicatorColor: AppTheme.color.primaryColor,
              indicatorWeight: 3,
              tabs: const [Tab(text: 'Aperçu'), Tab(text: 'Transactions')],
            ),
          ),
          Expanded(
            child: TabBarView(
              controller: _tabController,
              children: [_buildOverviewTab(), _buildTransactionsTab()],
            ),
          ),
        ],
      ),
      floatingActionButton: FloatingActionButton(
        onPressed: () {
          _showVersementDialog(context, _cotisation['amount']);
        },
        backgroundColor: AppTheme.color.primaryColor,
        child: const Icon(Icons.add),
      ),
    );
  }

  Widget _buildCotisationHeader() {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [
            AppTheme.color.primaryColor,
            Color.fromARGB(255, 41, 98, 255),
          ],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
      ),
      child: Column(
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const Text(
                    'Montant total',
                    style: TextStyle(color: Colors.white70, fontSize: 14),
                  ),
                  Text(
                    '${_formatPrice(_cotisation['totalAmount'])} FCFA',
                    style: const TextStyle(
                      color: Colors.white,
                      fontSize: 24,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ],
              ),
              Container(
                padding: const EdgeInsets.symmetric(
                  horizontal: 12,
                  vertical: 6,
                ),
                decoration: BoxDecoration(
                  color: Colors.white.withOpacity(0.2),
                  borderRadius: BorderRadius.circular(20),
                ),
                child: Text(
                  _cotisation['status'] == 'active' ? 'Active' : 'Inactive',
                  style: const TextStyle(
                    color: Colors.white,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
            ],
          ),
          const SizedBox(height: 20),
          Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    'Progression: ${(_cotisation['progress'] * 100).toInt()}%',
                    style: const TextStyle(
                      color: Colors.white,
                      fontSize: 14,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                  Text(
                    '${_cotisation['versementsEffectues']}/${_cotisation['versementsEffectues'] + _cotisation['versementsRestants']} jours',
                    style: const TextStyle(
                      color: Colors.white,
                      fontSize: 14,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 8),
              ClipRRect(
                borderRadius: BorderRadius.circular(8),
                child: LinearProgressIndicator(
                  value: _cotisation['progress'],
                  backgroundColor: Colors.white.withOpacity(0.3),
                  valueColor: const AlwaysStoppedAnimation<Color>(Colors.white),
                  minHeight: 10,
                ),
              ),
              const SizedBox(height: 12),
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      const Text(
                        'Collecté',
                        style: TextStyle(color: Colors.white70, fontSize: 12),
                      ),
                      Text(
                        '${_formatPrice(_cotisation['collectedAmount'])} FCFA',
                        style: const TextStyle(
                          color: Colors.white,
                          fontSize: 14,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                    ],
                  ),
                  Column(
                    crossAxisAlignment: CrossAxisAlignment.end,
                    children: [
                      const Text(
                        'Restant',
                        style: TextStyle(color: Colors.white70, fontSize: 12),
                      ),
                      Text(
                        '${_formatPrice(_cotisation['remainingAmount'])} FCFA',
                        style: const TextStyle(
                          color: Colors.white,
                          fontSize: 14,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildOverviewTab() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildNextPaymentCard(),
          const SizedBox(height: 16),
          _buildInfoCard(),
          const SizedBox(height: 16),
          _buildCalendarCard(),
          const SizedBox(height: 16),
          _buildRecentTransactionsCard(),
          const SizedBox(height: 16),
          _buildDescriptionCard(),
          const SizedBox(height: 80), // Espace pour le FAB
        ],
      ),
    );
  }

  Widget _buildInfoCard() {
    return Card(
      elevation: 0,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16),
        side: BorderSide(color: Colors.grey.shade200),
      ),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Container(
                  padding: const EdgeInsets.all(10),
                  decoration: BoxDecoration(
                    color: AppTheme.color.primaryColor.withOpacity(0.1),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Icon(
                    Icons.info_outline,
                    color: AppTheme.color.primaryColor,
                    size: 20,
                  ),
                ),
                const SizedBox(width: 12),
                Text(
                  'Informations générales',
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.w600,
                    color: AppTheme.color.textColor,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            _buildInfoRow(
              'Montant par versement',
              '${_formatPrice(_cotisation['amount'])} FCFA',
            ),
            _buildInfoRow('Nombre total de jours', '21'),
            _buildInfoRow(
              'Jours couverts',
              '${_cotisation['versementsEffectues']}',
            ),
            _buildInfoRow(
              'Jours restants',
              '${_cotisation['versementsRestants']}',
            ),
            _buildInfoRow('Date de création', _cotisation['startDate']),
            _buildInfoRow(
              'Monnaie disponible',
              '${_formatPrice(_cotisation['monnaie'])} FCFA',
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildInfoRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 12),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            label,
            style: TextStyle(color: AppTheme.color.brunGris, fontSize: 14),
          ),
          Text(
            value,
            style: TextStyle(
              fontWeight: FontWeight.w600,
              fontSize: 14,
              color: AppTheme.color.textColor,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildNextPaymentCard() {
    return Card(
      elevation: 0,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16),
        side: BorderSide(color: Colors.grey.shade200),
      ),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Container(
                  padding: const EdgeInsets.all(10),
                  decoration: BoxDecoration(
                    color: AppTheme.color.primaryColor.withOpacity(0.1),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Icon(
                    Icons.calendar_today,
                    color: AppTheme.color.primaryColor,
                    size: 20,
                  ),
                ),
                const SizedBox(width: 12),
                Text(
                  'Prochain versement',
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.w600,
                    color: AppTheme.color.textColor,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Date',
                      style: TextStyle(
                        color: AppTheme.color.brunGris,
                        fontSize: 14,
                      ),
                    ),
                    Text(
                      _cotisation['nextPayment'],
                      style: TextStyle(
                        fontWeight: FontWeight.w600,
                        fontSize: 16,
                        color: AppTheme.color.textColor,
                      ),
                    ),
                  ],
                ),
                Column(
                  crossAxisAlignment: CrossAxisAlignment.end,
                  children: [
                    Text(
                      'Montant',
                      style: TextStyle(
                        color: AppTheme.color.brunGris,
                        fontSize: 14,
                      ),
                    ),
                    Text(
                      '${_formatPrice(_cotisation['amount'])} FCFA',
                      style: TextStyle(
                        fontWeight: FontWeight.w600,
                        fontSize: 16,
                        color: AppTheme.color.greenColor,
                      ),
                    ),
                  ],
                ),
              ],
            ),
            const SizedBox(height: 16),
            ElevatedButton.icon(
              onPressed: () {
                _showVersementDialog(context, _cotisation['amount']);
              },
              icon: const Icon(Icons.add_circle),
              label: const Text('Effectuer un versement'),
              style: ElevatedButton.styleFrom(
                backgroundColor: AppTheme.color.primaryColor,
                foregroundColor: Colors.white,
                minimumSize: const Size(double.infinity, 48),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(12),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildDescriptionCard() {
    return Card(
      elevation: 0,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16),
        side: BorderSide(color: Colors.grey.shade200),
      ),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Container(
                  padding: const EdgeInsets.all(10),
                  decoration: BoxDecoration(
                    color: AppTheme.color.primaryColor.withOpacity(0.1),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Icon(
                    Icons.description,
                    color: AppTheme.color.primaryColor,
                    size: 20,
                  ),
                ),
                const SizedBox(width: 12),
                Text(
                  'Description',
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.w600,
                    color: AppTheme.color.textColor,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            Text(
              _cotisation['description'],
              style: TextStyle(
                color: AppTheme.color.brunGris,
                fontSize: 14,
                height: 1.5,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildRecentTransactionsCard() {
    final recentTransactions = _transactions.take(3).toList();

    return Card(
      elevation: 0,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16),
        side: BorderSide(color: Colors.grey.shade200),
      ),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Row(
                  children: [
                    Container(
                      padding: const EdgeInsets.all(10),
                      decoration: BoxDecoration(
                        color: AppTheme.color.primaryColor.withOpacity(0.1),
                        borderRadius: BorderRadius.circular(12),
                      ),
                      child: Icon(
                        Icons.receipt_long,
                        color: AppTheme.color.primaryColor,
                        size: 20,
                      ),
                    ),
                    const SizedBox(width: 12),
                    Text(
                      'Transactions récentes',
                      style: TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.w600,
                        color: AppTheme.color.textColor,
                      ),
                    ),
                  ],
                ),
                TextButton(
                  onPressed: () {
                    _tabController.animateTo(
                      1,
                    ); // Aller à l'onglet Transactions
                  },
                  child: Text(
                    'Voir tout',
                    style: TextStyle(
                      color: AppTheme.color.primaryColor,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 8),
            ...recentTransactions.map(
              (transaction) => _buildTransactionItem(transaction),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildTransactionsTab() {
    return ListView.builder(
      padding: const EdgeInsets.all(16),
      itemCount: _transactions.length,
      itemBuilder: (context, index) {
        final transaction = _transactions[index];
        return _buildTransactionItem(transaction);
      },
    );
  }

  Widget _buildTransactionItem(Map<String, dynamic> transaction) {
    return Card(
      elevation: 0,
      margin: const EdgeInsets.only(bottom: 12),
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: ListTile(
        contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
        leading: CircleAvatar(
          backgroundColor: AppTheme.color.primaryColor.withOpacity(0.2),
          child: Icon(
            transaction['type'] == 'Versement'
                ? Icons.arrow_upward
                : Icons.arrow_downward,
            color: AppTheme.color.primaryColor,
          ),
        ),
        title: Text(
          '${_formatPrice(transaction['amount'])} FCFA',
          style: const TextStyle(fontWeight: FontWeight.bold),
        ),
        subtitle: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              transaction['type'],
              style: TextStyle(color: AppTheme.color.brunGris),
            ),
            Text(
              'Via ${transaction['method']}',
              style: TextStyle(color: AppTheme.color.brunGris, fontSize: 12),
            ),
          ],
        ),
        trailing: Column(
          crossAxisAlignment: CrossAxisAlignment.end,
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Text(
              transaction['date'],
              style: TextStyle(color: AppTheme.color.brunGris, fontSize: 12),
            ),
            const SizedBox(height: 4),
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
              decoration: BoxDecoration(
                color:
                    transaction['status'] == 'Réussi'
                        ? AppTheme.color.greenColor.withOpacity(0.2)
                        : AppTheme.color.redColor.withOpacity(0.2),
                borderRadius: BorderRadius.circular(20),
              ),
              child: Text(
                transaction['status'],
                style: TextStyle(
                  color:
                      transaction['status'] == 'Réussi'
                          ? AppTheme.color.greenColor
                          : AppTheme.color.redColor,
                  fontWeight: FontWeight.bold,
                  fontSize: 12,
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  void _showVersementDialog(BuildContext context, int montantJournalier) {
    final TextEditingController montantController = TextEditingController();
    montantController.text = montantJournalier.toString();

    // Variables pour le calcul en temps réel
    int versementsComplets = 1; // Par défaut, 1 jour
    double monnaie = 0;

    // Fonction pour calculer les jours et la monnaie
    void calculerVersement(String value) {
      final double montantVerse = double.tryParse(value) ?? 0;
      versementsComplets = (montantVerse / montantJournalier).floor();
      monnaie = montantVerse - (versementsComplets * montantJournalier);
    }

    // Calculer les valeurs initiales
    calculerVersement(montantController.text);

    showDialog(
      context: context,
      barrierDismissible: false,
      barrierColor: Colors.black.withOpacity(0.6),
      builder: (BuildContext context) {
        return StatefulBuilder(
          builder: (context, setState) {
            return Dialog(
              insetPadding: const EdgeInsets.symmetric(horizontal: 20),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(28),
              ),
              elevation: 0,
              backgroundColor: Colors.transparent,
              child: Container(
                padding: const EdgeInsets.all(28),
                decoration: BoxDecoration(
                  color: Colors.white,
                  borderRadius: BorderRadius.circular(28),
                  boxShadow: [
                    BoxShadow(
                      color: Colors.black.withOpacity(0.12),
                      blurRadius: 30,
                      offset: const Offset(0, 15),
                      spreadRadius: 0,
                    ),
                  ],
                ),
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Effectuer un versement',
                      style: TextStyle(
                        fontSize: 20,
                        fontWeight: FontWeight.bold,
                        color: AppTheme.color.textColor,
                      ),
                    ),
                    const SizedBox(height: 8),
                    Text(
                      'Montant journalier: ${_formatPrice(montantJournalier)} FCFA',
                      style: TextStyle(
                        color: AppTheme.color.brunGris,
                        fontSize: 14,
                      ),
                    ),
                    const SizedBox(height: 24),
                    TextField(
                      controller: montantController,
                      keyboardType: TextInputType.number,
                      onChanged: (value) {
                        setState(() {
                          calculerVersement(value);
                        });
                      },
                      decoration: InputDecoration(
                        labelText: 'Montant à verser',
                        hintText: 'Entrez le montant',
                        border: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(16),
                          borderSide: BorderSide(
                            color: AppTheme.color.brunGris.withOpacity(0.3),
                          ),
                        ),
                        enabledBorder: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(16),
                          borderSide: BorderSide(
                            color: AppTheme.color.brunGris.withOpacity(0.3),
                          ),
                        ),
                        focusedBorder: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(16),
                          borderSide: BorderSide(
                            color: AppTheme.color.primaryColor,
                            width: 2,
                          ),
                        ),
                        suffixText: 'FCFA',
                        prefixIcon: Icon(
                          Icons.payments_outlined,
                          color: AppTheme.color.primaryColor,
                        ),
                      ),
                    ),
                    const SizedBox(height: 24),
                    Container(
                      padding: const EdgeInsets.all(16),
                      decoration: BoxDecoration(
                        color: AppTheme.color.primaryColor.withOpacity(0.05),
                        borderRadius: BorderRadius.circular(16),
                      ),
                      child: Column(
                        children: [
                          Row(
                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                            children: [
                              Text(
                                'Jours couverts:',
                                style: TextStyle(
                                  color: AppTheme.color.brunGris,
                                  fontSize: 14,
                                ),
                              ),
                              Text(
                                '$versementsComplets jour${versementsComplets > 1 ? 's' : ''}',
                                style: TextStyle(
                                  fontWeight: FontWeight.bold,
                                  color: AppTheme.color.textColor,
                                  fontSize: 14,
                                ),
                              ),
                            ],
                          ),
                          const SizedBox(height: 8),
                          Row(
                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                            children: [
                              Text(
                                'Monnaie:',
                                style: TextStyle(
                                  color: AppTheme.color.brunGris,
                                  fontSize: 14,
                                ),
                              ),
                              Text(
                                '${monnaie.toStringAsFixed(0)} FCFA',
                                style: TextStyle(
                                  fontWeight: FontWeight.bold,
                                  color: AppTheme.color.greenColor,
                                  fontSize: 14,
                                ),
                              ),
                            ],
                          ),
                        ],
                      ),
                    ),
                    const SizedBox(height: 24),
                    Row(
                      children: [
                        Expanded(
                          child: OutlinedButton(
                            onPressed: () {
                              Navigator.of(context).pop();
                            },
                            style: OutlinedButton.styleFrom(
                              padding: const EdgeInsets.symmetric(vertical: 16),
                              shape: RoundedRectangleBorder(
                                borderRadius: BorderRadius.circular(16),
                              ),
                              side: BorderSide(
                                color: AppTheme.color.brunGris.withOpacity(0.3),
                              ),
                            ),
                            child: Text(
                              'Annuler',
                              style: TextStyle(
                                color: AppTheme.color.brunGris,
                                fontWeight: FontWeight.w600,
                              ),
                            ),
                          ),
                        ),
                        const SizedBox(width: 16),
                        Expanded(
                          child: ElevatedButton(
                            onPressed: () {
                              Navigator.of(context).pop();

                              // Simuler l'ajout d'une transaction
                              final now = DateTime.now();
                              final dateFormat = DateFormat('dd/MM/yyyy');

                              setState(() {
                                _transactions.insert(0, {
                                  'id': (_transactions.length + 1).toString(),
                                  'type': 'Versement',
                                  'amount':
                                      int.tryParse(montantController.text) ?? 0,
                                  'date': dateFormat.format(now),
                                  'method': 'Mobile Money',
                                  'status': 'Réussi',
                                });

                                // Mettre à jour les données de la cotisation
                                _cotisation['versementsEffectues'] +=
                                    versementsComplets;
                                _cotisation['versementsRestants'] -=
                                    versementsComplets;
                                _cotisation['collectedAmount'] +=
                                    versementsComplets * montantJournalier;
                                _cotisation['remainingAmount'] -=
                                    versementsComplets * montantJournalier;
                                _cotisation['monnaie'] += monnaie;
                                _cotisation['progress'] =
                                    _cotisation['versementsEffectues'] /
                                    (_cotisation['versementsEffectues'] +
                                        _cotisation['versementsRestants']);
                              });

                              // Afficher un message de confirmation
                              ScaffoldMessenger.of(context).showSnackBar(
                                SnackBar(
                                  content: const Text(
                                    'Versement effectué avec succès',
                                  ),
                                  backgroundColor: AppTheme.color.greenColor,
                                ),
                              );
                            },
                            style: ElevatedButton.styleFrom(
                              backgroundColor: AppTheme.color.primaryColor,
                              foregroundColor: Colors.white,
                              padding: const EdgeInsets.symmetric(vertical: 16),
                              elevation: 0,
                              shape: RoundedRectangleBorder(
                                borderRadius: BorderRadius.circular(16),
                              ),
                            ),
                            child: const Text(
                              'Verser',
                              style: TextStyle(fontWeight: FontWeight.w600),
                            ),
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            );
          },
        );
      },
    );
  }

  String _formatPrice(int price) {
    // Formater le prix avec des séparateurs de milliers
    final String priceString = price.toString();
    final StringBuffer result = StringBuffer();

    for (int i = 0; i < priceString.length; i++) {
      if (i > 0 && (priceString.length - i) % 3 == 0) {
        result.write(' ');
      }
      result.write(priceString[i]);
    }

    return result.toString();
  }

  Widget _buildCalendarCard() {
    return Card(
      elevation: 0,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16),
        side: BorderSide(color: Colors.grey.shade200),
      ),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Container(
                  padding: const EdgeInsets.all(10),
                  decoration: BoxDecoration(
                    color: AppTheme.color.primaryColor.withOpacity(0.1),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Icon(
                    Icons.calendar_month,
                    color: AppTheme.color.primaryColor,
                    size: 20,
                  ),
                ),
                const SizedBox(width: 12),
                Text(
                  'Calendrier des versements',
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.w600,
                    color: AppTheme.color.textColor,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            GridView.builder(
              shrinkWrap: true,
              physics: const NeverScrollableScrollPhysics(),
              gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
                crossAxisCount: 7,
                childAspectRatio: 1,
                crossAxisSpacing: 8,
                mainAxisSpacing: 8,
              ),
              itemCount: 21, // Nombre total de versements
              itemBuilder: (context, index) {
                final bool isCompleted =
                    index < _cotisation['versementsEffectues'];
                return Container(
                  decoration: BoxDecoration(
                    color:
                        isCompleted
                            ? AppTheme.color.primaryColor
                            : Colors.grey[200],
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Center(
                    child: Text(
                      '${index + 1}',
                      style: TextStyle(
                        color:
                            isCompleted
                                ? Colors.white
                                : AppTheme.color.brunGris,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                );
              },
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                Container(
                  width: 16,
                  height: 16,
                  decoration: BoxDecoration(
                    color: AppTheme.color.primaryColor,
                    borderRadius: BorderRadius.circular(4),
                  ),
                ),
                const SizedBox(width: 8),
                const Text('Versement effectué'),
                const SizedBox(width: 16),
                Container(
                  width: 16,
                  height: 16,
                  decoration: BoxDecoration(
                    color: Colors.grey[200],
                    borderRadius: BorderRadius.circular(4),
                  ),
                ),
                const SizedBox(width: 8),
                const Text('Versement à venir'),
              ],
            ),
          ],
        ),
      ),
    );
  }

  void _showOptionsMenu(BuildContext context) {
    showModalBottomSheet(
      context: context,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
      ),
      builder: (context) {
        return SafeArea(
          child: Padding(
            padding: const EdgeInsets.symmetric(vertical: 16),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                ListTile(
                  leading: Icon(
                    Icons.edit_outlined,
                    color: AppTheme.color.primaryColor,
                  ),
                  title: const Text('Modifier la cotisation'),
                  onTap: () {
                    Navigator.pop(context);
                    // Logique pour modifier la cotisation
                  },
                ),
                ListTile(
                  leading: Icon(
                    Icons.share_outlined,
                    color: AppTheme.color.primaryColor,
                  ),
                  title: const Text('Partager'),
                  onTap: () {
                    Navigator.pop(context);
                    // Logique pour partager
                  },
                ),
                ListTile(
                  leading: Icon(
                    Icons.pause_circle_outline,
                    color: AppTheme.color.orangeColor,
                  ),
                  title: const Text('Mettre en pause'),
                  onTap: () {
                    Navigator.pop(context);
                    _showPauseDialog(context);
                  },
                ),
                ListTile(
                  leading: Icon(
                    Icons.delete_outline,
                    color: AppTheme.color.redColor,
                  ),
                  title: const Text('Supprimer'),
                  onTap: () {
                    Navigator.pop(context);
                    _showDeleteDialog(context);
                  },
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  void _showPauseDialog(BuildContext context) {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (BuildContext context) {
        return AlertDialog(
          title: Text(
            'Mettre en pause',
            style: TextStyle(color: AppTheme.color.primaryColor),
          ),
          content: const Text(
            'Voulez-vous vraiment mettre cette cotisation en pause ? Vous pourrez la réactiver plus tard.',
          ),
          actions: [
            TextButton(
              onPressed: () {
                Navigator.of(context).pop();
              },
              child: Text(
                'Annuler',
                style: TextStyle(color: AppTheme.color.brunGris),
              ),
            ),
            ElevatedButton(
              onPressed: () {
                Navigator.of(context).pop();
                // Logique pour mettre en pause
                ScaffoldMessenger.of(context).showSnackBar(
                  SnackBar(
                    content: const Text('Cotisation mise en pause'),
                    backgroundColor: AppTheme.color.orangeColor,
                  ),
                );
              },
              style: ElevatedButton.styleFrom(
                backgroundColor: AppTheme.color.orangeColor,
              ),
              child: const Text('Mettre en pause'),
            ),
          ],
        );
      },
    );
  }

  void _showDeleteDialog(BuildContext context) {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (BuildContext context) {
        return AlertDialog(
          title: Text(
            'Supprimer la cotisation',
            style: TextStyle(color: AppTheme.color.redColor),
          ),
          content: const Text(
            'Voulez-vous vraiment supprimer cette cotisation ? Cette action est irréversible.',
          ),
          actions: [
            TextButton(
              onPressed: () {
                Navigator.of(context).pop();
              },
              child: Text(
                'Annuler',
                style: TextStyle(color: AppTheme.color.brunGris),
              ),
            ),
            ElevatedButton(
              onPressed: () {
                Navigator.of(context).pop();
                Navigator.of(context).pop(); // Retour à l'écran précédent
                // Logique pour supprimer
              },
              style: ElevatedButton.styleFrom(
                backgroundColor: AppTheme.color.redColor,
              ),
              child: const Text('Supprimer'),
            ),
          ],
        );
      },
    );
  }
}
