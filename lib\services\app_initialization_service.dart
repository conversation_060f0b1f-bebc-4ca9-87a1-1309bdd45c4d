import 'dart:async';
import 'package:flutter/foundation.dart';
import 'package:callitris/services/network_service.dart';
import 'package:callitris/services/auth_service.dart';
import 'package:callitris/services/order_service.dart';
import 'package:callitris/services/smart_cache_service.dart';
import 'package:callitris/services/image_service.dart';

/// Service d'initialisation optimisé pour l'application
class AppInitializationService {
  static const String _tag = '[AppInitializationService]';
  
  // État d'initialisation
  static bool _isInitialized = false;
  static bool _isInitializing = false;
  static final Completer<void> _initializationCompleter = Completer<void>();
  
  // Services critiques (nécessaires au démarrage)
  static final List<String> _criticalServices = [
    'NetworkService',
    'AuthService',
  ];
  
  // Services non-critiques (peuvent être initialisés en arrière-plan)
  static final List<String> _nonCriticalServices = [
    'OrderService',
    'SmartCacheService',
    'ImageService',
  ];
  
  // Statut d'initialisation des services
  static final Map<String, bool> _serviceStatus = {};
  static final Map<String, String> _serviceErrors = {};

  /// Initialise l'application de manière optimisée
  static Future<void> initialize() async {
    if (_isInitialized) return;
    if (_isInitializing) return _initializationCompleter.future;
    
    _isInitializing = true;
    final stopwatch = Stopwatch()..start();
    
    try {
      debugPrint('$_tag: Début de l\'initialisation optimisée...');
      
      // Phase 1: Initialisation des services critiques en parallèle
      await _initializeCriticalServices();
      
      // Phase 2: Initialisation des services non-critiques en arrière-plan
      _initializeNonCriticalServicesInBackground();
      
      _isInitialized = true;
      _initializationCompleter.complete();
      
      stopwatch.stop();
      debugPrint('$_tag: Initialisation terminée en ${stopwatch.elapsedMilliseconds}ms');
      
    } catch (e) {
      debugPrint('$_tag: Erreur lors de l\'initialisation: $e');
      _initializationCompleter.completeError(e);
      rethrow;
    } finally {
      _isInitializing = false;
    }
  }

  /// Initialise les services critiques en parallèle
  static Future<void> _initializeCriticalServices() async {
    debugPrint('$_tag: Initialisation des services critiques...');
    
    final futures = <Future<void>>[];
    
    // NetworkService
    futures.add(_initializeService(
      'NetworkService',
      () => NetworkService.instance.initialize(),
    ));
    
    // AuthService (dépend de NetworkService, mais peut être initialisé en parallèle)
    futures.add(_initializeService(
      'AuthService',
      () => AuthService.initialize(),
    ));
    
    // Attendre que tous les services critiques soient initialisés
    await Future.wait(futures);
    
    // Vérifier que tous les services critiques sont OK
    for (final service in _criticalServices) {
      if (!_serviceStatus[service]!) {
        throw Exception('Service critique $service a échoué: ${_serviceErrors[service]}');
      }
    }
    
    debugPrint('$_tag: Services critiques initialisés avec succès');
  }

  /// Initialise les services non-critiques en arrière-plan
  static void _initializeNonCriticalServicesInBackground() {
    debugPrint('$_tag: Initialisation des services non-critiques en arrière-plan...');
    
    // OrderService
    _initializeService(
      'OrderService',
      () => OrderService.initialize(),
    );
    
    // SmartCacheService
    _initializeService(
      'SmartCacheService',
      () => SmartCacheService.instance.initialize(),
    );
    
    // ImageService
    _initializeService(
      'ImageService',
      () => Future.value(), // ImageService n'a pas besoin d'initialisation async
    );
  }

  /// Initialise un service spécifique avec gestion d'erreur
  static Future<void> _initializeService(
    String serviceName,
    Future<void> Function() initFunction,
  ) async {
    try {
      final stopwatch = Stopwatch()..start();
      await initFunction();
      stopwatch.stop();
      
      _serviceStatus[serviceName] = true;
      debugPrint('$_tag: $serviceName initialisé en ${stopwatch.elapsedMilliseconds}ms');
      
    } catch (e) {
      _serviceStatus[serviceName] = false;
      _serviceErrors[serviceName] = e.toString();
      debugPrint('$_tag: Erreur lors de l\'initialisation de $serviceName: $e');
      
      // Pour les services critiques, on relance l'erreur
      if (_criticalServices.contains(serviceName)) {
        rethrow;
      }
    }
  }

  /// Vérifie si l'application est initialisée
  static bool get isInitialized => _isInitialized;
  
  /// Vérifie si l'initialisation est en cours
  static bool get isInitializing => _isInitializing;
  
  /// Attend que l'initialisation soit terminée
  static Future<void> waitForInitialization() {
    if (_isInitialized) return Future.value();
    return _initializationCompleter.future;
  }

  /// Obtient le statut d'un service
  static bool isServiceInitialized(String serviceName) {
    return _serviceStatus[serviceName] ?? false;
  }

  /// Obtient l'erreur d'un service
  static String? getServiceError(String serviceName) {
    return _serviceErrors[serviceName];
  }

  /// Obtient le statut de tous les services
  static Map<String, bool> get serviceStatus => Map.unmodifiable(_serviceStatus);

  /// Réinitialise un service spécifique
  static Future<void> reinitializeService(String serviceName) async {
    debugPrint('$_tag: Réinitialisation de $serviceName...');
    
    switch (serviceName) {
      case 'NetworkService':
        await _initializeService(
          serviceName,
          () => NetworkService.instance.initialize(),
        );
        break;
      case 'AuthService':
        await _initializeService(
          serviceName,
          () => AuthService.initialize(),
        );
        break;
      case 'OrderService':
        await _initializeService(
          serviceName,
          () => OrderService.initialize(),
        );
        break;
      case 'SmartCacheService':
        await _initializeService(
          serviceName,
          () => SmartCacheService.instance.initialize(),
        );
        break;
      default:
        debugPrint('$_tag: Service $serviceName non reconnu');
    }
  }

  /// Précharge les données essentielles
  static Future<void> preloadEssentialData() async {
    if (!_isInitialized) {
      await waitForInitialization();
    }
    
    debugPrint('$_tag: Préchargement des données essentielles...');
    
    try {
      // Précharger les données utilisateur si connecté
      if (AuthService.isLoggedInValue) {
        await AuthService.refreshUserData(silent: true);
      }
      
      // Précharger les commandes si l'utilisateur est connecté
      if (AuthService.isLoggedInValue && isServiceInitialized('OrderService')) {
        await OrderService.refreshOrders(silent: true);
      }
      
      debugPrint('$_tag: Données essentielles préchargées');
      
    } catch (e) {
      debugPrint('$_tag: Erreur lors du préchargement des données: $e');
      // Ne pas bloquer l'application pour les erreurs de préchargement
    }
  }

  /// Nettoie les ressources d'initialisation
  static void dispose() {
    _serviceStatus.clear();
    _serviceErrors.clear();
    debugPrint('$_tag: Ressources d\'initialisation nettoyées');
  }

  /// Obtient un rapport détaillé de l'initialisation
  static Map<String, dynamic> getInitializationReport() {
    return {
      'isInitialized': _isInitialized,
      'isInitializing': _isInitializing,
      'criticalServices': _criticalServices.map((service) => {
        'name': service,
        'status': _serviceStatus[service] ?? false,
        'error': _serviceErrors[service],
      }).toList(),
      'nonCriticalServices': _nonCriticalServices.map((service) => {
        'name': service,
        'status': _serviceStatus[service] ?? false,
        'error': _serviceErrors[service],
      }).toList(),
    };
  }
}
