/// Énumération des statuts de transaction CinetPay
enum TransactionStatus {
  pending,    // En attente
  processing, // En cours de traitement
  completed,  // Terminée avec succès
  failed,     // <PERSON>cho<PERSON><PERSON>
  cancelled,  // Annulée
  refunded,   // Remboursée
}

/// Énumération des types de transaction
enum TransactionType {
  versement,  // Versement de commande
  package,    // Achat de package
  tontine,    // Participation à une tontine
  recharge,   // Recharge de monnaie
}

/// Modèle de transaction CinetPay
class CinetPayTransaction {
  final String id;
  final double amount;
  final String currency;
  final String description;
  final String customerName;
  final String customerEmail;
  final String? customerPhone;
  final DateTime createdAt;
  final Map<String, dynamic> metadata;
  
  // Champs modifiables
  TransactionStatus status;
  DateTime? completedAt;
  String? operatorId;
  String? paymentMethod;
  String? failureReason;
  String? refundReason;
  
  CinetPayTransaction({
    required this.id,
    required this.amount,
    required this.currency,
    required this.description,
    required this.customerName,
    required this.customerEmail,
    this.customerPhone,
    required this.createdAt,
    this.metadata = const {},
    this.status = TransactionStatus.pending,
    this.completedAt,
    this.operatorId,
    this.paymentMethod,
    this.failureReason,
    this.refundReason,
  });
  
  /// Convertit la transaction en Map pour la sérialisation
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'amount': amount,
      'currency': currency,
      'description': description,
      'customerName': customerName,
      'customerEmail': customerEmail,
      'customerPhone': customerPhone,
      'createdAt': createdAt.toIso8601String(),
      'metadata': metadata,
      'status': status.name,
      'completedAt': completedAt?.toIso8601String(),
      'operatorId': operatorId,
      'paymentMethod': paymentMethod,
      'failureReason': failureReason,
      'refundReason': refundReason,
    };
  }
  
  /// Crée une transaction à partir d'un Map
  factory CinetPayTransaction.fromJson(Map<String, dynamic> json) {
    return CinetPayTransaction(
      id: json['id'],
      amount: json['amount'].toDouble(),
      currency: json['currency'],
      description: json['description'],
      customerName: json['customerName'],
      customerEmail: json['customerEmail'],
      customerPhone: json['customerPhone'],
      createdAt: DateTime.parse(json['createdAt']),
      metadata: json['metadata'] ?? {},
      status: TransactionStatus.values.firstWhere(
        (e) => e.name == json['status'],
        orElse: () => TransactionStatus.pending,
      ),
      completedAt: json['completedAt'] != null 
          ? DateTime.parse(json['completedAt']) 
          : null,
      operatorId: json['operatorId'],
      paymentMethod: json['paymentMethod'],
      failureReason: json['failureReason'],
      refundReason: json['refundReason'],
    );
  }
  
  /// Vérifie si la transaction est terminée
  bool get isCompleted => status == TransactionStatus.completed;
  
  /// Vérifie si la transaction a échoué
  bool get isFailed => status == TransactionStatus.failed;
  
  /// Vérifie si la transaction est en cours
  bool get isPending => status == TransactionStatus.pending || status == TransactionStatus.processing;
  
  /// Vérifie si la transaction est annulée
  bool get isCancelled => status == TransactionStatus.cancelled;
  
  /// Obtient la durée de la transaction
  Duration? get duration {
    if (completedAt != null) {
      return completedAt!.difference(createdAt);
    }
    return null;
  }
  
  /// Obtient le type de transaction à partir des métadonnées
  TransactionType get type {
    final typeString = metadata['type'] as String?;
    if (typeString != null) {
      return TransactionType.values.firstWhere(
        (e) => e.name == typeString,
        orElse: () => TransactionType.versement,
      );
    }
    return TransactionType.versement;
  }
  
  /// Obtient l'ID de la commande associée (si applicable)
  String? get orderId => metadata['orderId'] as String?;
  
  /// Obtient l'ID du package associé (si applicable)
  String? get packageId => metadata['packageId'] as String?;
  
  /// Obtient l'ID de la tontine associée (si applicable)
  String? get tontineId => metadata['tontineId'] as String?;
  
  /// Copie la transaction avec de nouvelles valeurs
  CinetPayTransaction copyWith({
    String? id,
    double? amount,
    String? currency,
    String? description,
    String? customerName,
    String? customerEmail,
    String? customerPhone,
    DateTime? createdAt,
    Map<String, dynamic>? metadata,
    TransactionStatus? status,
    DateTime? completedAt,
    String? operatorId,
    String? paymentMethod,
    String? failureReason,
    String? refundReason,
  }) {
    return CinetPayTransaction(
      id: id ?? this.id,
      amount: amount ?? this.amount,
      currency: currency ?? this.currency,
      description: description ?? this.description,
      customerName: customerName ?? this.customerName,
      customerEmail: customerEmail ?? this.customerEmail,
      customerPhone: customerPhone ?? this.customerPhone,
      createdAt: createdAt ?? this.createdAt,
      metadata: metadata ?? this.metadata,
      status: status ?? this.status,
      completedAt: completedAt ?? this.completedAt,
      operatorId: operatorId ?? this.operatorId,
      paymentMethod: paymentMethod ?? this.paymentMethod,
      failureReason: failureReason ?? this.failureReason,
      refundReason: refundReason ?? this.refundReason,
    );
  }
  
  @override
  String toString() {
    return 'CinetPayTransaction(id: $id, amount: $amount $currency, status: ${status.name})';
  }
  
  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is CinetPayTransaction && other.id == id;
  }
  
  @override
  int get hashCode => id.hashCode;
}

/// Modèle pour les résultats de paiement
class PaymentResult {
  final bool success;
  final String message;
  final CinetPayTransaction? transaction;
  final Map<String, dynamic>? additionalData;
  
  PaymentResult({
    required this.success,
    required this.message,
    this.transaction,
    this.additionalData,
  });
  
  factory PaymentResult.success({
    required String message,
    CinetPayTransaction? transaction,
    Map<String, dynamic>? additionalData,
  }) {
    return PaymentResult(
      success: true,
      message: message,
      transaction: transaction,
      additionalData: additionalData,
    );
  }
  
  factory PaymentResult.failure({
    required String message,
    CinetPayTransaction? transaction,
    Map<String, dynamic>? additionalData,
  }) {
    return PaymentResult(
      success: false,
      message: message,
      transaction: transaction,
      additionalData: additionalData,
    );
  }
  
  Map<String, dynamic> toJson() {
    return {
      'success': success,
      'message': message,
      'transaction': transaction?.toJson(),
      'additionalData': additionalData,
    };
  }
  
  factory PaymentResult.fromJson(Map<String, dynamic> json) {
    return PaymentResult(
      success: json['success'],
      message: json['message'],
      transaction: json['transaction'] != null 
          ? CinetPayTransaction.fromJson(json['transaction'])
          : null,
      additionalData: json['additionalData'],
    );
  }
}
