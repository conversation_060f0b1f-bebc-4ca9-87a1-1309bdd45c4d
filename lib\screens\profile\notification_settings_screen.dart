import 'package:flutter/material.dart';
import 'package:callitris/utils/appTheme.dart';

class NotificationSettingsScreen extends StatefulWidget {
  const NotificationSettingsScreen({super.key});

  @override
  State<NotificationSettingsScreen> createState() => _NotificationSettingsScreenState();
}

class _NotificationSettingsScreenState extends State<NotificationSettingsScreen> {
  // États des paramètres de notification
  bool _tontineReminders = true;
  bool _paymentNotifications = true;
  bool _newTontineNotifications = true;
  bool _marketingNotifications = false;
  bool _appUpdates = true;

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Paramètres de notification'),
      ),
      body: SingleChildScrollView(
        child: Column(
          children: [
            _buildHeader(),
            _buildNotificationSettings(),
          ],
        ),
      ),
    );
  }

  Widget _buildHeader() {
    return Container(
      padding: const EdgeInsets.all(16),
      color: AppTheme.color.primaryColor,
      child: Row(
        children: [
          Container(
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: Colors.white.withOpacity(0.2),
              shape: BoxShape.circle,
            ),
            child: const Icon(
              Icons.notifications,
              color: Colors.white,
              size: 28,
            ),
          ),
          const SizedBox(width: 16),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                const Text(
                  'Gérez vos notifications',
                  style: TextStyle(
                    color: Colors.white,
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  'Personnalisez les alertes que vous souhaitez recevoir',
                  style: TextStyle(
                    color: Colors.white.withOpacity(0.8),
                    fontSize: 14,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildNotificationSettings() {
    return Card(
      margin: const EdgeInsets.all(16),
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Notifications',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
                color: AppTheme.color.primaryColor,
              ),
            ),
            const SizedBox(height: 16),
            _buildSwitchTile(
              'Rappels de cotisation',
              'Recevez des rappels pour vos paiements de cotisation',
              _tontineReminders,
              (value) {
                setState(() {
                  _tontineReminders = value;
                });
              },
            ),
            const Divider(),
            _buildSwitchTile(
              'Notifications de paiement',
              'Soyez informé des paiements reçus et effectués',
              _paymentNotifications,
              (value) {
                setState(() {
                  _paymentNotifications = value;
                });
              },
            ),
            const Divider(),
            _buildSwitchTile(
              'Nouvelles cotisation',
              'Soyez informé des nouvelles cotisation disponibles',
              _newTontineNotifications,
              (value) {
                setState(() {
                  _newTontineNotifications = value;
                });
              },
            ),
            const Divider(),
            _buildSwitchTile(
              'Offres et promotions',
              'Recevez des informations sur nos offres spéciales',
              _marketingNotifications,
              (value) {
                setState(() {
                  _marketingNotifications = value;
                });
              },
            ),
            const Divider(),
            _buildSwitchTile(
              'Mises à jour de l\'application',
              'Soyez informé des nouvelles fonctionnalités',
              _appUpdates,
              (value) {
                setState(() {
                  _appUpdates = value;
                });
              },
            ),
            const SizedBox(height: 24),
            ElevatedButton(
              onPressed: () {
                // Logique pour sauvegarder les paramètres
                ScaffoldMessenger.of(context).showSnackBar(
                  SnackBar(
                    content: const Text('Paramètres de notification enregistrés'),
                    backgroundColor: AppTheme.color.greenColor,
                  ),
                );
              },
              style: ElevatedButton.styleFrom(
                minimumSize: const Size(double.infinity, 50),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(10),
                ),
              ),
              child: const Text('Enregistrer les modifications'),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSwitchTile(
    String title,
    String subtitle,
    bool value,
    ValueChanged<bool> onChanged,
  ) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  title,
                  style: const TextStyle(
                    fontWeight: FontWeight.bold,
                    fontSize: 16,
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  subtitle,
                  style: TextStyle(
                    color: AppTheme.color.brunGris,
                    fontSize: 14,
                  ),
                ),
              ],
            ),
          ),
          Switch(
            value: value,
            onChanged: onChanged,
            activeColor: AppTheme.color.primaryColor,
          ),
        ],
      ),
    );
  }
}