import 'package:flutter/material.dart';
import 'package:callitris/config/cinetpay_config.dart';

/// Gestionnaire d'erreurs pour les paiements CinetPay
class PaymentErrorHandler {
  /// Mappe les codes d'erreur CinetPay vers des messages utilisateur
  static String getErrorMessage(dynamic error) {
    if (error == null) {
      return CinetPayConfig.errorMessages['UNKNOWN_ERROR']!;
    }

    // Si c'est une Map (réponse d'erreur structurée)
    if (error is Map<String, dynamic>) {
      final code = error['code']?.toString();
      final message = error['message']?.toString();
      final description = error['description']?.toString();

      // Codes d'erreur spécifiques CinetPay
      switch (code) {
        case '01':
          return 'Paiement refusé par l\'opérateur';
        case '02':
          return 'Fonds insuffisants';
        case '03':
          return 'Numéro de téléphone invalide';
        case '04':
          return 'Transaction expirée';
        case '05':
          return 'Transaction annulée par l\'utilisateur';
        case '06':
          return 'Erreur réseau';
        case '07':
          return 'Service temporairement indisponible';
        default:
          return description ??
              message ??
              CinetPayConfig.errorMessages['UNKNOWN_ERROR']!;
      }
    }

    // Si c'est une chaîne
    if (error is String) {
      final errorLower = error.toLowerCase();

      if (errorLower.contains('insufficient') || errorLower.contains('fonds')) {
        return CinetPayConfig.errorMessages['INSUFFICIENT_FUNDS']!;
      } else if (errorLower.contains('invalid') ||
          errorLower.contains('invalide')) {
        return CinetPayConfig.errorMessages['INVALID_PHONE']!;
      } else if (errorLower.contains('network') ||
          errorLower.contains('réseau')) {
        return CinetPayConfig.errorMessages['NETWORK_ERROR']!;
      } else if (errorLower.contains('timeout') ||
          errorLower.contains('expired')) {
        return CinetPayConfig.errorMessages['TIMEOUT']!;
      } else if (errorLower.contains('cancelled') ||
          errorLower.contains('annulé')) {
        return CinetPayConfig.errorMessages['PAYMENT_CANCELLED']!;
      }

      return error;
    }

    return CinetPayConfig.errorMessages['UNKNOWN_ERROR']!;
  }

  /// Affiche une notification d'erreur
  static void showErrorNotification(BuildContext context, dynamic error) {
    final message = getErrorMessage(error);

    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Row(
          children: [
            const Icon(Icons.error_outline, color: Colors.white, size: 20),
            const SizedBox(width: 8),
            Expanded(
              child: Text(
                message,
                style: const TextStyle(color: Colors.white, fontSize: 14),
              ),
            ),
          ],
        ),
        backgroundColor: Colors.red,
        duration: const Duration(seconds: 4),
        behavior: SnackBarBehavior.floating,
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
        action: SnackBarAction(
          label: 'OK',
          textColor: Colors.white,
          onPressed: () {
            ScaffoldMessenger.of(context).hideCurrentSnackBar();
          },
        ),
      ),
    );
  }

  /// Affiche une notification de succès
  static void showSuccessNotification(BuildContext context, String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Row(
          children: [
            const Icon(
              Icons.check_circle_outline,
              color: Colors.white,
              size: 20,
            ),
            const SizedBox(width: 8),
            Expanded(
              child: Text(
                message,
                style: const TextStyle(color: Colors.white, fontSize: 14),
              ),
            ),
          ],
        ),
        backgroundColor: const Color(0xFF4CAF50),
        duration: const Duration(seconds: 3),
        behavior: SnackBarBehavior.floating,
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
      ),
    );
  }

  /// Affiche une notification d'information
  static void showInfoNotification(BuildContext context, String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Row(
          children: [
            const Icon(Icons.info_outline, color: Colors.white, size: 20),
            const SizedBox(width: 8),
            Expanded(
              child: Text(
                message,
                style: const TextStyle(color: Colors.white, fontSize: 14),
              ),
            ),
          ],
        ),
        backgroundColor: Colors.blue,
        duration: const Duration(seconds: 3),
        behavior: SnackBarBehavior.floating,
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
      ),
    );
  }

  /// Affiche un dialog de confirmation pour retry
  static Future<bool> showRetryDialog(
    BuildContext context,
    String error,
  ) async {
    return await showDialog<bool>(
          context: context,
          barrierDismissible: false,
          builder: (BuildContext context) {
            return PopScope(
              canPop: false,
              child: AlertDialog(
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(16),
                ),
                title: Row(
                  children: [
                    Icon(Icons.error_outline, color: Colors.red, size: 24),
                    const SizedBox(width: 8),
                    const Text(
                      'Erreur de paiement',
                      style: TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                  ],
                ),
                content: Column(
                  mainAxisSize: MainAxisSize.min,
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      getErrorMessage(error),
                      style: const TextStyle(fontSize: 14, height: 1.4),
                    ),
                    const SizedBox(height: 16),
                    const Text(
                      'Voulez-vous réessayer ?',
                      style: TextStyle(
                        fontSize: 14,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ],
                ),
                actions: [
                  TextButton(
                    onPressed: () => Navigator.of(context).pop(false),
                    child: Text(
                      'Annuler',
                      style: TextStyle(color: Colors.grey.shade600),
                    ),
                  ),
                  ElevatedButton(
                    onPressed: () => Navigator.of(context).pop(true),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: const Color(0xFF4CAF50),
                      foregroundColor: Colors.white,
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(8),
                      ),
                    ),
                    child: const Text('Réessayer'),
                  ),
                ],
              ),
            );
          },
        ) ??
        false;
  }

  /// Vérifie si une erreur est récupérable (peut être retentée)
  static bool isRetryableError(dynamic error) {
    if (error is Map<String, dynamic>) {
      final code = error['code']?.toString();
      // Codes d'erreur non récupérables
      final nonRetryableCodes = [
        '02',
        '03',
        '05',
      ]; // Fonds insuffisants, numéro invalide, annulé
      return !nonRetryableCodes.contains(code);
    }

    if (error is String) {
      final errorLower = error.toLowerCase();
      // Erreurs non récupérables
      if (errorLower.contains('insufficient') ||
          errorLower.contains('invalid') ||
          errorLower.contains('cancelled')) {
        return false;
      }
    }

    return true; // Par défaut, on peut réessayer
  }

  /// Affiche un indicateur de chargement
  static void showLoadingDialog(BuildContext context, {String? message}) {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (BuildContext context) {
        return Dialog(
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(16),
          ),
          child: Padding(
            padding: const EdgeInsets.all(24),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                const CircularProgressIndicator(color: Color(0xFF4CAF50)),
                const SizedBox(height: 16),
                Text(
                  message ?? 'Traitement en cours...',
                  style: const TextStyle(
                    fontSize: 14,
                    fontWeight: FontWeight.w500,
                  ),
                  textAlign: TextAlign.center,
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  /// Ferme le dialog de chargement
  static void hideLoadingDialog(BuildContext context) {
    if (Navigator.canPop(context)) {
      Navigator.pop(context);
    }
  }
}
