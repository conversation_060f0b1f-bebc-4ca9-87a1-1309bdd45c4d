-- Script SQL pour créer les tables nécessaires aux paiements Wave
-- Base de données: Callitris
-- 
-- Exécutez ce script sur votre base de données MySQL/MariaDB

-- Table pour stocker les transactions Wave
CREATE TABLE IF NOT EXISTS `wave_transactions` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `transaction_id` varchar(255) NOT NULL UNIQUE,
  `order_id` varchar(255) DEFAULT NULL,
  `customer_id` varchar(255) DEFAULT NULL,
  `customer_phone` varchar(20) DEFAULT NULL,
  `amount` decimal(10,2) NOT NULL,
  `currency` varchar(3) DEFAULT 'XOF',
  `status` enum('pending','success','failed','cancelled') DEFAULT 'pending',
  `wave_response` text DEFAULT NULL,
  `callback_received_at` timestamp NULL DEFAULT NULL,
  `created_at` timestamp DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  <PERSON><PERSON>AR<PERSON>EY (`id`),
  <PERSON><PERSON>Y `idx_transaction_id` (`transaction_id`),
  KEY `idx_order_id` (`order_id`),
  KEY `idx_customer_id` (`customer_id`),
  KEY `idx_status` (`status`),
  KEY `idx_created_at` (`created_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Table pour les logs de callbacks Wave
CREATE TABLE IF NOT EXISTS `wave_callback_logs` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `transaction_id` varchar(255) DEFAULT NULL,
  `callback_type` varchar(50) NOT NULL,
  `request_method` varchar(10) NOT NULL,
  `request_data` text DEFAULT NULL,
  `response_data` text DEFAULT NULL,
  `ip_address` varchar(45) DEFAULT NULL,
  `user_agent` text DEFAULT NULL,
  `status_code` int(3) DEFAULT NULL,
  `processing_time` decimal(8,3) DEFAULT NULL,
  `created_at` timestamp DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `idx_transaction_id` (`transaction_id`),
  KEY `idx_callback_type` (`callback_type`),
  KEY `idx_created_at` (`created_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Mise à jour de la table commandes pour supporter Wave
ALTER TABLE `commandes` 
ADD COLUMN IF NOT EXISTS `payment_method` varchar(50) DEFAULT NULL AFTER `payment_status`,
ADD COLUMN IF NOT EXISTS `payment_transaction_id` varchar(255) DEFAULT NULL AFTER `payment_method`,
ADD COLUMN IF NOT EXISTS `payment_amount` decimal(10,2) DEFAULT NULL AFTER `payment_transaction_id`,
ADD COLUMN IF NOT EXISTS `payment_date` timestamp NULL DEFAULT NULL AFTER `payment_amount`,
ADD COLUMN IF NOT EXISTS `payment_reference` varchar(255) DEFAULT NULL AFTER `payment_date`;

-- Index pour optimiser les requêtes de paiement
ALTER TABLE `commandes`
ADD INDEX IF NOT EXISTS `idx_payment_transaction_id` (`payment_transaction_id`),
ADD INDEX IF NOT EXISTS `idx_payment_reference` (`payment_reference`),
ADD INDEX IF NOT EXISTS `idx_payment_status` (`payment_status`);

-- Table pour stocker les tentatives de deep links
CREATE TABLE IF NOT EXISTS `deep_link_attempts` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `transaction_id` varchar(255) DEFAULT NULL,
  `deep_link_url` text NOT NULL,
  `callback_type` varchar(50) NOT NULL,
  `user_agent` text DEFAULT NULL,
  `ip_address` varchar(45) DEFAULT NULL,
  `success` tinyint(1) DEFAULT 0,
  `error_message` text DEFAULT NULL,
  `created_at` timestamp DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `idx_transaction_id` (`transaction_id`),
  KEY `idx_callback_type` (`callback_type`),
  KEY `idx_created_at` (`created_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Vues pour faciliter les requêtes

-- Vue pour les transactions Wave avec détails des commandes
CREATE OR REPLACE VIEW `v_wave_transactions_details` AS
SELECT 
    wt.id,
    wt.transaction_id,
    wt.order_id,
    wt.customer_id,
    wt.customer_phone,
    wt.amount,
    wt.currency,
    wt.status as wave_status,
    wt.created_at as wave_created_at,
    wt.updated_at as wave_updated_at,
    c.id as commande_id,
    c.payment_status as commande_payment_status,
    c.payment_amount as commande_payment_amount,
    c.payment_date as commande_payment_date,
    cl.nom as client_nom,
    cl.telephone as client_telephone
FROM wave_transactions wt
LEFT JOIN commandes c ON wt.order_id = c.id
LEFT JOIN clients cl ON wt.customer_id = cl.id_client;

-- Vue pour les statistiques des paiements Wave
CREATE OR REPLACE VIEW `v_wave_payment_stats` AS
SELECT 
    DATE(created_at) as payment_date,
    status,
    COUNT(*) as transaction_count,
    SUM(amount) as total_amount,
    AVG(amount) as average_amount,
    MIN(amount) as min_amount,
    MAX(amount) as max_amount
FROM wave_transactions
GROUP BY DATE(created_at), status
ORDER BY payment_date DESC, status;

-- Procédure stockée pour nettoyer les anciens logs
DELIMITER //
CREATE PROCEDURE IF NOT EXISTS `CleanOldWaveLogs`()
BEGIN
    -- Supprimer les logs de callbacks plus anciens que 30 jours
    DELETE FROM wave_callback_logs 
    WHERE created_at < DATE_SUB(NOW(), INTERVAL 30 DAY);
    
    -- Supprimer les tentatives de deep links plus anciennes que 7 jours
    DELETE FROM deep_link_attempts 
    WHERE created_at < DATE_SUB(NOW(), INTERVAL 7 DAY);
    
    -- Log de nettoyage
    INSERT INTO wave_callback_logs (
        callback_type, 
        request_method, 
        request_data, 
        status_code
    ) VALUES (
        'cleanup', 
        'SYSTEM', 
        CONCAT('Cleaned logs older than 30 days at ', NOW()), 
        200
    );
END //
DELIMITER ;

-- Événement pour nettoyer automatiquement les logs (optionnel)
-- Décommentez si vous voulez un nettoyage automatique
/*
CREATE EVENT IF NOT EXISTS `auto_clean_wave_logs`
ON SCHEDULE EVERY 1 DAY
STARTS CURRENT_TIMESTAMP
DO
  CALL CleanOldWaveLogs();
*/

-- Insertion de données de test (optionnel)
-- Décommentez pour insérer des données de test
/*
INSERT INTO wave_transactions (
    transaction_id, 
    order_id, 
    customer_id, 
    customer_phone, 
    amount, 
    status
) VALUES 
('wave_test_001', '1', 'client_001', '+22501020304', 1000.00, 'success'),
('wave_test_002', '2', 'client_002', '+22501020305', 2500.00, 'pending'),
('wave_test_003', '3', 'client_003', '+22501020306', 500.00, 'failed');
*/

-- Requêtes utiles pour le monitoring

-- Voir toutes les transactions Wave récentes
-- SELECT * FROM v_wave_transactions_details ORDER BY wave_created_at DESC LIMIT 10;

-- Voir les statistiques du jour
-- SELECT * FROM v_wave_payment_stats WHERE payment_date = CURDATE();

-- Voir les transactions en échec
-- SELECT * FROM wave_transactions WHERE status = 'failed' ORDER BY created_at DESC;

-- Voir les callbacks récents
-- SELECT * FROM wave_callback_logs ORDER BY created_at DESC LIMIT 20;
