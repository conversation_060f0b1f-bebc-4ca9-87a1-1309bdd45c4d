import 'package:callitris/config/api_config.dart';

/// Configuration centralisée pour CinetPay
class CinetPayConfig {
  // Clés d'API CinetPay
  static const String apiKey = ApiConfig.cinetPayApiKey;
  static const String siteId = ApiConfig.cinetPaySiteId;
  static const String secretKey = ApiConfig.cinetPaySecretKey;
  
  // Mode sandbox (true pour les tests, false pour la production)
  static const bool sandboxMode = ApiConfig.cinetPaySandboxMode;
  
  // URLs de callback
  static const String returnUrl = ApiConfig.cinetPayReturnUrl;
  static const String notifyUrl = ApiConfig.cinetPayNotifyUrl;
  static const String cancelUrl = ApiConfig.cinetPayCancelUrl;
  
  // Devises supportées par CinetPay
  static const Map<String, String> supportedCurrencies = {
    'XOF': 'Franc CFA (BCEAO)',
    'XAF': 'Franc CFA (BEAC)',
    'CDF': 'Franc <PERSON>lai<PERSON>',
    'GNF': 'Franc <PERSON>',
    'USD': 'Dollar Américain',
    'EUR': 'Euro',
  };
  
  // Devise par défaut pour votre application
  static const String defaultCurrency = 'XOF';
  
  // Montant minimum pour un paiement (en centimes)
  static const int minimumAmount = 100; // 1 XOF
  
  // Timeout pour les requêtes API (en secondes)
  static const int apiTimeout = 30;
  
  // Méthodes de paiement disponibles
  static const List<String> availablePaymentMethods = [
    'MOBILE_MONEY',
    'CREDIT_CARD',
    'PAYPAL',
    'BANK_TRANSFER',
  ];
  
  // Configuration des logos des opérateurs
  static const Map<String, String> operatorLogos = {
    'ORANGE_MONEY_CI': 'assets/images/orange_money.png',
    'MOOV_MONEY_CI': 'assets/images/moov_money.png',
    'MTN_MONEY_CI': 'assets/images/mtn_money.png',
    'WAVE_CI': 'assets/images/wave.png',
  };
  
  // Messages d'erreur personnalisés
  static const Map<String, String> errorMessages = {
    'PAYMENT_FAILED': 'Le paiement a échoué. Veuillez réessayer.',
    'PAYMENT_CANCELLED': 'Le paiement a été annulé.',
    'INSUFFICIENT_FUNDS': 'Fonds insuffisants pour effectuer cette transaction.',
    'INVALID_PHONE': 'Numéro de téléphone invalide.',
    'NETWORK_ERROR': 'Erreur de connexion. Vérifiez votre connexion internet.',
    'TIMEOUT': 'La transaction a expiré. Veuillez réessayer.',
    'UNKNOWN_ERROR': 'Une erreur inattendue s\'est produite.',
  };
  
  // Messages de succès
  static const Map<String, String> successMessages = {
    'PAYMENT_SUCCESS': 'Paiement effectué avec succès !',
    'PAYMENT_PENDING': 'Paiement en cours de traitement...',
  };
  
  /// Valide si une devise est supportée
  static bool isCurrencySupported(String currency) {
    return supportedCurrencies.containsKey(currency.toUpperCase());
  }
  
  /// Valide si un montant est valide (supérieur au minimum)
  static bool isAmountValid(double amount) {
    return (amount * 100).round() >= minimumAmount;
  }
  
  /// Convertit un montant en centimes pour l'API CinetPay
  static int convertToCentimes(double amount) {
    return (amount * 100).round();
  }
  
  /// Convertit un montant de centimes vers la devise
  static double convertFromCentimes(int centimes) {
    return centimes / 100.0;
  }
  
  /// Génère un ID de transaction unique
  static String generateTransactionId() {
    final timestamp = DateTime.now().millisecondsSinceEpoch;
    return 'CALLITRIS_${timestamp}_${DateTime.now().microsecond}';
  }
  
  /// Valide un numéro de téléphone pour Mobile Money
  static bool isValidPhoneNumber(String phone) {
    // Regex pour les numéros de téléphone ivoiriens
    final RegExp phoneRegex = RegExp(r'^(\+225|225)?[0-9]{8,10}$');
    return phoneRegex.hasMatch(phone.replaceAll(' ', ''));
  }
  
  /// Formate un numéro de téléphone
  static String formatPhoneNumber(String phone) {
    String cleaned = phone.replaceAll(RegExp(r'[^\d+]'), '');
    if (cleaned.startsWith('225')) {
      cleaned = '+$cleaned';
    } else if (!cleaned.startsWith('+225')) {
      cleaned = '+225$cleaned';
    }
    return cleaned;
  }
}
