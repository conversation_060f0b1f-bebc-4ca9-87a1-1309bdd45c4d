import 'package:intl/intl.dart';

class DateUtils {
  // Formater une date au format français (JJ/MM/AAAA)
  static String formatToFrenchDate(dynamic dateInput) {
    if (dateInput == null) return 'N/A';
    
    try {
      DateTime date;
      
      // Si c'est déjà un DateTime
      if (dateInput is DateTime) {
        date = dateInput;
      } 
      // Si c'est une chaîne de caractères
      else if (dateInput is String) {
        // Si la chaîne est vide
        if (dateInput.trim().isEmpty) return 'N/A';
        
        // Si la chaîne est déjà au format JJ/MM/AAAA
        if (dateInput.contains('/')) {
          final parts = dateInput.split('/');
          if (parts.length == 3) {
            // Vérifier si c'est déjà au bon format
            if (parts[0].length <= 2 && parts[1].length <= 2 && parts[2].length == 4) {
              return dateInput; // Déjà au bon format
            }
          }
        }
        
        // Essayer de parser la date
        date = DateTime.parse(dateInput);
      } 
      // Si c'est un timestamp
      else if (dateInput is int) {
        date = DateTime.fromMillisecondsSinceEpoch(dateInput);
      } 
      else {
        return 'N/A';
      }
      
      // Formater la date
      return DateFormat('dd/MM/yyyy').format(date);
    } catch (e) {
      print('Erreur lors du formatage de la date: $e');
      return 'N/A';
    }
  }
}