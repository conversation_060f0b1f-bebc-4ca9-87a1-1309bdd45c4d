import 'package:flutter/material.dart';

/// Animations optimisées pour de meilleures performances
class OptimizedAnimations {
  // Durées d'animation optimisées
  static const Duration fast = Duration(milliseconds: 150);
  static const Duration normal = Duration(milliseconds: 250);
  static const Duration slow = Duration(milliseconds: 400);

  // Courbes d'animation optimisées
  static const Curve easeInOutQuart = Cubic(0.77, 0, 0.175, 1);
  static const Curve easeOutQuart = Cubic(0.25, 0.46, 0.45, 0.94);
  static const Curve easeInQuart = Cubic(0.55, 0.055, 0.675, 0.19);

  /// Transition de page optimisée
  static PageRouteBuilder<T> createPageRoute<T>({
    required Widget page,
    RouteSettings? settings,
    Duration duration = normal,
    AnimationType type = AnimationType.slideFromRight,
  }) {
    return PageRouteBuilder<T>(
      settings: settings,
      pageBuilder: (context, animation, secondaryAnimation) => page,
      transitionDuration: duration,
      reverseTransitionDuration: duration,
      transitionsBuilder: (context, animation, secondaryAnimation, child) {
        return _buildTransition(type, animation, secondaryAnimation, child);
      },
    );
  }

  /// Construit la transition selon le type
  static Widget _buildTransition(
    AnimationType type,
    Animation<double> animation,
    Animation<double> secondaryAnimation,
    Widget child,
  ) {
    switch (type) {
      case AnimationType.slideFromRight:
        return SlideTransition(
          position: Tween<Offset>(
            begin: const Offset(1.0, 0.0),
            end: Offset.zero,
          ).animate(CurvedAnimation(
            parent: animation,
            curve: easeOutQuart,
          )),
          child: child,
        );

      case AnimationType.slideFromBottom:
        return SlideTransition(
          position: Tween<Offset>(
            begin: const Offset(0.0, 1.0),
            end: Offset.zero,
          ).animate(CurvedAnimation(
            parent: animation,
            curve: easeOutQuart,
          )),
          child: child,
        );

      case AnimationType.fadeIn:
        return FadeTransition(
          opacity: CurvedAnimation(
            parent: animation,
            curve: easeInOutQuart,
          ),
          child: child,
        );

      case AnimationType.scaleIn:
        return ScaleTransition(
          scale: Tween<double>(
            begin: 0.8,
            end: 1.0,
          ).animate(CurvedAnimation(
            parent: animation,
            curve: easeOutQuart,
          )),
          child: FadeTransition(
            opacity: animation,
            child: child,
          ),
        );

      case AnimationType.none:
        return child;
    }
  }
}

/// Types d'animation disponibles
enum AnimationType {
  slideFromRight,
  slideFromBottom,
  fadeIn,
  scaleIn,
  none,
}

/// Widget d'animation d'apparition optimisé
class OptimizedFadeIn extends StatefulWidget {
  final Widget child;
  final Duration duration;
  final Duration delay;
  final Curve curve;
  final bool animate;

  const OptimizedFadeIn({
    super.key,
    required this.child,
    this.duration = OptimizedAnimations.normal,
    this.delay = Duration.zero,
    this.curve = OptimizedAnimations.easeOutQuart,
    this.animate = true,
  });

  @override
  State<OptimizedFadeIn> createState() => _OptimizedFadeInState();
}

class _OptimizedFadeInState extends State<OptimizedFadeIn>
    with SingleTickerProviderStateMixin {
  late AnimationController _controller;
  late Animation<double> _animation;

  @override
  void initState() {
    super.initState();
    _controller = AnimationController(
      duration: widget.duration,
      vsync: this,
    );
    _animation = CurvedAnimation(
      parent: _controller,
      curve: widget.curve,
    );

    if (widget.animate) {
      Future.delayed(widget.delay, () {
        if (mounted) {
          _controller.forward();
        }
      });
    } else {
      _controller.value = 1.0;
    }
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return FadeTransition(
      opacity: _animation,
      child: widget.child,
    );
  }
}

/// Widget d'animation de glissement optimisé
class OptimizedSlideIn extends StatefulWidget {
  final Widget child;
  final Duration duration;
  final Duration delay;
  final Curve curve;
  final Offset begin;
  final bool animate;

  const OptimizedSlideIn({
    super.key,
    required this.child,
    this.duration = OptimizedAnimations.normal,
    this.delay = Duration.zero,
    this.curve = OptimizedAnimations.easeOutQuart,
    this.begin = const Offset(0.0, 1.0),
    this.animate = true,
  });

  @override
  State<OptimizedSlideIn> createState() => _OptimizedSlideInState();
}

class _OptimizedSlideInState extends State<OptimizedSlideIn>
    with SingleTickerProviderStateMixin {
  late AnimationController _controller;
  late Animation<Offset> _animation;

  @override
  void initState() {
    super.initState();
    _controller = AnimationController(
      duration: widget.duration,
      vsync: this,
    );
    _animation = Tween<Offset>(
      begin: widget.begin,
      end: Offset.zero,
    ).animate(CurvedAnimation(
      parent: _controller,
      curve: widget.curve,
    ));

    if (widget.animate) {
      Future.delayed(widget.delay, () {
        if (mounted) {
          _controller.forward();
        }
      });
    } else {
      _controller.value = 1.0;
    }
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return SlideTransition(
      position: _animation,
      child: widget.child,
    );
  }
}

/// Widget d'animation de mise à l'échelle optimisé
class OptimizedScaleIn extends StatefulWidget {
  final Widget child;
  final Duration duration;
  final Duration delay;
  final Curve curve;
  final double begin;
  final bool animate;

  const OptimizedScaleIn({
    super.key,
    required this.child,
    this.duration = OptimizedAnimations.normal,
    this.delay = Duration.zero,
    this.curve = OptimizedAnimations.easeOutQuart,
    this.begin = 0.8,
    this.animate = true,
  });

  @override
  State<OptimizedScaleIn> createState() => _OptimizedScaleInState();
}

class _OptimizedScaleInState extends State<OptimizedScaleIn>
    with SingleTickerProviderStateMixin {
  late AnimationController _controller;
  late Animation<double> _scaleAnimation;
  late Animation<double> _opacityAnimation;

  @override
  void initState() {
    super.initState();
    _controller = AnimationController(
      duration: widget.duration,
      vsync: this,
    );
    
    _scaleAnimation = Tween<double>(
      begin: widget.begin,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _controller,
      curve: widget.curve,
    ));
    
    _opacityAnimation = CurvedAnimation(
      parent: _controller,
      curve: widget.curve,
    );

    if (widget.animate) {
      Future.delayed(widget.delay, () {
        if (mounted) {
          _controller.forward();
        }
      });
    } else {
      _controller.value = 1.0;
    }
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return ScaleTransition(
      scale: _scaleAnimation,
      child: FadeTransition(
        opacity: _opacityAnimation,
        child: widget.child,
      ),
    );
  }
}

/// Animation de liste échelonnée optimisée
class OptimizedStaggeredList extends StatefulWidget {
  final List<Widget> children;
  final Duration staggerDelay;
  final Duration itemDuration;
  final Curve curve;
  final bool animate;

  const OptimizedStaggeredList({
    super.key,
    required this.children,
    this.staggerDelay = const Duration(milliseconds: 50),
    this.itemDuration = OptimizedAnimations.normal,
    this.curve = OptimizedAnimations.easeOutQuart,
    this.animate = true,
  });

  @override
  State<OptimizedStaggeredList> createState() => _OptimizedStaggeredListState();
}

class _OptimizedStaggeredListState extends State<OptimizedStaggeredList> {
  @override
  Widget build(BuildContext context) {
    return Column(
      children: widget.children.asMap().entries.map((entry) {
        final index = entry.key;
        final child = entry.value;
        
        return OptimizedSlideIn(
          delay: widget.animate 
            ? Duration(milliseconds: index * widget.staggerDelay.inMilliseconds)
            : Duration.zero,
          duration: widget.itemDuration,
          curve: widget.curve,
          animate: widget.animate,
          child: child,
        );
      }).toList(),
    );
  }
}
