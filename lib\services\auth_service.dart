import 'dart:convert';
import 'dart:async'; // Pour TimeoutException
import 'package:http/http.dart' as http;
import 'package:shared_preferences/shared_preferences.dart';
import 'package:rxdart/rxdart.dart';
import '../config/api_config.dart';
import 'app_state_service.dart';
import 'network_service.dart';

class AuthService {
  static const String _tag = '[AuthService]';

  // Streams pour la réactivité
  static final BehaviorSubject<bool> _isLoggedInSubject =
      BehaviorSubject<bool>.seeded(false);
  static final BehaviorSubject<Map<String, dynamic>?> _userDataSubject =
      BehaviorSubject<Map<String, dynamic>?>.seeded(null);
  static final BehaviorSubject<String?> _authTokenSubject =
      BehaviorSubject<String?>.seeded(null);

  // Streams publics
  static Stream<bool> get isLoggedInStream => _isLoggedInSubject.stream;
  static Stream<Map<String, dynamic>?> get userDataStream =>
      _userDataSubject.stream;
  static Stream<String?> get authTokenStream => _authTokenSubject.stream;

  // Getters réactifs
  static bool get isLoggedInValue => _isLoggedInSubject.value;
  static Map<String, dynamic>? get userDataValue => _userDataSubject.value;
  static String? get authTokenValue => _authTokenSubject.value;

  // Timer pour le rafraîchissement automatique
  static Timer? _refreshTimer;

  /// Initialise le service d'authentification réactif
  static Future<void> initialize() async {
    print('$_tag: Initialisation du service d\'authentification réactif...');

    // Charger l'état initial depuis le stockage local
    await _loadInitialState();

    // Configurer le rafraîchissement automatique
    _setupAutoRefresh();

    print('$_tag: Service d\'authentification initialisé');
  }

  /// Charge l'état initial depuis le stockage local
  static Future<void> _loadInitialState() async {
    try {
      final loggedIn = await isLoggedIn();
      final userData = await getUserData();
      final token = await getAuthToken();

      _isLoggedInSubject.add(loggedIn);
      _userDataSubject.add(userData);
      _authTokenSubject.add(token);

      // Mettre à jour le service d'état global
      if (userData != null) {
        AppStateService.instance.updateUserData(userData);
      }
    } catch (e) {
      print('$_tag: Erreur lors du chargement de l\'état initial: $e');
    }
  }

  /// Configure le rafraîchissement automatique des données utilisateur
  static void _setupAutoRefresh() {
    _refreshTimer?.cancel();

    // Rafraîchir les données utilisateur toutes les 5 minutes si connecté
    _refreshTimer = Timer.periodic(const Duration(minutes: 5), (timer) async {
      if (_isLoggedInSubject.value) {
        await refreshUserData(silent: true);
      }
    });
  }

  /// Rafraîchit les données utilisateur
  static Future<void> refreshUserData({bool silent = false}) async {
    try {
      if (!silent) {
        AppStateService.instance.setLoading(true);
      }

      final userData = await getUserData();
      if (userData != null) {
        _userDataSubject.add(userData);
        AppStateService.instance.updateUserData(userData);
      }
    } catch (e) {
      print(
        '$_tag: Erreur lors du rafraîchissement des données utilisateur: $e',
      );
      if (!silent) {
        AppStateService.instance.setError(
          'Erreur lors du rafraîchissement du profil',
        );
      }
    } finally {
      if (!silent) {
        AppStateService.instance.setLoading(false);
      }
    }
  }

  /// Met à jour l'état de connexion de manière réactive
  static Future<void> _updateLoginState(
    bool isLoggedIn, {
    Map<String, dynamic>? userData,
    String? token,
  }) async {
    _isLoggedInSubject.add(isLoggedIn);

    if (isLoggedIn) {
      if (userData != null) {
        _userDataSubject.add(userData);
        AppStateService.instance.updateUserData(userData);
      }
      if (token != null) {
        _authTokenSubject.add(token);
      }

      // Démarrer le rafraîchissement automatique
      _setupAutoRefresh();
    } else {
      _userDataSubject.add(null);
      _authTokenSubject.add(null);
      AppStateService.instance.updateUserData(null);

      // Arrêter le rafraîchissement automatique
      _refreshTimer?.cancel();
    }
  }

  // Envoyer l'OTP au numéro de téléphone
  static Future<Map<String, dynamic>> sendOtp(String phoneNumber) async {
    print('phoneNumber: $phoneNumber');
    try {
      final response = await http
          .post(
            Uri.parse(ApiConfig.sendOtpEndpoint),
            headers: {'Content-Type': 'application/json'},
            body: jsonEncode({'telephone': phoneNumber}),
          )
          .timeout(
            const Duration(seconds: 15),
            onTimeout: () {
              throw TimeoutException('La requête a pris trop de temps');
            },
          );

      print('Réponse brute: ${response.body}');

      if (response.statusCode == 200) {
        try {
          final jsonResponse = jsonDecode(response.body);
          print('JSON décodé avec succès: $jsonResponse');
          return jsonResponse;
        } catch (e) {
          print('Erreur lors du décodage JSON: $e');
          print('Contenu de la réponse: ${response.body}');

          // Si la réponse contient "succès" ou "success", on considère que c'est un succès
          if (response.body.toLowerCase().contains('succ')) {
            return {'success': true, 'message': 'OTP envoyé avec succès.'};
          } else {
            return {
              'success': false,
              'message': 'Format de réponse invalide. Veuillez réessayer.',
            };
          }
        }
      } else {
        print('Erreur HTTP: ${response.statusCode}, Body: ${response.body}');
        // Traiter les erreurs HTTP
        Map<String, dynamic> jsonResponse = jsonDecode(response.body);
        if (jsonResponse.containsKey('message')) {
          return {'success': false, 'message': jsonResponse['message']};
        } else {
          return {
            'success': false,
            'message': 'Format de réponse invalide. Veuillez réessayer.',
          };
        }
      }
    } catch (e) {
      print('Exception lors de l\'envoi de l\'OTP: $e');

      // Si l'exception contient un message de succès, on considère que c'est un succès
      if (e.toString().toLowerCase().contains('succ')) {
        return {'success': true, 'message': 'OTP envoyé avec succès.'};
      }

      return {
        'success': false,
        'message':
            'Erreur de connexion. Veuillez vérifier votre connexion internet.',
      };
    }
  }

  // Ajouter cette méthode dans la classe AuthService
  static Future<Map<String, dynamic>> loginWithPassword(
    String phoneNumber,
    String password,
  ) async {
    try {
      print('Tentative de connexion: $phoneNumber');

      // Utiliser le service réseau optimisé
      final responseData = await NetworkService.instance.post(
        ApiConfig.loginEndpoint,
        headers: {'Content-Type': 'application/json'},
        body: {
          'phone': phoneNumber, // Utiliser 'phone' au lieu de 'telephone'
          'password': password,
        },
      );

      print('Réponse traitée: $responseData');

      // Le service réseau retourne déjà les données décodées
      try {
        // Vérifier si la réponse contient des données
        if (responseData.containsKey('data')) {
          final dynamic data = responseData['data'];

          // Si c'est un tableau, prendre le premier élément
          List<dynamic> jsonResponseList;
          if (data is List) {
            jsonResponseList = data;
          } else if (data is String) {
            jsonResponseList = jsonDecode(data);
          } else {
            jsonResponseList = [data];
          }

          if (jsonResponseList.isNotEmpty) {
            final Map<String, dynamic> userData = jsonResponseList[0];

            // Vérifier si les données utilisateur contiennent un ID client et un token
            if (userData.containsKey('id_client') &&
                userData.containsKey('token')) {
              // Sauvegarder les informations de l'utilisateur
              await _saveUserSession(phoneNumber, userData);

              return {'success': true, 'message': 'Connexion réussie'};
            } else {
              return {
                'success': false,
                'message': 'Données utilisateur incomplètes',
              };
            }
          } else {
            return {'success': false, 'message': 'Identifiants incorrects'};
          }
        } else {
          return {'success': false, 'message': 'Réponse invalide du serveur'};
        }
      } catch (e) {
        print('Erreur lors du traitement de la réponse: $e');
        return {'success': false, 'message': 'Format de réponse invalide'};
      }
    } catch (e) {
      print('Exception lors de la connexion: $e');
      return {
        'success': false,
        'message':
            'Erreur de connexion. Veuillez vérifier votre connexion internet.',
      };
    }
  }

  // Vérifier l'OTP
  // Vérifier l'OTP
  static Future<Map<String, dynamic>> verifyOtp(
    String phoneNumber,
    String otp,
  ) async {
    try {
      print('Vérification OTP: Téléphone=$phoneNumber, OTP=$otp');

      final response = await http
          .post(
            Uri.parse(ApiConfig.verifyOtpEndpoint),
            headers: {'Content-Type': 'application/json'},
            body: jsonEncode({'telephone': phoneNumber, 'otp': otp}),
          )
          .timeout(
            const Duration(seconds: 15),
            onTimeout: () {
              throw TimeoutException('La requête a pris trop de temps');
            },
          );

      print('Réponse brute de vérification OTP: ${response.body}');
      print('Status code: ${response.statusCode}');

      // Traiter la réponse JSON, quel que soit le code d'état
      Map<String, dynamic> jsonResponse;
      try {
        jsonResponse = jsonDecode(response.body);
        print('Données utilisateur reçues: $jsonResponse');
      } catch (e) {
        print('Erreur lors du décodage JSON: $e');
        jsonResponse = {
          'success': false,
          'message': 'Format de réponse invalide. Veuillez réessayer.',
        };
      }

      if (response.statusCode == 200) {
        if (jsonResponse.containsKey('id_client') &&
            jsonResponse.containsKey('token')) {
          jsonResponse['success'] = true;

          await _saveUserSession(phoneNumber, jsonResponse);

          print(
            'Données utilisateur sauvegardées avec succès: token=${jsonResponse['token']}',
          );

          return jsonResponse;
        } else if (jsonResponse.containsKey('success')) {
          if (jsonResponse['success'] == true) {
            await _saveUserSession(phoneNumber, jsonResponse);
            print(
              'Session utilisateur sauvegardée avec succès (via success=true)',
            );
          }
          return jsonResponse;
        } else {
          // Si la réponse ne contient ni id_client ni success, mais le statut est 200
          // On considère que c'est un succès
          jsonResponse['success'] = true;
          await _saveUserSession(phoneNumber, jsonResponse);
          print('Session utilisateur sauvegardée avec succès (statut 200)');
          return jsonResponse;
        }
      }
      // Si le statut est 401, l'OTP est invalide ou expiré
      else if (response.statusCode == 401) {
        return {
          'success': false,
          'message': jsonResponse['message'] ?? 'Code OTP invalide ou expiré.',
        };
      }
      // Autres codes d'erreur
      else {
        print('Erreur HTTP: ${response.statusCode}, Body: ${response.body}');
        return {
          'success': false,
          'message':
              jsonResponse['message'] ??
              'Erreur lors de la vérification de l\'OTP (${response.statusCode}).',
        };
      }
    } catch (e) {
      print('Exception lors de la vérification de l\'OTP: $e');

      // Si l'exception contient un message de succès, on considère que c'est un succès
      if (e.toString().toLowerCase().contains('succ')) {
        await _saveUserSession(phoneNumber);
        return {'success': true, 'message': 'OTP vérifié avec succès.'};
      }

      return {
        'success': false,
        'message':
            'Erreur de connexion. Veuillez vérifier votre connexion internet.',
      };
    }
  }

  // Sauvegarder la session utilisateur de manière exhaustive et réactive
  static Future<void> _saveUserSession(
    String phoneNumber, [
    Map<String, dynamic>? userData,
  ]) async {
    final prefs = await SharedPreferences.getInstance();

    // Informations de base
    await prefs.setBool('isLoggedIn', true);
    await prefs.setString('phoneNumber', phoneNumber);
    await prefs.setString('loginTime', DateTime.now().toIso8601String());

    // Sauvegarder les données utilisateur si disponibles
    if (userData != null) {
      print('Sauvegarde des données utilisateur: $userData');

      // Sauvegarder le token (IMPORTANT)
      if (userData.containsKey('token')) {
        await prefs.setString('token', userData['token']);
        print('Token sauvegardé: ${userData['token']}');
      }

      // Sauvegarder l'ID client
      if (userData.containsKey('id_client')) {
        await prefs.setString('userId', userData['id_client'].toString());
        print('ID client sauvegardé: ${userData['id_client']}');
      }

      // Sauvegarder le nom d'utilisateur
      if (userData.containsKey('nom_client') &&
          userData.containsKey('prenom_client')) {
        final String userName =
            '${userData['prenom_client']} ${userData['nom_client']}';
        await prefs.setString('userName', userName);
        print('Nom utilisateur sauvegardé: $userName');
      }

      // Sauvegarder le code client
      if (userData.containsKey('code_client')) {
        await prefs.setString('codeClient', userData['code_client']);
        print('Code client sauvegardé: ${userData['code_client']}');
      }

      // Sauvegarder le pseudo client
      if (userData.containsKey('pseudo_client')) {
        await prefs.setString('pseudoClient', userData['pseudo_client']);
        print('Pseudo client sauvegardé: ${userData['pseudo_client']}');
      }

      // Sauvegarder la photo de profil
      // if (userData.containsKey('photo')) {
      //   await prefs.setString('userPhoto', userData['photo']);
      //   print('Photo utilisateur sauvegardée: ${userData['photo']}');
      // }

      // Sauvegarder les informations de localité et zone
      if (userData.containsKey('local_id')) {
        await prefs.setString('localId', userData['local_id'].toString());
        print('ID local sauvegardé: ${userData['local_id']}');
      }

      if (userData.containsKey('zone_id')) {
        await prefs.setString('zoneId', userData['zone_id'].toString());
        print('ID zone sauvegardé: ${userData['zone_id']}');
      }

      // Sauvegarder toutes les données utilisateur sous forme de JSON
      await prefs.setString('userData', jsonEncode(userData));
      print('Toutes les données utilisateur sauvegardées en JSON');
    }

    // Mettre à jour les streams de manière réactive
    final token = userData?['token'];
    await _updateLoginState(true, userData: userData, token: token);
  }

  // Vérifier si l'utilisateur est connecté
  static Future<bool> isLoggedIn() async {
    final prefs = await SharedPreferences.getInstance();
    return prefs.getBool('isLoggedIn') ?? false;
  }

  // Récupérer le numéro de téléphone de l'utilisateur connecté
  static Future<String?> getLoggedInPhoneNumber() async {
    final prefs = await SharedPreferences.getInstance();
    return prefs.getString('phoneNumber');
  }

  // Récupérer les données utilisateur
  static Future<Map<String, dynamic>?> getUserData() async {
    final prefs = await SharedPreferences.getInstance();
    final userDataJson = prefs.getString('userData');

    if (userDataJson != null) {
      try {
        final userData = jsonDecode(userDataJson) as Map<String, dynamic>;

        // Vérifier si le token est présent dans les données
        if (!userData.containsKey('token')) {
          // Si le token est stocké séparément, l'ajouter aux données
          final token = prefs.getString('token');
          if (token != null) {
            userData['token'] = token;
          }
        }

        return userData;
      } catch (e) {
        print('Erreur lors du décodage des données utilisateur: $e');
        return null;
      }
    }

    // Si userData n'existe pas, essayer de construire un objet minimal
    final isLoggedIn = prefs.getBool('isLoggedIn') ?? false;
    if (isLoggedIn) {
      final Map<String, dynamic> minimalUserData = {};

      // Ajouter les informations disponibles
      final phoneNumber = prefs.getString('phoneNumber');
      final token = prefs.getString('token');
      final userId = prefs.getString('userId');
      final userName = prefs.getString('userName');

      if (phoneNumber != null) {
        minimalUserData['telephone_client'] = phoneNumber;
      }
      if (token != null) minimalUserData['token'] = token;
      if (userId != null) minimalUserData['id_client'] = userId;
      if (userName != null) {
        final nameParts = userName.split(' ');
        if (nameParts.length > 1) {
          minimalUserData['prenom_client'] = nameParts[0];
          minimalUserData['nom_client'] = nameParts.sublist(1).join(' ');
        } else {
          minimalUserData['prenom_client'] = userName;
        }
      }

      return minimalUserData.isNotEmpty ? minimalUserData : null;
    }

    return null;
  }

  // Vérifier si le token est valide
  static Future<bool> isTokenValid() async {
    final token = await getAuthToken();

    if (token == null || token.isEmpty) {
      return false;
    }

    try {
      // Vérifier si le token est expiré en décodant sa partie payload
      final parts = token.split('.');
      if (parts.length != 3) {
        return false;
      }

      // Décoder la partie payload (deuxième partie du token)
      String normalizedPayload = parts[1];
      normalizedPayload = base64Url.normalize(normalizedPayload);
      final payloadJson = utf8.decode(base64Url.decode(normalizedPayload));
      final payload = jsonDecode(payloadJson);

      // Vérifier si le token contient une date d'expiration
      if (payload.containsKey('exp')) {
        final expTimestamp = payload['exp'] as int;
        final expDate = DateTime.fromMillisecondsSinceEpoch(
          expTimestamp * 1000,
        );

        // Comparer avec la date actuelle
        return expDate.isAfter(DateTime.now());
      }

      // Si pas de date d'expiration, considérer comme valide
      return true;
    } catch (e) {
      print('Erreur lors de la vérification du token: $e');
      return false;
    }
  }

  // Récupérer le token d'authentification
  static Future<String?> getAuthToken() async {
    final prefs = await SharedPreferences.getInstance();
    return prefs.getString('token');
  }

  // Alias pour getAuthToken pour maintenir la compatibilité
  static Future<String?> getToken() async {
    return await getAuthToken();
  }

  // Déconnecter l'utilisateur de manière réactive
  static Future<void> logout() async {
    try {
      final prefs = await SharedPreferences.getInstance();

      // Effacer toutes les données de session
      await prefs.clear();

      // Pour plus de sécurité, effacer explicitement les données sensibles
      await prefs.remove('token');
      await prefs.remove('userData');
      await prefs.remove('phoneNumber');
      await prefs.remove('isLoggedIn');
      await prefs.remove('userId');
      await prefs.remove('userName');
      await prefs.remove('loginTime');

      // S'assurer que isLoggedIn est bien à false
      await prefs.setBool('isLoggedIn', false);

      // Mettre à jour les streams de manière réactive
      await _updateLoginState(false);

      // Invalider le cache global
      AppStateService.instance.invalidateAllCache();

      print(
        'Déconnexion réussie: toutes les données utilisateur ont été effacées',
      );
    } catch (e) {
      print('Erreur lors de la déconnexion: $e');
      throw Exception('Erreur lors de la déconnexion: $e');
    }
  }

  // Récupérer la liste des localités
  static Future<List<Map<String, dynamic>>> getLocalites() async {
    try {
      print('Appel API: ${ApiConfig.baseUrl}/local/get_localites.php');
      final response = await http.get(
        Uri.parse('${ApiConfig.baseUrl}/local/get_localites.php'),
      );

      if (response.statusCode == 200) {
        final jsonResponse = jsonDecode(response.body);
        if (jsonResponse.containsKey('localites')) {
          // Convertir les clés pour correspondre à notre format attendu
          return List<Map<String, dynamic>>.from(
            jsonResponse['localites'].map(
              (localite) => {
                'id': localite['id_local'].toString(),
                'libelle': localite['nom_local'],
              },
            ),
          );
        }
      }
      return [];
    } catch (e) {
      print('Exception lors de la récupération des localités: $e');
      return [];
    }
  }

  // Récupérer la liste des zones
  static Future<List<Map<String, dynamic>>> getZones() async {
    try {
      print('Appel API: ${ApiConfig.baseUrl}/zone/get_zones.php');
      final response = await http.get(
        Uri.parse('${ApiConfig.baseUrl}/zone/get_zones.php'),
      );

      if (response.statusCode == 200) {
        final jsonResponse = jsonDecode(response.body);
        if (jsonResponse.containsKey('zones')) {
          // Convertir les clés pour correspondre à notre format attendu
          return List<Map<String, dynamic>>.from(
            jsonResponse['zones'].map(
              (zone) => {
                'id': zone['id_zone'].toString(),
                'libelle': zone['nom_zone'],
              },
            ),
          );
        }
      }
      return [];
    } catch (e) {
      print('Exception lors de la récupération des zones: $e');
      return [];
    }
  }

  // Enregistrer un nouveau client
  static Future<Map<String, dynamic>> registerClient({
    required String nom,
    required String prenom,
    required String telephone,
    required String localId,
    required String zoneId,
    String? domicile,
    String? sexe,
    String? fonction,
    // String? email,
    // String? dateNaissance,
  }) async {
    try {
      final Map<String, dynamic> requestBody = {
        "nom_client": nom,
        "prenom_client": prenom,
        "telephone_client": telephone,
        "local_id": int.parse(localId), // Convertir en entier
        "zone_id": int.parse(zoneId), // Convertir en entier
      };

      if (domicile != null && domicile.isNotEmpty) {
        requestBody["domicile_client"] = domicile;
      }
      if (sexe != null && sexe.isNotEmpty) {
        requestBody["sexe_client"] =
            sexe == "Homme" ? "H" : (sexe == "Femme" ? "F" : sexe);
      }
      if (fonction != null && fonction.isNotEmpty) {
        requestBody["fonction_client"] = fonction;
      }
      // if (email != null && email.isNotEmpty) requestBody["email_client"] = email;
      // if (dateNaissance != null && dateNaissance.isNotEmpty) requestBody["date_naissance_client"] = dateNaissance;

      print('Envoi de la requête d\'inscription: $requestBody');
      print('URL: ${ApiConfig.baseUrl}/client/create.php');

      // Utiliser un timeout plus long pour les problèmes de réseau
      final response = await http
          .post(
            Uri.parse('${ApiConfig.baseUrl}/client/create.php'),
            headers: {'Content-Type': 'application/json'},
            body: jsonEncode(requestBody),
          )
          .timeout(const Duration(seconds: 30));

      print('Statut de la réponse: ${response.statusCode}');
      print('Corps de la réponse: ${response.body}');

      final jsonResponse = jsonDecode(response.body);
      if (response.statusCode == 200 || response.statusCode == 201) {
        try {
          return {
            'success': true,
            'message': jsonResponse['message'] ?? 'Enregistrement réussi !',
            'code_client': jsonResponse['code_client'] ?? 'N/A',
          };
        } catch (e) {
          print('Erreur de décodage JSON: $e');
          if (response.body.toLowerCase().contains('succ')) {
            return {
              'success': true,
              'message': 'Enregistrement réussi !',
              'code_client': 'Généré',
            };
          } else {
            return {
              'success': false,
              'message': 'Format de réponse invalide: ${response.body}',
            };
          }
        }
      } else if (response.statusCode == 500) {
        return {
          'success': false,
          'message': jsonResponse['message'] ??
              'Erreur serveur (500). Veuillez contacter l\'administrateur.',
        };
      } else {
        return {
          'success': false,
          'message':
              'Erreur lors de l\'inscription (${response.statusCode}): ${response.body}',
        };
      }
    } catch (e) {
      print('Exception lors de l\'inscription: $e');
      return {'success': false, 'message': 'Erreur: ${e.toString()}'};
    }
  }
}
