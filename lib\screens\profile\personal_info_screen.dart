import 'package:flutter/material.dart';
import 'package:callitris/utils/appTheme.dart';
import 'package:callitris/services/auth_service.dart';
import 'package:callitris/services/wallet_service.dart';
import 'dart:convert';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:intl/intl.dart';

class PersonalInfoScreen extends StatefulWidget {
  const PersonalInfoScreen({super.key});

  @override
  State<PersonalInfoScreen> createState() => _PersonalInfoScreenState();
}

class _PersonalInfoScreenState extends State<PersonalInfoScreen> with SingleTickerProviderStateMixin {
  bool _isLoading = true;
  Map<String, dynamic> _userData = {};
  late AnimationController _animationController;
  late Animation<double> _fadeAnimation;
  int _monnaie = 0;
  bool _isLoadingMonnaie = true;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 800),
    );
    _fadeAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(
        parent: _animationController,
        curve: Curves.easeInOut,
      ),
    );
    _loadUserData();
    _loadUserMonnaie();
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

    String _formatPrice(int price) {
    final formatter = NumberFormat('#,###', 'fr_FR');
    return formatter.format(price).replaceAll(',', ' ');
  }

  Future<void> _loadUserData() async {
    final prefs = await SharedPreferences.getInstance();
    final userDataJson = prefs.getString('userData');
    
    if (userDataJson != null) {
      try {
        final userData = jsonDecode(userDataJson);
        setState(() {
          _userData = userData;
          _isLoading = false;
        });
        _animationController.forward();
      } catch (e) {
        print('Erreur lors du décodage des données utilisateur: $e');
        setState(() {
          _isLoading = false;
        });
      }
    } else {
      // Fallback pour les anciennes installations qui n'ont pas userData
      final phoneNumber = await AuthService.getLoggedInPhoneNumber();
      setState(() {
        _userData = {'telephone_client': phoneNumber};
        _isLoading = false;
      });
      _animationController.forward();
    }
  }

  Future<void> _loadUserMonnaie() async {
    try {
      final result = await WalletService.getUserMonnaie();
      
      setState(() {
        _monnaie = result['montant'] ?? 0;
        _isLoadingMonnaie = false;
      });
    } catch (e) {
      print('Erreur lors du chargement de la monnaie: $e');
      setState(() {
        _monnaie = 0;
        _isLoadingMonnaie = false;
      });
    }
  }

  String _formatValue(dynamic value) {
    if (value == null || value == '' || value == ' ') {
      return 'Non renseigné';
    }
    return value.toString();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.grey[50],
      appBar: AppBar(
        backgroundColor: Colors.transparent,
        elevation: 0,
        title: const Text(
          'Profil',
          style: TextStyle(
            fontSize: 20,
            fontWeight: FontWeight.w700,
          ),
        ),
        centerTitle: true,
        leading: IconButton(
          icon: Container(
            padding: const EdgeInsets.all(8),
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(10),
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withOpacity(0.1),
                  blurRadius: 10,
                  offset: const Offset(0, 2),
                ),
              ],
            ),
            child: Icon(
              Icons.arrow_back_ios_new,
              color: AppTheme.color.primaryColor,
              size: 18,
            ),
          ),
          onPressed: () => Navigator.of(context).pop(),
        ),
      ),
      body: _isLoading
          ? const Center(
              child: CircularProgressIndicator(),
            )
          : FadeTransition(
              opacity: _fadeAnimation,
              child: SingleChildScrollView(
                physics: const BouncingScrollPhysics(),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    _buildProfileHeader(),
                    const SizedBox(height: 20),
                    _buildInfoCards(),
                  ],
                ),
              ),
            ),
    );
  }

    Widget _buildProfileHeader() {
    return Container(
      margin: const EdgeInsets.all(16),
      padding: const EdgeInsets.all(24),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [
            AppTheme.color.primaryColor,
            Color.lerp(AppTheme.color.primaryColor, Colors.black, 0.3) ?? AppTheme.color.primaryColor,
          ],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        borderRadius: BorderRadius.circular(20),
        boxShadow: [
          BoxShadow(
            color: AppTheme.color.primaryColor.withOpacity(0.3),
            blurRadius: 15,
            offset: const Offset(0, 5),
          ),
        ],
      ),
      child: Column(
        children: [
          Row(
            children: [
              Container(
                padding: const EdgeInsets.all(3),
                decoration: BoxDecoration(
                  color: Colors.white,
                  shape: BoxShape.circle,
                ),
                child: CircleAvatar(
                  radius: 40,
                  backgroundColor: Colors.white,
                  child: Text(
                    _getInitials(),
                    style: TextStyle(
                      fontSize: 28,
                      fontWeight: FontWeight.bold,
                      color: AppTheme.color.primaryColor,
                    ),
                  ),
                ),
              ),
              const SizedBox(width: 20),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      '${_formatValue(_userData['prenom_client'])} ${_formatValue(_userData['nom_client'])}',
                      style: const TextStyle(
                        fontSize: 22,
                        fontWeight: FontWeight.bold,
                        color: Colors.white,
                      ),
                    ),
                    const SizedBox(height: 8),
                    Row(
                      children: [
                        Icon(
                          Icons.phone_android,
                          color: Colors.white.withOpacity(0.8),
                          size: 16,
                        ),
                        const SizedBox(width: 8),
                        Text(
                          _formatValue(_userData['telephone_client']),
                          style: TextStyle(
                            fontSize: 16,
                            color: Colors.white.withOpacity(0.9),
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 4),
                    Row(
                      children: [
                        Icon(
                          Icons.work_outline,
                          color: Colors.white.withOpacity(0.8),
                          size: 16,
                        ),
                        const SizedBox(width: 8),
                        Text(
                          _formatValue(_userData['fonction_client']),
                          style: TextStyle(
                            fontSize: 14,
                            color: Colors.white.withOpacity(0.9),
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            ],
          ),
          const SizedBox(height: 20),
          // Code client
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 10),
            decoration: BoxDecoration(
              color: Colors.white.withOpacity(0.2),
              borderRadius: BorderRadius.circular(10),
            ),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(
                  Icons.verified_user,
                  color: Colors.white,
                  size: 18,
                ),
                const SizedBox(width: 8),
                Text(
                  'Code client: ${_formatValue(_userData['code_client'])}',
                  style: const TextStyle(
                    fontSize: 14,
                    fontWeight: FontWeight.w500,
                    color: Colors.white,
                  ),
                ),
              ],
            ),
          ),
          const SizedBox(height: 16),
          // Monnaie disponible
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(10),
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withOpacity(0.05),
                  blurRadius: 10,
                  offset: const Offset(0, 2),
                ),
              ],
            ),
            child: Row(
              children: [
                Container(
                  padding: const EdgeInsets.all(10),
                  decoration: BoxDecoration(
                    color: AppTheme.color.greenColor.withOpacity(0.1),
                    borderRadius: BorderRadius.circular(10),
                  ),
                  child: Icon(
                    Icons.account_balance_wallet,
                    color: AppTheme.color.greenColor,
                    size: 24,
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'Monnaie disponible',
                        style: TextStyle(
                          fontSize: 14,
                          color: AppTheme.color.brunGris,
                        ),
                      ),
                      const SizedBox(height: 4),
                      _isLoadingMonnaie
                          ? const SizedBox(
                              height: 20,
                              width: 20,
                              child: CircularProgressIndicator(
                                strokeWidth: 2,
                              ),
                            )
                          : Text(
                              '${_formatPrice(_monnaie)} FCFA',
                              style: TextStyle(
                                fontSize: 18,
                                fontWeight: FontWeight.bold,
                                color: AppTheme.color.greenColor,
                              ),
                            ),
                    ],
                  ),
                ),
                IconButton(
                  icon: Icon(
                    Icons.refresh,
                    color: AppTheme.color.primaryColor,
                    size: 20,
                  ),
                  onPressed: () {
                    setState(() {
                      _isLoadingMonnaie = true;
                    });
                    _loadUserMonnaie();
                  },
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

    Widget _buildInfoCards() {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Section Monnaie
          _buildSectionTitle('Monnaie'),
          const SizedBox(height: 12),
          _buildInfoCard([
            _buildInfoItem(
              Icons.account_balance_wallet,
              'Monnaie disponible',
              _isLoadingMonnaie
                  ? 'Chargement...'
                  : '${_formatPrice(_monnaie)} FCFA',
            ),
            _buildInfoItem(
              Icons.history,
              'Dernière mise à jour',
              DateFormat('dd/MM/yyyy à HH:mm').format(DateTime.now()),
            ),
          ]),
          
          const SizedBox(height: 24),
          _buildSectionTitle('Informations personnelles'),
          const SizedBox(height: 12),
          _buildInfoCard([
            _buildInfoItem(Icons.person, 'Nom complet', 
              '${_formatValue(_userData['prenom_client'])} ${_formatValue(_userData['nom_client'])}'),
            _buildInfoItem(Icons.wc, 'Sexe', 
              _userData['sexe_client'] == 'H' ? 'Homme' : 
              (_userData['sexe_client'] == 'F' ? 'Femme' : 'Non renseigné')),
            _buildInfoItem(Icons.cake, 'Date de naissance', 
              _formatValue(_userData['date_naiss_client'])),
          ]),
          
          const SizedBox(height: 24),
          _buildSectionTitle('Coordonnées'),
          const SizedBox(height: 12),
          _buildInfoCard([
            _buildInfoItem(Icons.phone, 'Téléphone', 
              _formatValue(_userData['telephone_client'])),
            _buildInfoItem(Icons.email, 'Email', 
              _formatValue(_userData['email_client'])),
            _buildInfoItem(Icons.home, 'Domicile', 
              _formatValue(_userData['domicile_client'])),
          ]),
          
          const SizedBox(height: 24),
          _buildSectionTitle('Informations du compte'),
          const SizedBox(height: 12),
          _buildInfoCard([
            _buildInfoItem(Icons.badge, 'Code client', 
              _formatValue(_userData['code_client'])),
            _buildInfoItem(Icons.calendar_today, 'Date d\'inscription', 
              _formatDateString(_formatValue(_userData['date_ajout']))),
            _buildInfoItem(Icons.access_time, 'Heure d\'inscription', 
              _formatValue(_userData['heure_ajout'])),
          ]),
          
          const SizedBox(height: 30),
        ],
      ),
    );
  }

  Widget _buildSectionTitle(String title) {
    return Padding(
      padding: const EdgeInsets.only(left: 8),
      child: Row(
        children: [
          Container(
            width: 4,
            height: 20,
            decoration: BoxDecoration(
              color: AppTheme.color.primaryColor,
              borderRadius: BorderRadius.circular(2),
            ),
          ),
          const SizedBox(width: 8),
          Text(
            title,
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: AppTheme.color.textColor,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildInfoCard(List<Widget> children) {
    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        children: List.generate(
          children.length * 2 - 1,
          (index) {
            if (index.isEven) {
              return children[index ~/ 2];
            } else {
              return Divider(
                height: 1,
                thickness: 1,
                color: Colors.grey.withOpacity(0.1),
                indent: 70,
                endIndent: 20,
              );
            }
          },
        ),
      ),
    );
  }

  Widget _buildInfoItem(IconData icon, String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 16, horizontal: 20),
      child: Row(
        children: [
          Container(
            padding: const EdgeInsets.all(10),
            decoration: BoxDecoration(
              color: AppTheme.color.primaryColor.withOpacity(0.1),
              borderRadius: BorderRadius.circular(10),
            ),
            child: Icon(
              icon,
              color: AppTheme.color.primaryColor,
              size: 20,
            ),
          ),
          const SizedBox(width: 16),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  label,
                  style: TextStyle(
                    fontSize: 14,
                    color: AppTheme.color.brunGris,
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  value,
                  style: const TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  String _getInitials() {
    String prenom = _formatValue(_userData['prenom_client']);
    String nom = _formatValue(_userData['nom_client']);
    
    if (prenom == 'Non renseigné' && nom == 'Non renseigné') {
      return 'U';
    }
    
    String initials = '';
    if (prenom != 'Non renseigné' && prenom.isNotEmpty) {
      initials += prenom[0];
    }
    if (nom != 'Non renseigné' && nom.isNotEmpty) {
      initials += nom[0];
    }
    
    return initials.toUpperCase();
  }
  
  String _formatDateString(String dateStr) {
    if (dateStr == 'Non renseigné') return dateStr;
    
    try {
      final parts = dateStr.split('-');
      if (parts.length == 3) {
        return '${parts[2]}/${parts[1]}/${parts[0]}';
      }
    } catch (e) {
      print('Erreur lors du formatage de la date: $e');
    }
    
    return dateStr;
  }
}
