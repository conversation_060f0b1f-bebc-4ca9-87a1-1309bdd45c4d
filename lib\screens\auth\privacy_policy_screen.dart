import 'package:flutter/material.dart';
import 'package:callitris/utils/appTheme.dart';

class PrivacyPolicyScreen extends StatelessWidget {
  const PrivacyPolicyScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFFF5F7FA),
      body: CustomScrollView(
        slivers: [
          // AppBar moderne avec effet
          SliverAppBar(
            expandedHeight: 200,
            pinned: true,
            stretch: true,
            backgroundColor: Colors.transparent,
            elevation: 0,
            leading: Container(
              margin: const EdgeInsets.all(8),
              decoration: BoxDecoration(
                color: Colors.white.withValues(alpha: 0.9),
                borderRadius: BorderRadius.circular(12),
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withValues(alpha: 0.1),
                    blurRadius: 10,
                    offset: const Offset(0, 2),
                  ),
                ],
              ),
              child: IconButton(
                icon: Icon(Icons.arrow_back, color: AppTheme.color.textColor),
                onPressed: () => Navigator.pop(context),
              ),
            ),
            flexibleSpace: FlexibleSpaceBar(
              background: Container(
                decoration: BoxDecoration(
                  gradient: LinearGradient(
                    begin: Alignment.topLeft,
                    end: Alignment.bottomRight,
                    colors: [
                      AppTheme.color.primaryColor,
                      AppTheme.color.primaryColor.withValues(alpha: 0.8),
                      const Color(0xFF667eea),
                    ],
                  ),
                ),
                child: Stack(
                  children: [
                    // Effet de décoration en arrière-plan
                    Positioned(
                      top: -50,
                      right: -50,
                      child: Container(
                        width: 200,
                        height: 200,
                        decoration: BoxDecoration(
                          color: Colors.white.withValues(alpha: 0.1),
                          shape: BoxShape.circle,
                        ),
                      ),
                    ),
                    Positioned(
                      bottom: -30,
                      left: -30,
                      child: Container(
                        width: 120,
                        height: 120,
                        decoration: BoxDecoration(
                          color: Colors.white.withValues(alpha: 0.1),
                          shape: BoxShape.circle,
                        ),
                      ),
                    ),
                    // Contenu principal
                    Center(
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Container(
                            padding: const EdgeInsets.all(20),
                            decoration: BoxDecoration(
                              color: Colors.white.withValues(alpha: 0.2),
                              borderRadius: BorderRadius.circular(20),
                              border: Border.all(
                                color: Colors.white.withValues(alpha: 0.3),
                                width: 1,
                              ),
                            ),
                            child: const Icon(
                              Icons.gavel,
                              color: Colors.white,
                              size: 40,
                            ),
                          ),
                          const SizedBox(height: 20),
                          const Text(
                            'Clause de Contrat',
                            style: TextStyle(
                              color: Colors.white,
                              fontSize: 28,
                              fontWeight: FontWeight.bold,
                              letterSpacing: 0.5,
                            ),
                          ),
                          const SizedBox(height: 8),
                          Text(
                            'Callitris Distribution',
                            style: TextStyle(
                              color: Colors.white.withValues(alpha: 0.9),
                              fontSize: 16,
                              fontWeight: FontWeight.w500,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ),

          // Contenu principal
          SliverToBoxAdapter(
            child: Padding(
              padding: const EdgeInsets.all(20),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Carte d'introduction moderne
                  _buildIntroCard(),
                  const SizedBox(height: 24),

                  // // Introduction
                  // _buildSection(
                  //   'Présentation',
                  //   'Callitris Distribution est une entreprise multidimensionnelle qui vient vous proposer l\'une de ses prestations de services à travers ce carnet d\'achat.\n\n'
                  //       'NB: Nous vous conseillons de lire attentivement les différents articles inscrits dans ce document afin de vous imprégner de notre mode de fonctionnement.',
                  // ),

                  _buildSection(
                    'Article 1 : Objet de la cotisation',
                    'Callitris Distribution vient à travers ce produit vous proposer une collecte de fond en vue de vous permettre d\'anticiper sur vos différentes dépenses relatives aux grands évènements de l\'année, notamment la rentrée scolaire et les fêtes religieuses.\n\n'
                        'Il est strictement confidentiel.',
                  ),

                  _buildSection(
                    'Article 2 : La souscription',
                    'Sachez de prime abord qu\'aucun remboursement n\'est pas possible lorsque vous vous inscrivez. Nous vous considérons sain de corps et d\'esprit lorsque vous vous engagez à souscrire à nos offres.\n\n'
                        'Vous êtes inscrit à l\'achat de votre carnet au prix de 500 FCFA. Le commercial vous communiquera les instructions relatives à votre inscription.\n\n'
                        'En cas de perte de votre carnet de cotisation, vous serez dans l\'obligation d\'en acheter un autre.',
                  ),

                  _buildSection(
                    'Article 3 : Le substitut',
                    'Chaque client est tenu d\'inscrire dans son carnet le nom d\'un parent ou d\'un ami en qui il a une entière confiance, afin que ce dernier puisse récupérer son lot en cas d\'indisponibilité de sa part.',
                  ),

                  _buildSection(
                    'Article 4 : Obligation du souscripteur, délai de paiement et pénalité',
                    'Votre cotisation n\'est pas remboursable. Vous devez obligatoirement solder votre cotisation avant d\'obtenir votre pack choisi en fin de campagne.\n\n'
                        'En cas de fluctuation de prix d\'un article contenu dans un pack, le client doit supporter un changement du coût de l\'article si ce changement est connu sur le plan national.\n\n'
                        'Un délai de collecte vous est exigé. Les clients qui n\'ayant pas pu respecter le délai indiqué auront un délai de rigueur d\'une semaine. Passé ce délai, ils seront reconduits pour la campagne suivante avec une pénalité de 35% qui sera déduite de leur collecte.\n\n'
                        'Les clients n\'ayant pas pu solder leur pack dans un délai de un (01) an devront changer de carnet avec une pénalité de -35%.',
                  ),

                  _buildSection(
                    'Article 5 : Distribution des packs',
                    'La date de distribution vous sera communiquée par le service commercial de la société.\n\n'
                        'La distribution de vos packs se fera dans un délai maximal d\'une semaine après la date butoir de fin de cotisation, sur des espaces que vous indiqueront nos agents commerciaux.',
                  ),

                  _buildSection(
                    'Article 6 : Modification',
                    'Si vous souhaitez une modification, demandez à votre gestionnaire ou commercial. Il ou elle fera remonter l\'information.\n\n'
                        'Si votre requête est recevable, vous subirez une pénalité de 35% sur votre cotisation. Les modifications ne seront possibles qu\'une seule fois.',
                  ),

                  _buildSection(
                    'Article 7 : Arrêt de la cotisation',
                    'Si vous voudriez annuler votre cotisation pour des raisons de force majeure, d\'incapacité de continuer ou autre, veuillez adresser un courrier à la direction des affaires et financières et attendre un délai de 72h après accusé de réception.\n\n'
                        'Si votre désir d\'annulation a été accepté, votre pack choisi sera converti en un autre qui a un montant inférieur de sorte à ce que vous puissiez avoir d\'autres articles suite à un prélèvement de 35%.\n\n'
                        'NB: La restitution en espèce ne peut se faire.',
                  ),

                  _buildSection(
                    'Article 8 : Engagement et prévention',
                    'Vous devrez vous abstenir de toutes sortes de menaces envers votre commercial ou agent de la société CALLITRIS DISTRIBUTION.\n\n'
                        'En cas de mésententes, veuillez appeler au numéro suivant: +225 07 99 02 57 57 ou écrire à la direction générale.\n\n'
                        'Pour des raisons publicitaires, la société CALLITRIS DISTRIBUTION pourrait utiliser votre image. Toutefois, elle s\'engage à garder confidentielles et secrètes les informations personnelles vous concernant.',
                  ),

                  const SizedBox(height: 32),

                  // Bouton de retour moderne
                  _buildActionButton(context),
                  const SizedBox(height: 20),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildIntroCard() {
    return Container(
      padding: const EdgeInsets.all(24),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(20),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.08),
            blurRadius: 20,
            offset: const Offset(0, 8),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  gradient: LinearGradient(
                    colors: [
                      AppTheme.color.primaryColor,
                      AppTheme.color.primaryColor.withValues(alpha: 0.7),
                    ],
                  ),
                  borderRadius: BorderRadius.circular(12),
                ),
                child: const Icon(
                  Icons.info_outline,
                  color: Colors.white,
                  size: 24,
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Information importante',
                      style: TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                        color: AppTheme.color.textColor,
                      ),
                    ),
                    const SizedBox(height: 4),
                    Text(
                      'Veuillez lire attentivement',
                      style: TextStyle(
                        fontSize: 14,
                        color: AppTheme.color.brunGris,
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: AppTheme.color.primaryColor.withValues(alpha: 0.05),
              borderRadius: BorderRadius.circular(12),
              border: Border.all(
                color: AppTheme.color.primaryColor.withValues(alpha: 0.2),
                width: 1,
              ),
            ),
            child: Text(
              'Callitris Distribution est une entreprise multidimensionnelle qui vient vous proposer l\'une de ses prestations de services à travers ce carnet d\'achat.\n\n'
              'NB: Nous vous conseillons de lire attentivement les différents articles inscrits dans ce document afin de vous imprégner de notre mode de fonctionnement.',
              style: TextStyle(
                fontSize: 14,
                color: AppTheme.color.textColor,
                height: 1.6,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildActionButton(BuildContext context) {
    return Container(
      width: double.infinity,
      height: 56,
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [
            AppTheme.color.primaryColor,
            AppTheme.color.primaryColor.withValues(alpha: 0.8),
          ],
          begin: Alignment.centerLeft,
          end: Alignment.centerRight,
        ),
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: AppTheme.color.primaryColor.withValues(alpha: 0.3),
            blurRadius: 15,
            offset: const Offset(0, 8),
          ),
        ],
      ),
      child: ElevatedButton.icon(
        onPressed: () => Navigator.pop(context),
        icon: const Icon(Icons.check_circle_outline),
        label: const Text(
          'J\'ai compris',
          style: TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.w600,
            letterSpacing: 0.5,
          ),
        ),
        style: ElevatedButton.styleFrom(
          backgroundColor: Colors.transparent,
          foregroundColor: Colors.white,
          shadowColor: Colors.transparent,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(16),
          ),
          elevation: 0,
        ),
      ),
    );
  }

  Widget _buildSection(String title, String content) {
    // Extraire le numéro d'article s'il existe
    final RegExp articleRegex = RegExp(r'^Article (\d+)');
    final Match? match = articleRegex.firstMatch(title);
    final String? articleNumber = match?.group(1);

    return Container(
      margin: const EdgeInsets.only(bottom: 24),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(20),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.08),
            blurRadius: 20,
            offset: const Offset(0, 8),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Header avec numéro d'article
          Container(
            padding: const EdgeInsets.all(20),
            decoration: BoxDecoration(
              gradient: LinearGradient(
                colors: [
                  AppTheme.color.primaryColor.withValues(alpha: 0.1),
                  AppTheme.color.primaryColor.withValues(alpha: 0.05),
                ],
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
              ),
              borderRadius: const BorderRadius.only(
                topLeft: Radius.circular(20),
                topRight: Radius.circular(20),
              ),
            ),
            child: Row(
              children: [
                if (articleNumber != null) ...[
                  Container(
                    width: 40,
                    height: 40,
                    decoration: BoxDecoration(
                      gradient: LinearGradient(
                        colors: [
                          AppTheme.color.primaryColor,
                          AppTheme.color.primaryColor.withValues(alpha: 0.8),
                        ],
                      ),
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: Center(
                      child: Text(
                        articleNumber,
                        style: const TextStyle(
                          color: Colors.white,
                          fontSize: 18,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ),
                  ),
                  const SizedBox(width: 16),
                ],
                Expanded(
                  child: Text(
                    title,
                    style: TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                      color: AppTheme.color.textColor,
                      height: 1.3,
                    ),
                  ),
                ),
              ],
            ),
          ),

          // Contenu
          Padding(
            padding: const EdgeInsets.all(20),
            child: Text(
              content,
              style: TextStyle(
                fontSize: 15,
                color: AppTheme.color.textColor,
                height: 1.7,
                letterSpacing: 0.2,
              ),
            ),
          ),
        ],
      ),
    );
  }
}
