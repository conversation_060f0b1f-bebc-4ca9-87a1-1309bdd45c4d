class ApiConfig {
  // Base URL
  static const String baseUrl =
      'https://api.callitris-distribution.com/client-api.callitris-distribution.com';

  static const String baseUrl2 =
      'https://app.callitris-distribution.com';

  // Endpoints d'authentification
  static const String sendOtpEndpoint = '$baseUrl/auth/send_otp.php';
  static const String verifyOtpEndpoint = '$baseUrl/auth/verify_otp.php';
  // Ajouter dans la classe ApiConfig
  static const String loginEndpoint = '$baseUrl/auth/auth.php';

  // Endpoints de gestion des clients
  static const String registerClientEndpoint = '$baseUrl/client/create.php';
  static const String getClientInfoEndpoint = '$baseUrl/client/get_info.php';
  static const String updateClientEndpoint = '$baseUrl/client/update.php';

  // Endpoints pour les données de référence
  static const String getLocalitesEndpoint = '$baseUrl/local/get_localites.php';
  static const String getZonesEndpoint = '$baseUrl/zone/get_zones.php';

  // Endpoints pour les carnets
  static const String getCarnetCategoriesEndpoint =
      '$baseUrl/carnet/get_carnet.php';
  static const String getCarnetDetailsEndpoint =
      '$baseUrl/carnet/get_carnet_details.php';
  static const String subscribeToCarnetEndpoint =
      '$baseUrl/carnet/subscribe_carnet.php';
  static const String getCarnetElementsEndpoint =
      '$baseUrl/kit/getKitCarnet.php';
  static const String getCarnetCataloguesEndpoint =
      '$baseUrl/carnet/get_carnetCatalogue.php';

  // Endpoints pour les produits en vedette
  static const String getFeaturedProductsEndpoint =
      '$baseUrl/kit/getFeaturedProducts.php';

  // Endpoints pour les commandes
  static const String addCommandeEndpoint = '$baseUrl/commande/addCommande.php';
  static const String getUserOrdersEndpoint =
      '$baseUrl/commande/getCommandeClient.php';
  static const String addVersementEndpoint =
      '$baseUrl/commande/addVersementCom.php';
  static const String getOrderVersementsEndpoint =
      '$baseUrl/commande/getVersementsCompte.php';

  // Endpoint CinetPay
  static const String initCinetPayEndpoint =
      '$baseUrl/paiement/init_cinetpay.php';

  // Configuration CinetPay
  static const String cinetPayApiKey =
      'YOUR_CINETPAY_API_KEY'; // À remplacer par votre clé API
  static const String cinetPaySiteId =
      'YOUR_SITE_ID'; // À remplacer par votre Site ID
  static const String cinetPaySecretKey =
      'YOUR_SECRET_KEY'; // À remplacer par votre clé secrète
  static const bool cinetPaySandboxMode = true; // false pour la production

  // URLs de callback CinetPay
  static const String cinetPayReturnUrl = '$baseUrl/cinetpay/return';
  static const String cinetPayNotifyUrl = '$baseUrl/cinetpay/notify';
  static const String cinetPayCancelUrl = '$baseUrl/cinetpay/cancel';

  // Configuration Wave
  // URLs de callback Wave pour votre backend
  static const String waveReturnUrl = '$baseUrl/callback/wave/return';
  static const String waveNotifyUrl = '$baseUrl/callback/wave/notify';
  static const String waveCancelUrl = '$baseUrl/callback/wave/cancel';

  // Deep Links pour l'application mobile
  static const String appScheme = 'callitris';
  static const String paymentReturnDeepLink = '$appScheme://payment/return';
  static const String paymentSuccessDeepLink = '$appScheme://payment/success';
  static const String paymentCancelDeepLink = '$appScheme://payment/cancel';
  static const String paymentFailureDeepLink = '$appScheme://payment/failure';

  // URLs de callback complètes pour Wave (à configurer dans votre backend)
  static const String waveSuccessCallbackUrl = '$baseUrl/callback/wave/success';
  static const String waveFailureCallbackUrl = '$baseUrl/callback/wave/failure';
}
