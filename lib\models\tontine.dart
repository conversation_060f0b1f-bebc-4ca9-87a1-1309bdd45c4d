// Renommer le fichier en cotisation.dart
// Et modifier le contenu :

class Tontine {
  final String id;
  final String userId;
  final String libelle;
  final double montantJournalier;
  final DateTime dateCreation;
  final List<Versement> versements;
  final double monnaie;
  
  Tontine({
    required this.id,
    required this.userId,
    required this.libelle,
    required this.montantJournalier,
    required this.dateCreation,
    required this.versements,
    required this.monnaie,
  });
  
  // Nombre total de versements pour la tontine (fixé à 21)
  int get totalVersements => 21;
  
  // Nombre de versements déjà effectués
  int get versementsEffectues => versements.length;
  
  // Nombre de versements restants
  int get versementsRestants => totalVersements - versementsEffectues;
  
  // Montant total à verser
  double get montantTotal => montantJournalier * totalVersements;
  
  // Montant déjà versé
  double get montantVerse => versements.fold(0.0, (sum, versement) => sum + versement.montant);
  
  // Montant restant à verser
  double get montantRestant => montantTotal - montantVerse;
  
  // Pourcentage de progression
  double get pourcentageProgression => (versementsEffectues / totalVersements) * 100;
}

class Versement {
  final String id;
  final double montant;
  final DateTime date;
  
  Versement({
    required this.id,
    required this.montant,
    required this.date,
  });
  
  factory Versement.fromJson(Map<String, dynamic> json) {
    return Versement(
      id: json['id'],
      montant: json['montant'],
      date: DateTime.parse(json['date']),
    );
  }
  
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'montant': montant,
      'date': date.toIso8601String(),
    };
  }
}
