import 'dart:convert';
import 'package:http/http.dart' as http;
import 'package:callitris/config/api_config.dart';
import 'package:callitris/services/auth_service.dart';

class WalletService {
  // Récupérer la monnaie de l'utilisateur
  static Future<Map<String, dynamic>> getUserMonnaie() async {
    try {
      // Récupérer les données utilisateur
      final userData = await AuthService.getUserData();
      
      if (userData == null || !userData.containsKey('id_client')) {
        return {
          'success': false,
          'message': 'Utilisateur non connecté ou ID client non disponible',
          'montant': 0,
        };
      }
      
      final clientId = userData['id_client'].toString();
      
      // Construire l'URL de l'API
      final apiUrl = '${ApiConfig.baseUrl}/client/getMonnaie.php?clientId=$clientId';
      
      print('Appel API monnaie: $apiUrl');
      
      // Effectuer la requête HTTP
      final response = await http.get(
        Uri.parse(apiUrl),
      ).timeout(const Duration(seconds: 15));
      
      print('Réponse API monnaie (status: ${response.statusCode}): ${response.body}');
      
      if (response.statusCode == 200) {
        final jsonResponse = jsonDecode(response.body);
        
        // Vérifier si la réponse contient le montant
        if (jsonResponse.containsKey('montant')) {
          return {
            'success': true,
            'montant': jsonResponse['montant'],
          };
        } else {
          return {
            'success': false,
            'message': 'Format de réponse invalide',
            'montant': 0,
          };
        }
      } else {
        return {
          'success': false,
          'message': 'Erreur serveur: ${response.statusCode}',
          'montant': 0,
        };
      }
    } catch (e) {
      print('Exception lors de la récupération de la monnaie: $e');
      return {
        'success': false,
        'message': 'Erreur de connexion: $e',
        'montant': 0,
      };
    }
  }
}