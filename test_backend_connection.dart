import 'dart:convert';
import 'dart:io';

/// Script de test pour vérifier la connexion avec le backend CinetPay
void main() async {
  print('🧪 Test de connexion avec le backend CinetPay...\n');
  
  const String backendUrl = 'https://dev-mani.io/client-api.callitris-distribution.com/paiement/init_cinetpay.php';
  
  // Test avec les nouveaux paramètres
  final testData = {
    'amount': 1000.0,
    'phone': '+22501020304',
  };
  
  print('📤 Envoi des données:');
  print('URL: $backendUrl');
  print('Données: ${jsonEncode(testData)}\n');
  
  try {
    final client = HttpClient();
    final request = await client.postUrl(Uri.parse(backendUrl));
    
    // Headers
    request.headers.set('Content-Type', 'application/json');
    request.headers.set('Accept', 'application/json');
    
    // Body
    request.add(utf8.encode(jsonEncode(testData)));
    
    final response = await request.close();
    final responseBody = await response.transform(utf8.decoder).join();
    
    print('📥 Réponse du serveur:');
    print('Status Code: ${response.statusCode}');
    print('Headers: ${response.headers}');
    print('Body: $responseBody\n');
    
    if (response.statusCode == 200) {
      try {
        final jsonResponse = jsonDecode(responseBody);
        print('✅ JSON décodé avec succès:');
        print('Success: ${jsonResponse['success']}');
        print('Message: ${jsonResponse['message']}');
        if (jsonResponse['payment_url'] != null) {
          print('Payment URL: ${jsonResponse['payment_url']}');
        }
      } catch (e) {
        print('❌ Erreur de décodage JSON: $e');
        print('Réponse brute: $responseBody');
      }
    } else {
      print('❌ Erreur HTTP: ${response.statusCode}');
    }
    
    client.close();
    
  } catch (e) {
    print('❌ Erreur de connexion: $e');
  }
  
  print('\n🏁 Test terminé.');
}
