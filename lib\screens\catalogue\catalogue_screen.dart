import 'dart:convert';

import 'package:callitris/config/api_config.dart';
import 'package:callitris/screens/promos/promo_screen.dart' as promo;
import 'package:callitris/screens/catalogue/all_categories_screen.dart';
import 'package:callitris/services/auth_service.dart';
import 'package:flutter/material.dart';
import 'package:callitris/utils/appTheme.dart';
import 'package:callitris/screens/catalogue/category_products_screen.dart';
import 'package:callitris/services/catalogue_service.dart';
import 'dart:async';
import 'package:http/http.dart' as http;
import '../boutique/product_detail_screen.dart';

class CatalogueScreen extends StatefulWidget {
  const CatalogueScreen({super.key});

  @override
  State<CatalogueScreen> createState() => _CatalogueScreenState();
}

class _CatalogueScreenState extends State<CatalogueScreen>
    with TickerProviderStateMixin {
  bool _isLoading = true;
  List<Map<String, dynamic>> _categories = [];
  List<Map<String, dynamic>> _featuredProducts = [];

  // Contrôleurs pour l'auto-scroll
  late ScrollController _bannerScrollController;
  late Timer _autoScrollTimer;
  int _currentBannerIndex = 0;
  List<Map<String, dynamic>> _promoProducts = [];

  @override
  void initState() {
    super.initState();
    _bannerScrollController = ScrollController();
    _startAutoScroll();
    _loadData();
    _loadPromoProducts();
  }

  @override
  void dispose() {
    _autoScrollTimer.cancel();
    _bannerScrollController.dispose();
    super.dispose();
  }

  void _startAutoScroll() {
    _autoScrollTimer = Timer.periodic(const Duration(seconds: 3), (timer) {
      if (_bannerScrollController.hasClients) {
        _currentBannerIndex = (_currentBannerIndex + 1) % 4; // 4 bannières

        final double targetOffset =
            _currentBannerIndex * 272.0; // 260 (largeur) + 12 (margin)

        _bannerScrollController.animateTo(
          targetOffset,
          duration: const Duration(milliseconds: 800),
          curve: Curves.easeInOut,
        );
      }
    });
  }

  // Charger les données publicitaires statiques
  Future<void> _loadPromoProducts() async {
    setState(() {
      _isLoading = true;
    });

    try {
      final token = await AuthService.getAuthToken();
      final String cat = "PROMO";
      if (token == null) {
        print(' Token manquant pour récupérer les publicités');
        setState(() {
          _promoProducts = [];
          _isLoading = false;
        });
        return;
      }

      print(
        ' Chargement des publicités depuis: ${ApiConfig.baseUrl}/pubs/getPub.php?pub=$cat',
      );

      final response = await http
          .get(
            Uri.parse("${ApiConfig.baseUrl}/pubs/getPub.php?pub=$cat"),
            headers: {'Authorization': 'Bearer $token'},
          )
          .timeout(const Duration(seconds: 15));

      print(' Statut de la réponse: ${response.statusCode}');
      print(' Corps de la réponse: ${response.body}');

      if (response.statusCode == 200) {
        final responseBody = response.body.trim();

        if (responseBody.isEmpty) {
          print(' Réponse vide de l\'API');
          setState(() {
            _promoProducts = [];
            _isLoading = false;
          });
          return;
        }

        try {
          // Nettoyer le JSON avant le parsing pour éviter les problèmes d'encodage
          String cleanedJson = responseBody
              .replaceAll(r'\u00e9', 'é')
              .replaceAll(r'\u00e8', 'è')
              .replaceAll(r'\u00ea', 'ê')
              .replaceAll(r'\u00e0', 'à')
              .replaceAll(r'\u00f9', 'ù')
              .replaceAll(r'\u00e7', 'ç');

          print(' JSON nettoyé: $cleanedJson');

          final List<dynamic> responseData = jsonDecode(cleanedJson);
          print(' Nombre de publicités reçues: ${responseData.length}');

          if (responseData.isEmpty) {
            print(' Aucune publicité trouvée dans la réponse');
            setState(() {
              _promoProducts = [];
              _isLoading = false;
            });
            return;
          }

          final List<Map<String, dynamic>> staticAds =
              responseData
                  .map((item) {
                    print(' Traitement de l\'item: $item');

                    // Vérifier que l'item contient les champs nécessaires
                    if (item == null || !item.containsKey('imageUrl')) {
                      print(' Item invalide ou sans imageUrl: $item');
                      return null;
                    }

                    String rawPath = item['imageUrl']?.toString() ?? '';
                    String cleanedPath = rawPath.replaceAll(r'\/', '/');

                    // Encoder correctement l'URL pour gérer les caractères spéciaux
                    String fullUrl;
                    try {
                      // Séparer le chemin et le nom de fichier
                      List<String> pathParts = cleanedPath.split('/');
                      if (pathParts.isNotEmpty) {
                        // Encoder seulement le nom de fichier (dernière partie)
                        String fileName = pathParts.last;
                        String encodedFileName = Uri.encodeComponent(fileName);
                        pathParts[pathParts.length - 1] = encodedFileName;
                        String encodedPath = pathParts.join('/');
                        fullUrl = "${ApiConfig.baseUrl2}/$encodedPath";
                      } else {
                        fullUrl = "${ApiConfig.baseUrl2}/$cleanedPath";
                      }
                    } catch (e) {
                      print('⚠️ Erreur d\'encodage URL: $e');
                      fullUrl = "${ApiConfig.baseUrl2}/$cleanedPath";
                    }

                    print('🖼️ URL image générée: $fullUrl');

                    return {
                      'id_publicite': item['id_publicite']?.toString() ?? '',
                      'name': item['name']?.toString() ?? 'Produit sans nom',
                      'description':
                          item['description']?.toString() ??
                          'Aucune description',
                      'price':
                          int.tryParse(item['price']?.toString() ?? '0') ?? 0,
                      'imageUrl': fullUrl,
                      'category': item['category']?.toString() ?? 'Général',
                      'isPromo': item['isPromo'] ?? true,
                    };
                  })
                  .where((item) => item != null)
                  .cast<Map<String, dynamic>>()
                  .toList();

          print('✅ ${staticAds.length} publicités traitées avec succès');

          setState(() {
            _promoProducts = staticAds;
            _isLoading = false;
          });
        } catch (jsonError) {
          print('❌ Erreur de parsing JSON: $jsonError');
          print('📄 Contenu reçu: $responseBody');
          setState(() {
            _promoProducts = [];
            _isLoading = false;
          });
        }
      } else {
        print('❌ Erreur HTTP: ${response.statusCode}');
        print('📄 Message d\'erreur: ${response.body}');
        setState(() {
          _promoProducts = [];
          _isLoading = false;
        });
      }
    } catch (e) {
      print('❌ Exception lors du chargement des produits: $e');
      setState(() {
        _promoProducts = [];
        _isLoading = false;
      });
    }
  }

  static String formatImageUrl(String imageUrl) {
    if (imageUrl.isEmpty) return '';

    if (imageUrl.startsWith('http://') || imageUrl.startsWith('https://')) {
      return imageUrl;
    }

    String cleanPath = imageUrl;

    // Enlever préfixes inutiles
    if (cleanPath.startsWith('app/')) {
      cleanPath = cleanPath.substring(4);
    }
    if (cleanPath.startsWith('public/')) {
      cleanPath = cleanPath.substring(7);
    }
    if (cleanPath.startsWith('../../')) {
      cleanPath = cleanPath.substring(6);
    }

    // Éviter les doublons de /public
    cleanPath = cleanPath.replaceAll(RegExp(r'(\/public)+\/'), '/');

    if (cleanPath.contains('img/kit/')) {
      return 'https://app.callitris-distribution.com/app/$cleanPath';
    } else {
      return 'https://app.callitris-distribution.com/app/$cleanPath';
    }
  }

  Future<void> _loadData() async {
    setState(() {
      _isLoading = true;
    });

    try {
      final categories = await CatalogueService.getCategories(5);

      if (categories.isNotEmpty) {
        final List<Map<String, dynamic>> formattedCategories =
            categories.map((category) {
              IconData icon = Icons.category;
              Color color = Colors.blue;

              final categoryName = category['nom_cat'].toString().toLowerCase();

              if (categoryName.contains('electronique') ||
                  categoryName.contains('telephone')) {
                icon = Icons.phone_android;
                color = Colors.blue;
              } else if (categoryName.contains('electroménager') ||
                  categoryName.contains('ustensile')) {
                icon = Icons.kitchen;
                color = Colors.green;
              } else if (categoryName.contains('textile') ||
                  categoryName.contains('couture')) {
                icon = Icons.shopping_bag;
                color = Colors.purple;
              } else if (categoryName.contains('mobilier') ||
                  categoryName.contains('chantier')) {
                icon = Icons.chair;
                color = Colors.orange;
              } else if (categoryName.contains('coiffure') ||
                  categoryName.contains('acessoire')) {
                icon = Icons.spa;
                color = Colors.pink;
              } else if (categoryName.contains('jouet') ||
                  categoryName.contains('scolaire')) {
                icon = Icons.sports_soccer;
                color = Colors.red;
              } else if (categoryName.contains('moto')) {
                icon = Icons.motorcycle;
                color = Colors.grey;
              } else if (categoryName.contains('vivres') ||
                  categoryName.contains('alimentaire')) {
                icon = Icons.restaurant;
                color = Colors.amber;
              }

              return {
                'id': category['id_categorie'].toString(),
                'name': category['nom_cat'],
                'icon': icon,
                'color': color,
                'image':
                    'assets/images/category_default.jpg',
              };
            }).toList();

        setState(() {
          _categories = formattedCategories;
        });
      }

      List<Map<String, dynamic>> featuredProducts = [];

      if (_categories.isNotEmpty) {
        final firstCategoryProducts =
            await CatalogueService.getProductsByCategoryNew(
              _categories[0]['id'],
            );

        if (firstCategoryProducts.isNotEmpty) {
          featuredProducts =
              firstCategoryProducts.take(4).map((product) {
                String imageUrl =
                    product['photo_kit'] ?? 'assets/images/product_default.jpg';

                return {
                  'id': product['id_kit'].toString(),
                  'name':
                      product['nom_produit'] ??
                      product['nom_kit'] ??
                      'Produit sans nom',
                  'price':
                      int.tryParse(
                        product['montant_total_kit']?.toString() ??
                            product['prix_kit']?.toString() ??
                            '0',
                      ) ??
                      0,
                  'dailyPrice':
                      int.tryParse(
                        product['cout_journalier_kit']?.toString() ??
                            product['journalier_kit']?.toString() ??
                            '0',
                      ) ??
                      0,
                  'imageUrl': formatImageUrl(imageUrl),
                  'discount': '15%',
                  'categoryId': _categories[0]['id'].toString(),
                  'description':
                      product['description_kit'] ??
                      'Aucune description disponible',
                  'inStock': true,
                };
              }).toList();
        }
      }

      setState(() {
        _featuredProducts = featuredProducts;
      });

      // ...
    } catch (e) {
      print('Erreur lors du chargement des données: $e');
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Boutique'),
        actions: [
          IconButton(
            icon: const Icon(Icons.search),
            onPressed: () {
              // la barre recherche
            },
          ),
        ],
      ),
      body:
          _isLoading
              ? const Center(child: CircularProgressIndicator())
              : RefreshIndicator(
                onRefresh: () async {
                  await _loadData();
                },
                child: SingleChildScrollView(
                  physics: const AlwaysScrollableScrollPhysics(),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      _buildHorizontalBanners(),
                      const SizedBox(height: 24),

                      _buildCategoriesSection(),

                      const SizedBox(height: 24),

                      _buildFeaturedProductsSection(),

                      const SizedBox(height: 24),

                      _buildPaymentOptionsSection(),

                      const SizedBox(height: 24),
                    ],
                  ),
                ),
              ),
    );
  }

  Widget _buildHorizontalBanners() {
    final List<Map<String, dynamic>> banners = _promoProducts;

    return SizedBox(
      height: 140,
      child: ListView.builder(
        controller: _bannerScrollController,
        scrollDirection: Axis.horizontal,
        padding: const EdgeInsets.symmetric(horizontal: 16),
        itemCount: banners.length,
        itemBuilder: (context, index) {
          final banner = banners[index];
          return _buildPromoBanner(
            imageUrl: banner['imageUrl'],
            title: banner['name'],
            subtitle: banner['description'],
            id: banner['id_publicite'],
          );
        },
      ),
    );
  }

  Widget _buildPromoBanner({
    String? id,
    String? imageUrl,
    String title = 'Nouveaux produits',
    String subtitle = 'Découvrez notre nouvelle collection',
    String buttonText = 'Voir plus',
    VoidCallback? onPressed,
    List<Color>? gradientColors,
    IconData? backgroundIcon,
  }) {
    final bool hasImage = imageUrl != null && imageUrl.isNotEmpty;
    final bool isNetworkImage =
        hasImage &&
        (imageUrl.startsWith('http') || imageUrl.contains('public/img'));

    return Container(
      width: 260,
      height: 120,
      margin: const EdgeInsets.only(
        right: 12,
      ),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(16),
        gradient:
            hasImage
                ? null
                : LinearGradient(
                  begin: Alignment.topLeft,
                  end: Alignment.bottomRight,
                  colors:
                      gradientColors ??
                      [
                        AppTheme.color.primaryColor,
                        AppTheme.color.secondaryColor,
                      ],
                ),
        boxShadow: [
          BoxShadow(
            color: AppTheme.color.primaryColor.withValues(alpha: 0.3),
            blurRadius: 10,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: ClipRRect(
        borderRadius: BorderRadius.circular(16),
        child: Stack(
          children: [
            if (hasImage)
              Positioned.fill(
                child:
                    isNetworkImage
                        ? Image.network(
                          imageUrl,
                          fit: BoxFit.cover,
                          errorBuilder: (context, error, stackTrace) {
                            return Container(
                              decoration: BoxDecoration(
                                gradient: LinearGradient(
                                  begin: Alignment.topLeft,
                                  end: Alignment.bottomRight,
                                  colors:
                                      gradientColors ??
                                      [
                                        AppTheme.color.primaryColor,
                                        AppTheme.color.secondaryColor,
                                      ],
                                ),
                              ),
                            );
                          },
                        )
                        : Image.asset(
                          imageUrl,
                          fit: BoxFit.cover,
                          errorBuilder: (context, error, stackTrace) {
                            return Container(
                              decoration: BoxDecoration(
                                gradient: LinearGradient(
                                  begin: Alignment.topLeft,
                                  end: Alignment.bottomRight,
                                  colors:
                                      gradientColors ??
                                      [
                                        AppTheme.color.primaryColor,
                                        AppTheme.color.secondaryColor,
                                      ],
                                ),
                              ),
                            );
                          },
                        ),
              ),

            if (hasImage)
              Positioned.fill(
                child: Container(
                  decoration: BoxDecoration(
                    gradient: LinearGradient(
                      begin: Alignment.topCenter,
                      end: Alignment.bottomCenter,
                      colors: [
                        Colors.black.withValues(alpha: 0.3),
                        Colors.black.withValues(alpha: 0.6),
                      ],
                    ),
                  ),
                ),
              ),

            if (!hasImage)
              Positioned(
                right: -15,
                bottom: -15,
                child: Icon(
                  backgroundIcon ?? Icons.shopping_cart,
                  size: 100,
                  color: Colors.white.withValues(alpha: 0.1),
                ),
              ),

            Padding(
              padding: const EdgeInsets.all(12),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Text(
                    title,
                    style: const TextStyle(
                      color: Colors.white,
                      fontSize: 18, 
                      fontWeight: FontWeight.bold,
                      shadows: [
                        Shadow(
                          offset: Offset(1, 1),
                          blurRadius: 3,
                          color: Colors.black45,
                        ),
                      ],
                    ),
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                  ),
                  const SizedBox(height: 4),
                  Text(
                    subtitle,
                    style: const TextStyle(
                      color: Colors.white,
                      fontSize: 12,
                      shadows: [
                        Shadow(
                          offset: Offset(1, 1),
                          blurRadius: 3,
                          color: Colors.black45,
                        ),
                      ],
                    ),
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                  ),
                  const SizedBox(height: 8),
                  ElevatedButton(
                    onPressed:
                        onPressed ??
                        () {
                          Navigator.push(
                            context,
                            MaterialPageRoute(
                              builder:
                                  (context) => promo.InfoScreen(infoId: id!),
                            ),
                          );
                        },
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.white,
                      foregroundColor: AppTheme.color.primaryColor,
                      padding: const EdgeInsets.symmetric(
                        horizontal: 12,
                        vertical: 6,
                      ),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(6),
                      ),
                      elevation: 2,
                      minimumSize: const Size(
                        0,
                        28,
                      ),
                    ),
                    child: Text(
                      buttonText,
                      style: const TextStyle(
                        fontSize: 12,
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildCategoriesSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Padding(
          padding: const EdgeInsets.symmetric(horizontal: 20),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Catégories',
                    style: TextStyle(
                      fontSize: 24,
                      fontWeight: FontWeight.bold,
                      color: AppTheme.color.textColor,
                    ),
                  ),
                  const SizedBox(height: 4),
                  Text(
                    'Explorez nos collections',
                    style: TextStyle(
                      fontSize: 14,
                      color: AppTheme.color.brunGris,
                    ),
                  ),
                ],
              ),
              Container(
                padding: const EdgeInsets.symmetric(
                  horizontal: 16,
                  vertical: 8,
                ),
                decoration: BoxDecoration(
                  color: AppTheme.color.primaryColor.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(20),
                ),
                child: GestureDetector(
                  onTap: () {
                    Navigator.push(
                      context,
                      MaterialPageRoute(
                        builder: (context) => const AllCategoriesScreen(),
                      ),
                    );
                  },
                  child: Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Text(
                        'Voir tout',
                        style: TextStyle(
                          color: AppTheme.color.primaryColor,
                          fontWeight: FontWeight.w600,
                          fontSize: 14,
                        ),
                      ),
                      const SizedBox(width: 4),
                      Icon(
                        Icons.arrow_forward_ios,
                        color: AppTheme.color.primaryColor,
                        size: 12,
                      ),
                    ],
                  ),
                ),
              ),
            ],
          ),
        ),
        const SizedBox(height: 20),
        SizedBox(
          height: 170,
          child: ListView.builder(
            padding: const EdgeInsets.symmetric(horizontal: 16),
            scrollDirection: Axis.horizontal,
            itemCount: _categories.length,
            itemBuilder: (context, index) {
              final category = _categories[index];
              return _buildModernCategoryCard(category, index);
            },
          ),
        ),
      ],
    );
  }

  Widget _buildModernCategoryCard(Map<String, dynamic> category, int index) {
    final List<List<Color>> gradients = [
       [const Color(0xFF667eea), const Color(0xFF764ba2)], // Violet-Bleu
      [const Color(0xFFf093fb), const Color(0xFFf5576c)], // Rose-Rouge
      [const Color.fromARGB(255, 50, 138, 215), const Color.fromARGB(255, 63, 192, 198)], // Bleu-Cyan
      [const Color.fromARGB(255, 39, 176, 85), const Color(0xFF38f9d7)], // Vert-Turquoise
      [const Color(0xFFfa709a), const Color(0xFFfee140)], // Rose-Jaune
    ];

    final gradient = gradients[index % gradients.length];

    return GestureDetector(
      onTap: () async {
        // Afficher un indicateur de chargement
        showDialog(
          context: context,
          barrierDismissible: false,
          builder:
              (context) => const Center(child: CircularProgressIndicator()),
        );

        try {
          final products = await CatalogueService.getProductsByCategoryNew(
            category['id'],
          );

          Navigator.pop(context);

          Navigator.push(
            context,
            MaterialPageRoute(
              builder:
                  (context) => CategoryProductsScreen(
                    categoryId: category['id'],
                    categoryName: category['name'],
                    initialProducts:
                        products
                            .map(
                              (product) => {
                                'id': product['id_kit'].toString(),
                                'name':
                                    product['nom_produit'] ??
                                    product['nom_kit'] ??
                                    'Produit sans nom',
                                'price':
                                    int.tryParse(
                                      product['montant_total_kit']
                                              ?.toString() ??
                                          product['prix_kit']?.toString() ??
                                          '0',
                                    ) ??
                                    0,
                                'cashPrice':
                                    (int.tryParse(
                                          product['montant_total_kit']
                                                  ?.toString() ??
                                              product['prix_kit']?.toString() ??
                                              '0',
                                        ) ??
                                        0) *
                                    0.85,
                                'dailyPrice':
                                    int.tryParse(
                                      product['cout_journalier_kit']
                                              ?.toString() ??
                                          product['journalier_kit']
                                              ?.toString() ??
                                          '0',
                                    ) ??
                                    0,
                                'imageUrl': formatImageUrl(
                                  product['photo_kit'] ??
                                      'assets/images/product_default.jpg',
                                ),
                                'rating': 4.5,
                                'inStock': true,
                                'description':
                                    product['description_kit'] ??
                                    'Aucune description disponible',
                                'categoryId': category['id'].toString(),
                                'popularity': 80,
                                'livraison':
                                    product['livraison'] ??
                                    'indisponible',
                                'delai_livraison':
                                    product['delai_livraison'] ?? 'pas de délai',
                                'garantie': product['garantie'] ?? 'aucune',
                              },
                            )
                            .toList(),
                  ),
            ),
          );
        } catch (e) {
          Navigator.pop(context);
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('Erreur lors du chargement des produits: $e'),
              backgroundColor: Colors.red,
            ),
          );
        }
      },
      child: Container(
        width: 140,
        margin: const EdgeInsets.only(right: 16),
        decoration: BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
            colors: gradient,
          ),
          borderRadius: BorderRadius.circular(20),
          boxShadow: [
            BoxShadow(
              color: gradient[0].withValues(alpha: 0.3),
              blurRadius: 15,
              offset: const Offset(0, 8),
            ),
          ],
        ),
        child: Stack(
          children: [
            Positioned(
              top: -20,
              right: -20,
              child: Container(
                width: 80,
                height: 80,
                decoration: BoxDecoration(
                  color: Colors.white.withValues(alpha: 0.1),
                  shape: BoxShape.circle,
                ),
              ),
            ),
            Positioned(
              bottom: -10,
              left: -10,
              child: Container(
                width: 40,
                height: 40,
                decoration: BoxDecoration(
                  color: Colors.white.withValues(alpha: 0.1),
                  shape: BoxShape.circle,
                ),
              ),
            ),
            Padding(
              padding: const EdgeInsets.all(20),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Container(
                    width: 50,
                    height: 50,
                    decoration: BoxDecoration(
                      color: Colors.white.withValues(alpha: 0.2),
                      borderRadius: BorderRadius.circular(15),
                      border: Border.all(
                        color: Colors.white.withValues(alpha: 0.3),
                        width: 1,
                      ),
                    ),
                    child: Icon(
                      category['icon'],
                      color: Colors.white,
                      size: 28,
                    ),
                  ),

                  const Spacer(),

                  // Nom de la catégorie
                  Text(
                    category['name'],
                    style: const TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                      color: Colors.white,
                      height: 1.2,
                    ),
                    maxLines: 2,
                    overflow: TextOverflow.ellipsis,
                  ),

                  const SizedBox(height: 4),

                  // Sous-titre
                  Text(
                    'Découvrir',
                    style: TextStyle(
                      fontSize: 12,
                      color: Colors.white.withValues(alpha: 0.8),
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildFeaturedProductsSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Padding(
          padding: const EdgeInsets.symmetric(horizontal: 16),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              const Text(
                'Produits en vedette',
                style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
              ),
              TextButton(
                onPressed: () {
                  Navigator.push(
                    context,
                    MaterialPageRoute(
                      builder:
                          (context) => CategoryProductsScreen(
                            categoryId: "featured",
                            categoryName: "Produits en vedette",
                            initialProducts:
                                _featuredProducts
                                    .map(
                                      (product) => {
                                        'id': product['id'],
                                        'name': product['name'],
                                        'price': product['price'],
                                        'cashPrice':
                                            (product['price'] * 0.85)
                                                .toInt(), // 15% de réduction
                                        'dailyPrice': product['dailyPrice'],
                                        'imageUrl': product['imageUrl'],
                                        'rating': 4.5, // Valeur par défaut
                                        'inStock': true, // Par défaut
                                        'description':
                                            product['description'] ??
                                            'Produit en vedette de notre catalogue',
                                        'categoryId': product['categoryId'],
                                        'popularity':
                                            90, // Valeur par défaut élevée pour les produits en vedette
                                        'livraison':
                                            product['livraison'] ??
                                            'indisponible',
                                        'delai_livraison':
                                            product['delai_livraison'] ??
                                            'pas de délai',
                                        'garantie':
                                            product['garantie'] ?? 'aucune',
                                      },
                                    )
                                    .toList(),
                          ),
                    ),
                  );
                },
                child: Text(
                  'Voir tout',
                  style: TextStyle(color: AppTheme.color.primaryColor),
                ),
              ),
            ],
          ),
        ),
        const SizedBox(height: 8),
        Padding(
          padding: const EdgeInsets.symmetric(horizontal: 16),
          child: GridView.builder(
            shrinkWrap: true,
            physics: const NeverScrollableScrollPhysics(),
            gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
              crossAxisCount: 2,
              childAspectRatio: 0.75,
              crossAxisSpacing: 12,
              mainAxisSpacing: 12,
            ),
            itemCount: _featuredProducts.length,
            itemBuilder: (context, index) {
              final product = _featuredProducts[index];
              return _buildProductItem(product);
            },
          ),
        ),
      ],
    );
  }

  Widget _buildProductItem(Map<String, dynamic> product) {
    final String imageUrl = product['imageUrl'];
    final bool isNetworkImage =
        imageUrl.startsWith('http://') ||
        imageUrl.startsWith('https://') ||
        imageUrl.contains('public/img') ||
        imageUrl.startsWith('/');

    String finalImageUrl = formatImageUrl(imageUrl);

    return GestureDetector(
      onTap: () {
        Navigator.push(
          context,
          MaterialPageRoute(
            builder:
                (context) => ProductDetailScreen(
                  product: {
                    ...product,
                    'imageUrl': finalImageUrl, 
                    'nombre_jour_cat': 150, 
                    'inStock': product['inStock'] ?? true,
                    'categoryName': 'Produit en vedette',
                  },
                ),
          ),
        );
      },
      child: Container(
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(16),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withValues(alpha: 0.05),
              blurRadius: 10,
              offset: const Offset(0, 4),
            ),
          ],
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Stack(
              children: [
                ClipRRect(
                  borderRadius: const BorderRadius.vertical(
                    top: Radius.circular(16),
                  ),
                  child:
                      isNetworkImage
                          ? Image.network(
                            finalImageUrl,
                            width: double.infinity,
                            height: 100,
                            fit: BoxFit.cover,
                            errorBuilder: (context, error, stackTrace) {
                              
                              return Container(
                                height: 100,
                                color: Colors.grey[200],
                                child: Center(
                                  child: Icon(
                                    Icons.image_not_supported,
                                    color: Colors.grey[400],
                                  ),
                                ),
                              );
                            },
                          )
                          : Image.asset(
                            finalImageUrl,
                            width: double.infinity,
                            height: 100,
                            fit: BoxFit.cover,
                            errorBuilder: (context, error, stackTrace) {
                              return Container(
                                height: 100,
                                color: Colors.grey[200],
                                child: Center(
                                  child: Icon(
                                    Icons.image_not_supported,
                                    color: Colors.grey[400],
                                  ),
                                ),
                              );
                            },
                          ),
                ),
                if (product['discount'] != null)
                  Positioned(
                    top: 8,
                    right: 8,
                    child: Container(
                      padding: const EdgeInsets.symmetric(
                        horizontal: 8,
                        vertical: 4,
                      ),
                      decoration: BoxDecoration(
                        color: AppTheme.color.orangeColor,
                        borderRadius: BorderRadius.circular(8),
                      ),
                      child: Text(
                        '-${product['discount']}',
                        style: const TextStyle(
                          color: Colors.white,
                          fontSize: 10,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ),
                  ),
              ],
            ),

            // Informations du produit
            Expanded(
              child: Padding(
                padding: const EdgeInsets.all(8),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Text(
                      product['name'],
                      style: const TextStyle(
                        fontWeight: FontWeight.bold,
                        fontSize: 13,
                      ),
                      maxLines: 2,
                      overflow: TextOverflow.ellipsis,
                    ),
                    const SizedBox(height: 2),
                    Text(
                      '${_formatPrice(product['price'])} FCFA',
                      style: TextStyle(
                        fontSize: 13,
                        fontWeight: FontWeight.bold,
                        color: AppTheme.color.primaryColor,
                      ),
                    ),
                    Text(
                      '${_formatPrice(product['dailyPrice'])} FCFA/jour',
                      style: TextStyle(
                        fontSize: 11,
                        color: AppTheme.color.brunGris,
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildPaymentOptionsSection() {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            'Options de paiement',
            style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
          ),
          const SizedBox(height: 16),
          Row(
            children: [
              // Expanded(
              //   child: _buildPaymentOptionCard(
              //     title: 'Paiement cash',
              //     description: 'Économisez jusqu\'à 15% sur votre achat',
              //     icon: Icons.payments_outlined,
              //     color: AppTheme.color.greenColor,
              //   ),
              // ),
              const SizedBox(width: 16),
              Expanded(
                child: _buildPaymentOptionCard(
                  title: 'Paiement échelonné',
                  description: 'Payez en petites tranches journalières',
                  icon: Icons.calendar_today_outlined,
                  color: AppTheme.color.primaryColor,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildPaymentOptionCard({
    required String title,
    required String description,
    required IconData icon,
    required Color color,
  }) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 10,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Container(
            width: 40,
            height: 40,
            decoration: BoxDecoration(
              color: color.withOpacity(0.1),
              shape: BoxShape.circle,
            ),
            child: Icon(icon, color: color, size: 20),
          ),
          const SizedBox(height: 12),
          Text(
            title,
            style: const TextStyle(fontWeight: FontWeight.bold, fontSize: 14),
          ),
          const SizedBox(height: 4),
          Text(
            description,
            style: TextStyle(
              fontSize: 12,
              color: AppTheme.color.brunGris,
              height: 1.3,
            ),
          ),
        ],
      ),
    );
  }

  String _formatPrice(int price) {
    return price.toString().replaceAllMapped(
      RegExp(r'(\d{1,3})(?=(\d{3})+(?!\d))'),
      (Match m) => '${m[1]} ',
    );
  }
}
