import 'package:flutter/material.dart';
import 'package:callitris/utils/appTheme.dart';
import 'package:callitris/utils/navigation_service.dart';
import 'package:callitris/screens/tontine/create_tontine_screen.dart';
import 'package:callitris/screens/tontine/tontine_detail_screen.dart';

class TontineListScreen extends StatefulWidget {
  const TontineListScreen({super.key});

  @override
  State<TontineListScreen> createState() => _TontineListScreenState();
}

class _TontineListScreenState extends State<TontineListScreen>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;
  final bool _isLoading = false;

  // Données fictives des tontines
  final List<Map<String, dynamic>> _myTontines = [
    {
      'id': '1',
      'name': 'cotisation Anniversaire',
      'amount': 5000,
      'frequency': 'Quotidien',
      'nextPayment': '15/06/2023',
      'status': 'active',
      'progress': 0.33, // 7 versements sur 21
      'versementsEffectues': 7,
      'versementsRestants': 14,
      'montantTotal': 105000, // 5000 * 21
      'montantVerse': 35000, // 5000 * 7
      'montantRestant': 70000, // 5000 * 14
      'monnaie': 2500,
    },
    {
      'id': '2',
      'name': 'cotisation Vacances',
      'amount': 10000,
      'frequency': 'Quotidien',
      'nextPayment': '30/06/2023',
      'status': 'active',
      'progress': 0.19, // 4 versements sur 21
      'versementsEffectues': 4,
      'versementsRestants': 17,
      'montantTotal': 210000, // 10000 * 21
      'montantVerse': 40000, // 10000 * 4
      'montantRestant': 170000, // 10000 * 17
      'monnaie': 0,
    },
    {
      'id': '3',
      'name': 'cotisation Mariage',
      'amount': 15000,
      'frequency': 'Quotidien',
      'nextPayment': '10/06/2023',
      'status': 'active',
      'progress': 0.14, // 3 versements sur 21
      'versementsEffectues': 3,
      'versementsRestants': 18,
      'montantTotal': 315000, // 15000 * 21
      'montantVerse': 45000, // 15000 * 3
      'montantRestant': 270000, // 15000 * 18
      'monnaie': 1000,
    },
  ];

  final List<Map<String, dynamic>> _availableTontines = [
    {
      'id': '4',
      'name': 'cotisation Quartier',
      'members': 20,
      'amount': 15000,
      'frequency': 'Mensuel',
      'startDate': '01/07/2023',
      'duration': '12 mois',
      'description': 'cotisation pour les résidents du quartier',
    },
    {
      'id': '5',
      'name': 'cotisation Étudiants',
      'members': 15,
      'amount': 10000,
      'frequency': 'Mensuel',
      'startDate': '01/08/2023',
      'duration': '18 mois',
      'description': 'cotisation pour les étudiants universitaires',
    },
    {
      'id': '6',
      'name': 'cotisation Sportive',
      'members': 10,
      'amount': 20000,
      'frequency': 'Mensuel',
      'startDate': '01/09/2023',
      'duration': '12 mois',
      'description': 'cotisation pour les membres d\'une équipe sportive',
    },
  ];

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 2, vsync: this);
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFFF8F9FA),
      appBar: AppBar(
        elevation: 0,
        backgroundColor: Colors.white,
        centerTitle: false,
        title: Text(
          'Mes Cotisations',
          style: TextStyle(
            fontSize: 24,
            fontWeight: FontWeight.w700,
            color: AppTheme.color.textColor,
            letterSpacing: -0.5,
          ),
        ),
        actions: [
          Container(
            margin: const EdgeInsets.only(right: 16),
            decoration: BoxDecoration(
              color: AppTheme.color.primaryColor.withOpacity(0.1),
              borderRadius: BorderRadius.circular(12),
            ),
            child: IconButton(
              icon: Icon(
                Icons.account_balance_wallet,
                color: AppTheme.color.primaryColor,
                size: 22,
              ),
              onPressed: () {
                _showMonnaieDialog(context);
              },
              tooltip: 'Voir la monnaie',
            ),
          ),
        ],
        bottom: PreferredSize(
          preferredSize: const Size.fromHeight(60),
          child: Container(
            decoration: BoxDecoration(
              color: Colors.white,
              border: Border(
                bottom: BorderSide(
                  color: Colors.grey.withOpacity(0.15),
                  width: 1,
                ),
              ),
            ),
            child: TabBar(
              controller: _tabController,
              labelColor: AppTheme.color.primaryColor,
              unselectedLabelColor: AppTheme.color.brunGris,
              indicatorColor: AppTheme.color.primaryColor,
              indicatorWeight: 3,
              indicatorSize: TabBarIndicatorSize.label,
              labelStyle: const TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.w700,
                letterSpacing: -0.3,
              ),
              unselectedLabelStyle: const TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.w500,
                letterSpacing: -0.3,
              ),
              tabs: const [
                Tab(text: 'Mes Cotisations'),
                Tab(text: 'Disponibles'),
              ],
            ),
          ),
        ),
      ),
      body:
          _isLoading
              ? Center(
                child: CircularProgressIndicator(
                  color: AppTheme.color.primaryColor,
                ),
              )
              : TabBarView(
                controller: _tabController,
                children: [_buildMyTontinesTab(), _buildAvailableTontinesTab()],
              ),
      floatingActionButton: FloatingActionButton(
        onPressed: () {
          routeAnimation(context, const CreateTontineScreen());
        },
        backgroundColor: AppTheme.color.primaryColor,
        elevation: 2,
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
        child: const Icon(Icons.add, size: 28),
      ),
    );
  }

  Widget _buildMyTontinesTab() {
    return _myTontines.isEmpty
        ? _buildEmptyState()
        : Column(
          children: [
            _buildTontinesSummary(),
            Expanded(
              child: ListView.builder(
                padding: const EdgeInsets.all(16),
                itemCount: _myTontines.length,
                itemBuilder: (context, index) {
                  final tontine = _myTontines[index];
                  return _buildTontineCard(
                    tontine['id'],
                    tontine['name'],
                    tontine['amount'],
                    null,
                    tontine['frequency'],
                    tontine['nextPayment'],
                    tontine['status'],
                    tontine['progress'],
                    tontine['versementsEffectues'],
                    tontine['versementsRestants'],
                    tontine['montantTotal'],
                    tontine['montantVerse'],
                    tontine['montantRestant'],
                    false,
                  );
                },
              ),
            ),
          ],
        );
  }

  Widget _buildTontinesSummary() {
    // Calculer le montant total des tontines
    int totalAmount = 0;
    for (var tontine in _myTontines) {
      totalAmount += tontine['montantVerse'] as int;
    }

    return Container(
      margin: const EdgeInsets.fromLTRB(16, 16, 16, 0),
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [
            AppTheme.color.primaryColor,
            Color.fromARGB(255, 41, 98, 255),
          ],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        borderRadius: BorderRadius.circular(20),
        boxShadow: [
          BoxShadow(
            color: AppTheme.color.primaryColor.withOpacity(0.2),
            blurRadius: 15,
            offset: const Offset(0, 5),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                'Total épargné',
                style: TextStyle(
                  color: Colors.white.withOpacity(0.9),
                  fontSize: 14,
                  fontWeight: FontWeight.w500,
                ),
              ),
              Container(
                padding: const EdgeInsets.symmetric(
                  horizontal: 10,
                  vertical: 4,
                ),
                decoration: BoxDecoration(
                  color: Colors.white.withOpacity(0.2),
                  borderRadius: BorderRadius.circular(20),
                ),
                child: Row(
                  children: [
                    Icon(Icons.trending_up, color: Colors.white, size: 14),
                    const SizedBox(width: 4),
                    Text(
                      '+15%',
                      style: TextStyle(
                        color: Colors.white,
                        fontSize: 12,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
          const SizedBox(height: 10),
          Text(
            '${_formatPrice(totalAmount)} FCFA',
            style: const TextStyle(
              color: Colors.white,
              fontSize: 28,
              fontWeight: FontWeight.bold,
              letterSpacing: -0.5,
            ),
          ),
          const SizedBox(height: 20),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              _buildSummaryItem(
                'Cotisations actives',
                _myTontines.length.toString(),
                Icons.check_circle_outline,
              ),
              _buildSummaryItem(
                'Prochain versement',
                _myTontines.isNotEmpty ? _myTontines[0]['nextPayment'] : '-',
                Icons.calendar_today,
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildSummaryItem(String label, String value, IconData icon) {
    return Row(
      children: [
        Container(
          padding: const EdgeInsets.all(8),
          decoration: BoxDecoration(
            color: Colors.white.withOpacity(0.2),
            borderRadius: BorderRadius.circular(10),
          ),
          child: Icon(icon, color: Colors.white, size: 16),
        ),
        const SizedBox(width: 10),
        Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              label,
              style: TextStyle(
                color: Colors.white.withOpacity(0.8),
                fontSize: 12,
                fontWeight: FontWeight.w500,
              ),
            ),
            Text(
              value,
              style: const TextStyle(
                color: Colors.white,
                fontSize: 14,
                fontWeight: FontWeight.bold,
              ),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildAvailableTontinesTab() {
    return _availableTontines.isEmpty
        ? Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(
                Icons.search,
                size: 60,
                color: AppTheme.color.brunGris.withOpacity(0.5),
              ),
              const SizedBox(height: 16),
              Text(
                'Aucune cotisation disponible',
                style: TextStyle(
                  color: AppTheme.color.brunGris,
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                ),
              ),
              const SizedBox(height: 8),
              Text(
                'Revenez plus tard pour découvrir de nouvelles opportunités',
                style: TextStyle(color: AppTheme.color.brunGris, fontSize: 14),
                textAlign: TextAlign.center,
              ),
            ],
          ),
        )
        : ListView.builder(
          padding: const EdgeInsets.all(16),
          itemCount: _availableTontines.length,
          itemBuilder: (context, index) {
            final tontine = _availableTontines[index];
            return _buildAvailableTontineCard(
              tontine['id'],
              tontine['name'],
              tontine['members'],
              tontine['amount'],
              tontine['frequency'],
              tontine['startDate'],
              tontine['duration'],
              tontine['description'],
            );
          },
        );
  }

  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Container(
            width: 120,
            height: 120,
            decoration: BoxDecoration(
              color: AppTheme.color.primaryColor.withOpacity(0.1),
              shape: BoxShape.circle,
            ),
            child: Icon(
              Icons.savings_outlined,
              size: 60,
              color: AppTheme.color.primaryColor,
            ),
          ),
          const SizedBox(height: 24),
          Text(
            'Aucune cotisation pour le moment',
            style: TextStyle(
              fontSize: 20,
              fontWeight: FontWeight.bold,
              color: AppTheme.color.textColor,
            ),
          ),
          const SizedBox(height: 12),
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 40),
            child: Text(
              'Créez votre première cotisation en cliquant sur le bouton +',
              style: TextStyle(color: AppTheme.color.brunGris, fontSize: 16),
              textAlign: TextAlign.center,
            ),
          ),
          const SizedBox(height: 32),
          ElevatedButton.icon(
            onPressed: () {
              routeAnimation(context, const CreateTontineScreen());
            },
            icon: const Icon(Icons.add),
            label: const Text('Créer une cotisation'),
            style: ElevatedButton.styleFrom(
              backgroundColor: AppTheme.color.primaryColor,
              foregroundColor: Colors.white,
              padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 14),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(12),
              ),
              elevation: 0,
              textStyle: const TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildTontineCard(
    String id,
    String name,
    int amount,
    int? members,
    String frequency,
    String nextPayment,
    String status,
    double progress,
    int versementsEffectues,
    int versementsRestants,
    int montantTotal,
    int montantVerse,
    int montantRestant,
    bool isAdmin,
  ) {
    // Générer une couleur aléatoire pastel pour l'icône
    final List<Color> iconColors = [
      Color(0xFF6C63FF), // Violet
      Color(0xFF4CAF50), // Vert
      Color(0xFFFF9800), // Orange
      Color(0xFF2196F3), // Bleu
      Color(0xFFE91E63), // Rose
    ];
    final List<IconData> icons = [
      Icons.savings,
      Icons.account_balance,
      Icons.monetization_on,
      Icons.currency_exchange,
      Icons.wallet,
    ];

    // Utiliser l'ID pour générer un index stable
    final int colorIndex = int.parse(id) % iconColors.length;
    final int iconIndex = int.parse(id) % icons.length;

    return Container(
      margin: const EdgeInsets.only(bottom: 16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(20),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.03),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Material(
        color: Colors.transparent,
        borderRadius: BorderRadius.circular(20),
        child: InkWell(
          onTap: () {
            routeAnimation(context, CotisationDetailScreen(cotisationId: id));
          },
          borderRadius: BorderRadius.circular(20),
          child: Padding(
            padding: const EdgeInsets.all(20.0),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    Container(
                      padding: const EdgeInsets.all(12),
                      decoration: BoxDecoration(
                        color: iconColors[colorIndex].withOpacity(0.1),
                        borderRadius: BorderRadius.circular(14),
                      ),
                      child: Icon(
                        icons[iconIndex],
                        color: iconColors[colorIndex],
                        size: 24,
                      ),
                    ),
                    const SizedBox(width: 16),
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            name,
                            style: TextStyle(
                              fontWeight: FontWeight.bold,
                              fontSize: 18,
                              color: AppTheme.color.textColor,
                              letterSpacing: -0.3,
                            ),
                            overflow: TextOverflow.ellipsis,
                          ),
                          const SizedBox(height: 4),
                          Text(
                            'Versement $versementsEffectues/21 • $frequency',
                            style: TextStyle(
                              color: AppTheme.color.brunGris,
                              fontSize: 13,
                            ),
                          ),
                        ],
                      ),
                    ),
                    Container(
                      padding: const EdgeInsets.symmetric(
                        horizontal: 12,
                        vertical: 6,
                      ),
                      decoration: BoxDecoration(
                        color: AppTheme.color.greenColor.withOpacity(0.1),
                        borderRadius: BorderRadius.circular(12),
                      ),
                      child: Text(
                        '${_formatPrice(amount)} FCFA',
                        style: TextStyle(
                          color: AppTheme.color.greenColor,
                          fontWeight: FontWeight.bold,
                          fontSize: 14,
                        ),
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 20),
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          'Montant versé',
                          style: TextStyle(
                            color: AppTheme.color.brunGris,
                            fontSize: 13,
                          ),
                        ),
                        const SizedBox(height: 4),
                        Text(
                          '${_formatPrice(montantVerse)} FCFA',
                          style: TextStyle(
                            fontWeight: FontWeight.bold,
                            fontSize: 16,
                            color: AppTheme.color.textColor,
                          ),
                        ),
                      ],
                    ),
                    Column(
                      crossAxisAlignment: CrossAxisAlignment.end,
                      children: [
                        Text(
                          'Montant restant',
                          style: TextStyle(
                            color: AppTheme.color.brunGris,
                            fontSize: 13,
                          ),
                        ),
                        const SizedBox(height: 4),
                        Text(
                          '${_formatPrice(montantRestant)} FCFA',
                          style: TextStyle(
                            fontWeight: FontWeight.bold,
                            fontSize: 16,
                            color: AppTheme.color.textColor,
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
                const SizedBox(height: 20),
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Text(
                      'Progression',
                      style: TextStyle(
                        color: AppTheme.color.brunGris,
                        fontSize: 13,
                      ),
                    ),
                    Text(
                      '${(progress * 100).toInt()}%',
                      style: TextStyle(
                        color: AppTheme.color.primaryColor,
                        fontWeight: FontWeight.bold,
                        fontSize: 14,
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 10),
                Stack(
                  children: [
                    Container(
                      height: 8,
                      width: double.infinity,
                      decoration: BoxDecoration(
                        color: Colors.grey[200],
                        borderRadius: BorderRadius.circular(4),
                      ),
                    ),
                    Container(
                      height: 8,
                      width: MediaQuery.of(context).size.width * 0.8 * progress,
                      decoration: BoxDecoration(
                        gradient: LinearGradient(
                          colors: [
                            iconColors[colorIndex],
                            iconColors[colorIndex].withOpacity(0.7),
                          ],
                          begin: Alignment.centerLeft,
                          end: Alignment.centerRight,
                        ),
                        borderRadius: BorderRadius.circular(4),
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 20),
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Row(
                      children: [
                        Icon(
                          Icons.calendar_today,
                          size: 16,
                          color: AppTheme.color.brunGris,
                        ),
                        const SizedBox(width: 6),
                        Text(
                          'Prochain: $nextPayment',
                          style: TextStyle(
                            color: AppTheme.color.brunGris,
                            fontSize: 14,
                          ),
                        ),
                      ],
                    ),
                    Container(
                      padding: const EdgeInsets.symmetric(
                        horizontal: 10,
                        vertical: 4,
                      ),
                      decoration: BoxDecoration(
                        color:
                            status == 'active'
                                ? AppTheme.color.greenColor.withOpacity(0.1)
                                : AppTheme.color.redColor.withOpacity(0.1),
                        borderRadius: BorderRadius.circular(8),
                      ),
                      child: Text(
                        status == 'active' ? 'Active' : 'Inactive',
                        style: TextStyle(
                          color:
                              status == 'active'
                                  ? AppTheme.color.greenColor
                                  : AppTheme.color.redColor,
                          fontWeight: FontWeight.bold,
                          fontSize: 12,
                        ),
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 20),
                Row(
                  children: [
                    Expanded(
                      child: OutlinedButton.icon(
                        onPressed: () {
                          routeAnimation(
                            context,
                            CotisationDetailScreen(cotisationId: id),
                          );
                        },
                        icon: Icon(
                          Icons.visibility,
                          size: 18,
                          color: AppTheme.color.primaryColor,
                        ),
                        label: Text(
                          'Détails',
                          style: TextStyle(
                            color: AppTheme.color.primaryColor,
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                        style: OutlinedButton.styleFrom(
                          side: BorderSide(
                            color: AppTheme.color.primaryColor.withOpacity(0.5),
                          ),
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(12),
                          ),
                          padding: const EdgeInsets.symmetric(vertical: 12),
                        ),
                      ),
                    ),
                    const SizedBox(width: 12),
                    Expanded(
                      child: ElevatedButton.icon(
                        onPressed: () {
                          _showVersementDialog(context, id, amount);
                        },
                        icon: Icon(
                          Icons.add_circle,
                          size: 18,
                          color: Colors.white,
                        ),
                        label: Text(
                          'Verser',
                          style: TextStyle(
                            color: Colors.white,
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                        style: ElevatedButton.styleFrom(
                          backgroundColor: AppTheme.color.primaryColor,
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(12),
                          ),
                          elevation: 0,
                          padding: const EdgeInsets.symmetric(vertical: 12),
                        ),
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildInfoItem(String label, String value, Color color) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          label,
          style: TextStyle(color: AppTheme.color.brunGris, fontSize: 14),
        ),
        const SizedBox(height: 4),
        Row(
          children: [
            Container(
              width: 4,
              height: 16,
              decoration: BoxDecoration(
                color: color,
                borderRadius: BorderRadius.circular(2),
              ),
            ),
            const SizedBox(width: 6),
            Text(
              value,
              style: TextStyle(
                fontWeight: FontWeight.bold,
                fontSize: 16,
                color: AppTheme.color.textColor,
              ),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildAvailableTontineCard(
    String id,
    String name,
    int members,
    int amount,
    String frequency,
    String startDate,
    String duration,
    String description,
  ) {
    return Container(
      margin: const EdgeInsets.only(bottom: 16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.04),
            blurRadius: 12,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Material(
        color: Colors.transparent,
        borderRadius: BorderRadius.circular(16),
        child: InkWell(
          onTap: () {
            _joinTontine(context, id, name);
          },
          borderRadius: BorderRadius.circular(16),
          child: Padding(
            padding: const EdgeInsets.all(20.0),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    Container(
                      padding: const EdgeInsets.all(12),
                      decoration: BoxDecoration(
                        color: AppTheme.color.primaryColor.withOpacity(0.1),
                        borderRadius: BorderRadius.circular(12),
                      ),
                      child: Icon(
                        Icons.group,
                        color: AppTheme.color.primaryColor,
                        size: 24,
                      ),
                    ),
                    const SizedBox(width: 16),
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            name,
                            style: TextStyle(
                              fontWeight: FontWeight.bold,
                              fontSize: 18,
                              color: AppTheme.color.textColor,
                            ),
                            overflow: TextOverflow.ellipsis,
                          ),
                          const SizedBox(height: 4),
                          Text(
                            '$members membres',
                            style: TextStyle(
                              color: AppTheme.color.brunGris,
                              fontSize: 14,
                            ),
                          ),
                        ],
                      ),
                    ),
                    Container(
                      padding: const EdgeInsets.symmetric(
                        horizontal: 12,
                        vertical: 6,
                      ),
                      decoration: BoxDecoration(
                        color: AppTheme.color.greenColor.withOpacity(0.1),
                        borderRadius: BorderRadius.circular(12),
                      ),
                      child: Text(
                        '${_formatPrice(amount)} FCFA',
                        style: TextStyle(
                          color: AppTheme.color.greenColor,
                          fontWeight: FontWeight.bold,
                          fontSize: 14,
                        ),
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 20),
                Row(
                  children: [
                    _buildInfoItem(
                      'Fréquence',
                      frequency,
                      AppTheme.color.orangeColor,
                    ),
                    const SizedBox(width: 24),
                    _buildInfoItem(
                      'Durée',
                      duration,
                      AppTheme.color.secondaryColor,
                    ),
                  ],
                ),
                const SizedBox(height: 20),
                Container(
                  padding: const EdgeInsets.all(12),
                  decoration: BoxDecoration(
                    color: Colors.grey[50],
                    borderRadius: BorderRadius.circular(12),
                    border: Border.all(color: Colors.grey[200]!, width: 1),
                  ),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        children: [
                          Icon(
                            Icons.info_outline,
                            size: 16,
                            color: AppTheme.color.brunGris,
                          ),
                          const SizedBox(width: 6),
                          Text(
                            'Description',
                            style: TextStyle(
                              color: AppTheme.color.brunGris,
                              fontSize: 14,
                              fontWeight: FontWeight.w500,
                            ),
                          ),
                        ],
                      ),
                      const SizedBox(height: 8),
                      Text(
                        description,
                        style: TextStyle(
                          color: AppTheme.color.textColor,
                          fontSize: 14,
                        ),
                      ),
                    ],
                  ),
                ),
                const SizedBox(height: 20),
                Row(
                  children: [
                    Icon(
                      Icons.calendar_today,
                      size: 16,
                      color: AppTheme.color.brunGris,
                    ),
                    const SizedBox(width: 6),
                    Text(
                      'Début: $startDate',
                      style: TextStyle(
                        color: AppTheme.color.brunGris,
                        fontSize: 14,
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 20),
                SizedBox(
                  width: double.infinity,
                  child: ElevatedButton.icon(
                    onPressed: () {
                      _joinTontine(context, id, name);
                    },
                    icon: Icon(
                      Icons.add_circle,
                      color: AppTheme.color.primaryColor,
                    ),
                    label: Text(
                      'Rejoindre',
                      style: TextStyle(color: AppTheme.color.primaryColor),
                    ),
                    style: ElevatedButton.styleFrom(
                      padding: const EdgeInsets.symmetric(
                        horizontal: 24,
                        vertical: 12,
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  void _showMonnaieDialog(BuildContext context) {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (BuildContext context) {
        return AlertDialog(
          title: Text(
            'Monnaie disponible',
            style: TextStyle(color: AppTheme.color.primaryColor),
          ),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Row(
                children: [
                  Icon(
                    Icons.account_balance_wallet,
                    color: AppTheme.color.orangeColor,
                  ),
                  const SizedBox(width: 12),
                  Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'Total monnaie',
                        style: TextStyle(
                          color: AppTheme.color.brunGris,
                          fontSize: 14,
                        ),
                      ),
                      Text(
                        '2 500 FCFA',
                        style: TextStyle(
                          color: AppTheme.color.primaryColor,
                          fontSize: 20,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ],
          ),
          actions: [
            TextButton(
              onPressed: () {
                Navigator.of(context).pop();
              },
              child: Text(
                'Fermer',
                style: TextStyle(color: AppTheme.color.primaryColor),
              ),
            ),
          ],
        );
      },
    );
  }

  void _showVersementDialog(
    BuildContext context,
    String tontineId,
    int montantJournalier,
  ) {
    final TextEditingController montantController = TextEditingController();
    montantController.text = montantJournalier.toString();

    // Variables pour le calcul en temps réel
    int versementsComplets = 1; // Par défaut, 1 jour
    double monnaie = 0;

    // Fonction pour calculer les jours et la monnaie
    void calculerVersement(String value) {
      final double montantVerse = double.tryParse(value) ?? 0;
      versementsComplets = (montantVerse / montantJournalier).floor();
      monnaie = montantVerse - (versementsComplets * montantJournalier);
    }

    // Calculer les valeurs initiales
    calculerVersement(montantController.text);

    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (BuildContext context) {
        return StatefulBuilder(
          builder: (context, setState) {
            return AlertDialog(
              title: Text(
                'Effectuer un versement',
                style: TextStyle(color: AppTheme.color.primaryColor),
              ),
              content: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Text(
                    'Montant par versement: ${_formatPrice(montantJournalier)} FCFA',
                    style: TextStyle(color: AppTheme.color.brunGris),
                  ),
                  const SizedBox(height: 16),
                  TextField(
                    controller: montantController,
                    keyboardType: TextInputType.number,
                    decoration: InputDecoration(
                      labelText: 'Montant à verser',
                      border: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(10),
                      ),
                      prefixIcon: Icon(
                        Icons.attach_money,
                        color: AppTheme.color.primaryColor,
                      ),
                    ),
                    onChanged: (value) {
                      setState(() {
                        calculerVersement(value);
                      });
                    },
                  ),
                  const SizedBox(height: 16),
                  Container(
                    padding: const EdgeInsets.all(12),
                    decoration: BoxDecoration(
                      color: AppTheme.color.primaryColor.withOpacity(0.1),
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: Column(
                      children: [
                        Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            Text(
                              'Nombre de jours:',
                              style: TextStyle(
                                color: AppTheme.color.brunGris,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                            Text(
                              '$versementsComplets jour${versementsComplets > 1 ? 's' : ''}',
                              style: TextStyle(
                                color: AppTheme.color.primaryColor,
                                fontWeight: FontWeight.bold,
                                fontSize: 16,
                              ),
                            ),
                          ],
                        ),
                        const SizedBox(height: 8),
                        Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            Text(
                              'Monnaie:',
                              style: TextStyle(
                                color: AppTheme.color.brunGris,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                            Text(
                              '${_formatPrice(monnaie.toInt())} FCFA',
                              style: TextStyle(
                                color: AppTheme.color.greenColor,
                                fontWeight: FontWeight.bold,
                                fontSize: 16,
                              ),
                            ),
                          ],
                        ),
                      ],
                    ),
                  ),
                ],
              ),
              actions: [
                TextButton(
                  onPressed: () {
                    Navigator.of(context).pop();
                  },
                  child: Text(
                    'Annuler',
                    style: TextStyle(color: AppTheme.color.brunGris),
                  ),
                ),
                ElevatedButton(
                  onPressed: () {
                    // Logique pour effectuer le versement
                    final double montantVerse =
                        double.tryParse(montantController.text) ?? 0;

                    if (montantVerse <= 0) {
                      ScaffoldMessenger.of(context).showSnackBar(
                        const SnackBar(
                          content: Text('Veuillez entrer un montant valide'),
                          backgroundColor: Colors.red,
                        ),
                      );
                      return;
                    }

                    // Dans une application réelle, mettre à jour la tontine dans la base de données

                    Navigator.of(context).pop();

                    // Afficher un message de confirmation
                    ScaffoldMessenger.of(context).showSnackBar(
                      SnackBar(
                        content: Text(
                          versementsComplets > 0
                              ? 'Versement de $versementsComplets jour(s) effectué avec succès${monnaie > 0 ? ' + ${_formatPrice(monnaie.toInt())} FCFA de monnaie' : ''}'
                              : 'Monnaie de ${_formatPrice(monnaie.toInt())} FCFA ajoutée',
                        ),
                        backgroundColor: AppTheme.color.greenColor,
                      ),
                    );
                  },
                  child: const Text('Verser'),
                ),
              ],
            );
          },
        );
      },
    );
  }

  void _joinTontine(
    BuildContext context,
    String tontineId,
    String tontineName,
  ) {
    // Logique pour rejoindre la tontine
    // Par exemple, vous pouvez naviguer vers une nouvelle page pour confirmer la participation
    // ou vous pouvez simplement afficher un message de confirmation
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (BuildContext context) {
        return AlertDialog(
          title: Text(
            'Rejoindre la cotisation',
            style: TextStyle(color: AppTheme.color.primaryColor),
          ),
          content: Text(
            'Voulez-vous vraiment rejoindre la cotisation "$tontineName" ?',
            style: TextStyle(color: AppTheme.color.brunGris),
          ),
          actions: [
            TextButton(
              onPressed: () {
                Navigator.of(context).pop();
              },
              child: Text(
                'Annuler',
                style: TextStyle(color: AppTheme.color.brunGris),
              ),
            ),
            ElevatedButton(
              onPressed: () {
                // Logique pour rejoindre la tontine
                Navigator.of(context).pop();

                // Afficher un message de confirmation
                ScaffoldMessenger.of(context).showSnackBar(
                  SnackBar(
                    content: Text(
                      'Vous avez rejoint la cotisation "$tontineName"',
                    ),
                    backgroundColor: AppTheme.color.greenColor,
                  ),
                );
              },
              child: Text(
                'Rejoindre',
                style: TextStyle(color: AppTheme.color.primaryColor),
              ),
            ),
          ],
        );
      },
    );
  }

  String _formatPrice(dynamic price) {
    // Convertir en entier si nécessaire
    final int priceInt = price is int ? price : price.toInt();
    final String priceString = priceInt.toString();
    final StringBuffer result = StringBuffer();

    for (int i = 0; i < priceString.length; i++) {
      if (i > 0 && (priceString.length - i) % 3 == 0) {
        result.write(' ');
      }
      result.write(priceString[i]);
    }

    return result.toString();
  }
}
