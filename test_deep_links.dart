import 'package:flutter/material.dart';
import 'package:callitris/services/deep_link_service.dart';
import 'package:callitris/config/api_config.dart';

/// Script de test pour vérifier les deep links Wave
void main() {
  runApp(const DeepLinkTestApp());
}

class DeepLinkTestApp extends StatelessWidget {
  const DeepLinkTestApp({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      title: 'Test Deep Links',
      home: const DeepLinkTestScreen(),
    );
  }
}

class DeepLinkTestScreen extends StatefulWidget {
  const DeepLinkTestScreen({Key? key}) : super(key: key);

  @override
  State<DeepLinkTestScreen> createState() => _DeepLinkTestScreenState();
}

class _DeepLinkTestScreenState extends State<DeepLinkTestScreen> {
  String _lastDeepLink = 'Aucun deep link reçu';
  List<String> _deepLinkHistory = [];

  @override
  void initState() {
    super.initState();
    _initializeDeepLinks();
  }

  void _initializeDeepLinks() {
    // Initialiser le service de deep links
    DeepLinkService.initialize(
      onLinkReceived: (String link) {
        setState(() {
          _lastDeepLink = link;
          _deepLinkHistory.insert(0, '${DateTime.now()}: $link');
          if (_deepLinkHistory.length > 10) {
            _deepLinkHistory.removeLast();
          }
        });
        
        print('🔗 Deep link reçu dans le test: $link');
      },
    );

    // Écouter le stream
    DeepLinkService.linkStream.listen((String link) {
      print('📱 Stream deep link: $link');
    });
  }

  void _testDeepLink(String link) {
    print('🧪 Test du deep link: $link');
    DeepLinkService.handleDeepLink(link);
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Test Deep Links Wave'),
        backgroundColor: Colors.blue,
      ),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Statut actuel
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text(
                      'Dernier Deep Link reçu:',
                      style: TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 8),
                    Text(
                      _lastDeepLink,
                      style: const TextStyle(
                        fontSize: 14,
                        fontFamily: 'monospace',
                      ),
                    ),
                  ],
                ),
              ),
            ),
            
            const SizedBox(height: 20),
            
            // Boutons de test
            const Text(
              'Tests de Deep Links:',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 10),
            
            // Test de succès
            ElevatedButton(
              onPressed: () => _testDeepLink(
                'callitris://payment/success?transaction_id=test123&amount=1000&order_id=456&status=success',
              ),
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.green,
                foregroundColor: Colors.white,
              ),
              child: const Text('Test Paiement Réussi'),
            ),
            
            const SizedBox(height: 8),
            
            // Test d'échec
            ElevatedButton(
              onPressed: () => _testDeepLink(
                'callitris://payment/failure?transaction_id=test124&error_message=Insufficient+funds&status=failed',
              ),
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.red,
                foregroundColor: Colors.white,
              ),
              child: const Text('Test Paiement Échoué'),
            ),
            
            const SizedBox(height: 8),
            
            // Test d'annulation
            ElevatedButton(
              onPressed: () => _testDeepLink(
                'callitris://payment/cancel?transaction_id=test125&status=cancelled',
              ),
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.orange,
                foregroundColor: Colors.white,
              ),
              child: const Text('Test Paiement Annulé'),
            ),
            
            const SizedBox(height: 8),
            
            // Test de retour général
            ElevatedButton(
              onPressed: () => _testDeepLink(
                'callitris://payment/return?transaction_id=test126',
              ),
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.blue,
                foregroundColor: Colors.white,
              ),
              child: const Text('Test Retour Général'),
            ),
            
            const SizedBox(height: 20),
            
            // Historique des deep links
            const Text(
              'Historique des Deep Links:',
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 10),
            
            Expanded(
              child: Card(
                child: Padding(
                  padding: const EdgeInsets.all(8.0),
                  child: ListView.builder(
                    itemCount: _deepLinkHistory.length,
                    itemBuilder: (context, index) {
                      return Padding(
                        padding: const EdgeInsets.symmetric(vertical: 4.0),
                        child: Text(
                          _deepLinkHistory[index],
                          style: const TextStyle(
                            fontSize: 12,
                            fontFamily: 'monospace',
                          ),
                        ),
                      );
                    },
                  ),
                ),
              ),
            ),
            
            const SizedBox(height: 20),
            
            // Informations de configuration
            Card(
              color: Colors.grey[100],
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text(
                      'Configuration:',
                      style: TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 8),
                    Text('App Scheme: ${ApiConfig.appScheme}'),
                    Text('Backend: ${ApiConfig.baseUrl}'),
                    const SizedBox(height: 8),
                    const Text(
                      'URLs de callback Wave:',
                      style: TextStyle(fontWeight: FontWeight.bold),
                    ),
                    Text('Success: ${ApiConfig.waveSuccessCallbackUrl}'),
                    Text('Failure: ${ApiConfig.waveFailureCallbackUrl}'),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  @override
  void dispose() {
    DeepLinkService.dispose();
    super.dispose();
  }
}
