/// Script de test pour vérifier l'extraction des URL Wave
void main() {
  print('🧪 Test d\'extraction des URL Wave...\n');
  
  // URL Wave reçue
  final waveUrl = 'wave://capture/https://pay.wave.com/c/cos-1yrvhw2d02334?a=100&c=XOF&m=CinetPay%20%2A%20Cine';
  
  print('URL originale: $waveUrl');
  
  // Test d'extraction
  if (waveUrl.startsWith('wave://capture/https://')) {
    final realWaveUrl = waveUrl.substring('wave://capture/'.length);
    print('URL extraite: $realWaveUrl');
    
    // Analyser l'URL extraite
    final uri = Uri.tryParse(realWaveUrl);
    if (uri != null) {
      print('✅ URL valide');
      print('Host: ${uri.host}');
      print('Path: ${uri.path}');
      print('Query parameters:');
      uri.queryParameters.forEach((key, value) {
        print('  $key: $value');
      });
    } else {
      print('❌ URL invalide');
    }
  } else {
    print('❌ Format non reconnu');
  }
  
  print('\n🏁 Test terminé.');
}
