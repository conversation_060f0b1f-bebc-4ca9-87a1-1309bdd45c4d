import 'package:flutter/material.dart';
import 'package:callitris/services/error_handling_service.dart';
import 'package:callitris/utils/optimized_animations.dart';

/// Widget d'erreur générique optimisé
class OptimizedErrorWidget extends StatelessWidget {
  final AppError error;
  final VoidCallback? onRetry;
  final String? retryLabel;
  final bool showDetails;

  const OptimizedErrorWidget({
    super.key,
    required this.error,
    this.onRetry,
    this.retryLabel,
    this.showDetails = false,
  });

  @override
  Widget build(BuildContext context) {
    return OptimizedFadeIn(
      child: Container(
        padding: const EdgeInsets.all(24),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // Icône d'erreur
            Container(
              width: 80,
              height: 80,
              decoration: BoxDecoration(
                color: _getErrorColor().withOpacity(0.1),
                shape: BoxShape.circle,
              ),
              child: Icon(
                _getErrorIcon(),
                size: 40,
                color: _getErrorColor(),
              ),
            ),
            
            const SizedBox(height: 24),
            
            // Titre de l'erreur
            Text(
              _getErrorTitle(),
              style: const TextStyle(
                fontSize: 20,
                fontWeight: FontWeight.bold,
                color: Colors.black87,
              ),
              textAlign: TextAlign.center,
            ),
            
            const SizedBox(height: 12),
            
            // Message d'erreur
            Text(
              _getErrorMessage(),
              style: const TextStyle(
                fontSize: 16,
                color: Colors.black54,
                height: 1.4,
              ),
              textAlign: TextAlign.center,
            ),
            
            // Détails de l'erreur (en mode debug)
            if (showDetails && error.details != null) ...[
              const SizedBox(height: 16),
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: Colors.grey[100],
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Text(
                  error.details!,
                  style: const TextStyle(
                    fontSize: 12,
                    color: Colors.black45,
                    fontFamily: 'monospace',
                  ),
                ),
              ),
            ],
            
            // Bouton de retry
            if (onRetry != null) ...[
              const SizedBox(height: 24),
              ElevatedButton.icon(
                onPressed: onRetry,
                icon: const Icon(Icons.refresh),
                label: Text(retryLabel ?? 'Réessayer'),
                style: ElevatedButton.styleFrom(
                  backgroundColor: _getErrorColor(),
                  foregroundColor: Colors.white,
                  padding: const EdgeInsets.symmetric(
                    horizontal: 24,
                    vertical: 12,
                  ),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(8),
                  ),
                ),
              ),
            ],
          ],
        ),
      ),
    );
  }

  Color _getErrorColor() {
    switch (error.type) {
      case ErrorType.network:
      case ErrorType.noInternet:
        return const Color(0xFFFF9800);
      case ErrorType.authentication:
        return const Color(0xFFF44336);
      case ErrorType.validation:
        return const Color(0xFF2196F3);
      case ErrorType.server:
      case ErrorType.timeout:
        return const Color(0xFF9C27B0);
      default:
        return const Color(0xFF607D8B);
    }
  }

  IconData _getErrorIcon() {
    switch (error.type) {
      case ErrorType.network:
      case ErrorType.noInternet:
        return Icons.wifi_off_outlined;
      case ErrorType.authentication:
        return Icons.lock_outline;
      case ErrorType.validation:
        return Icons.warning_outlined;
      case ErrorType.server:
        return Icons.cloud_off_outlined;
      case ErrorType.timeout:
        return Icons.access_time_outlined;
      default:
        return Icons.error_outline;
    }
  }

  String _getErrorTitle() {
    switch (error.type) {
      case ErrorType.network:
        return 'Problème de connexion';
      case ErrorType.noInternet:
        return 'Pas d\'Internet';
      case ErrorType.authentication:
        return 'Session expirée';
      case ErrorType.validation:
        return 'Données invalides';
      case ErrorType.server:
        return 'Erreur du serveur';
      case ErrorType.timeout:
        return 'Délai dépassé';
      default:
        return 'Une erreur s\'est produite';
    }
  }

  String _getErrorMessage() {
    switch (error.type) {
      case ErrorType.network:
        return 'Vérifiez votre connexion réseau et réessayez.';
      case ErrorType.noInternet:
        return 'Aucune connexion Internet détectée. Vérifiez vos paramètres réseau.';
      case ErrorType.authentication:
        return 'Votre session a expiré. Veuillez vous reconnecter.';
      case ErrorType.validation:
        return 'Les données saisies ne sont pas valides. Vérifiez vos informations.';
      case ErrorType.server:
        return 'Le serveur rencontre des difficultés. Réessayez plus tard.';
      case ErrorType.timeout:
        return 'La requête a pris trop de temps. Vérifiez votre connexion.';
      default:
        return error.message.isNotEmpty 
          ? error.message 
          : 'Une erreur inattendue s\'est produite.';
    }
  }
}

/// Widget d'erreur compact pour les listes
class CompactErrorWidget extends StatelessWidget {
  final AppError error;
  final VoidCallback? onRetry;

  const CompactErrorWidget({
    super.key,
    required this.error,
    this.onRetry,
  });

  @override
  Widget build(BuildContext context) {
    return OptimizedFadeIn(
      child: Container(
        padding: const EdgeInsets.all(16),
        margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
        decoration: BoxDecoration(
          color: _getErrorColor().withOpacity(0.1),
          borderRadius: BorderRadius.circular(12),
          border: Border.all(
            color: _getErrorColor().withOpacity(0.3),
            width: 1,
          ),
        ),
        child: Row(
          children: [
            Icon(
              _getErrorIcon(),
              color: _getErrorColor(),
              size: 24,
            ),
            const SizedBox(width: 12),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                mainAxisSize: MainAxisSize.min,
                children: [
                  Text(
                    _getErrorTitle(),
                    style: TextStyle(
                      fontSize: 14,
                      fontWeight: FontWeight.w600,
                      color: _getErrorColor(),
                    ),
                  ),
                  const SizedBox(height: 4),
                  Text(
                    error.message,
                    style: const TextStyle(
                      fontSize: 12,
                      color: Colors.black54,
                    ),
                    maxLines: 2,
                    overflow: TextOverflow.ellipsis,
                  ),
                ],
              ),
            ),
            if (onRetry != null) ...[
              const SizedBox(width: 12),
              IconButton(
                onPressed: onRetry,
                icon: const Icon(Icons.refresh),
                color: _getErrorColor(),
                iconSize: 20,
              ),
            ],
          ],
        ),
      ),
    );
  }

  Color _getErrorColor() {
    switch (error.type) {
      case ErrorType.network:
      case ErrorType.noInternet:
        return const Color(0xFFFF9800);
      case ErrorType.authentication:
        return const Color(0xFFF44336);
      case ErrorType.validation:
        return const Color(0xFF2196F3);
      case ErrorType.server:
      case ErrorType.timeout:
        return const Color(0xFF9C27B0);
      default:
        return const Color(0xFF607D8B);
    }
  }

  IconData _getErrorIcon() {
    switch (error.type) {
      case ErrorType.network:
      case ErrorType.noInternet:
        return Icons.wifi_off;
      case ErrorType.authentication:
        return Icons.lock_outline;
      case ErrorType.validation:
        return Icons.warning;
      case ErrorType.server:
        return Icons.cloud_off;
      case ErrorType.timeout:
        return Icons.access_time;
      default:
        return Icons.error;
    }
  }

  String _getErrorTitle() {
    switch (error.type) {
      case ErrorType.network:
        return 'Connexion';
      case ErrorType.noInternet:
        return 'Hors ligne';
      case ErrorType.authentication:
        return 'Session';
      case ErrorType.validation:
        return 'Validation';
      case ErrorType.server:
        return 'Serveur';
      case ErrorType.timeout:
        return 'Délai';
      default:
        return 'Erreur';
    }
  }
}

/// Widget d'état d'erreur pour les écrans complets
class ErrorStateWidget extends StatelessWidget {
  final AppError error;
  final VoidCallback? onRetry;
  final String? retryLabel;
  final Widget? customAction;

  const ErrorStateWidget({
    super.key,
    required this.error,
    this.onRetry,
    this.retryLabel,
    this.customAction,
  });

  @override
  Widget build(BuildContext context) {
    return Center(
      child: SingleChildScrollView(
        padding: const EdgeInsets.all(24),
        child: OptimizedErrorWidget(
          error: error,
          onRetry: onRetry,
          retryLabel: retryLabel,
          showDetails: false,
        ),
      ),
    );
  }
}

/// Mixin pour faciliter la gestion d'erreurs dans les widgets
mixin ErrorHandlingMixin<T extends StatefulWidget> on State<T> {
  /// Gère une erreur et l'affiche à l'utilisateur
  void handleError(
    dynamic error, {
    String? context,
    bool showToUser = true,
  }) {
    ErrorHandlingService.instance.handleError(
      error,
      context: context,
      showToUser: showToUser,
    );
  }

  /// Crée un widget d'erreur pour l'état actuel
  Widget buildErrorWidget(
    AppError error, {
    VoidCallback? onRetry,
    String? retryLabel,
  }) {
    return OptimizedErrorWidget(
      error: error,
      onRetry: onRetry,
      retryLabel: retryLabel,
    );
  }

  /// Crée un widget d'erreur compact
  Widget buildCompactErrorWidget(
    AppError error, {
    VoidCallback? onRetry,
  }) {
    return CompactErrorWidget(
      error: error,
      onRetry: onRetry,
    );
  }
}
