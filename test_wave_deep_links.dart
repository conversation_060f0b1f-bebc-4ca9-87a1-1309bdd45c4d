/// Test des deep links Wave pour Callitris
///
/// Ce script teste la génération et la validation des deep links
/// pour les retours de paiement Wave

import 'package:callitris/config/api_config.dart';
import 'package:callitris/services/deep_link_service.dart';

void main() {
  print('🧪 Test des Deep Links Wave pour Callitris\n');

  // Test 1: Génération des deep links
  print('📱 Test 1: Génération des deep links');
  testDeepLinkGeneration();

  print('\n' + '=' * 50 + '\n');

  // Test 2: Validation des deep links
  print('🔍 Test 2: Validation des deep links');
  testDeepLinkValidation();

  print('\n' + '=' * 50 + '\n');

  // Test 3: URLs de callback backend
  print('🌐 Test 3: URLs de callback backend');
  testBackendCallbackUrls();

  print('\n' + '=' * 50 + '\n');

  // Test 4: Parsing des paramètres
  print('📊 Test 4: Parsing des paramètres');
  testParameterParsing();

  print('\n🏁 Tests terminés.');
}

/// Test de génération des deep links
void testDeepLinkGeneration() {
  // Test deep link de succès
  final successLink = DeepLinkService.generateAppDeepLink(
    '/success',
    params: {
      'transaction_id': 'wave_123456',
      'amount': '1000',
      'order_id': '789',
      'status': 'success',
      'payment_method': 'wave',
    },
  );

  print('✅ Deep link de succès:');
  print('   $successLink');

  // Test deep link d'échec
  final failureLink = DeepLinkService.generateAppDeepLink(
    '/failure',
    params: {
      'transaction_id': 'wave_123457',
      'error_message': 'Insufficient funds',
      'status': 'failed',
    },
  );

  print('❌ Deep link d\'échec:');
  print('   $failureLink');

  // Test deep link d'annulation
  final cancelLink = DeepLinkService.generateAppDeepLink(
    '/cancel',
    params: {'transaction_id': 'wave_123458', 'status': 'cancelled'},
  );

  print('🚫 Deep link d\'annulation:');
  print('   $cancelLink');
}

/// Test de validation des deep links
void testDeepLinkValidation() {
  final testLinks = [
    'callitris://payment/success?transaction_id=123',
    'callitris://payment/failure?error=test',
    'https://api.callitris-distribution.com/client-api.callitris-distribution.com/callback/wave/success',
    'invalid://link',
    'callitris://invalid/path',
  ];

  for (final link in testLinks) {
    final isValid = DeepLinkService.isValidAppDeepLink(link);
    final status = isValid ? '✅ Valide' : '❌ Invalide';
    print('$status: $link');
  }
}

/// Test des URLs de callback backend
void testBackendCallbackUrls() {
  print('🔗 URLs de callback configurées:');
  print('   Success: ${ApiConfig.waveSuccessCallbackUrl}');
  print('   Failure: ${ApiConfig.waveFailureCallbackUrl}');
  print('   Return:  ${ApiConfig.waveReturnUrl}');
  print('   Notify:  ${ApiConfig.waveNotifyUrl}');
  print('   Cancel:  ${ApiConfig.waveCancelUrl}');

  print('\n📋 URLs à configurer dans Wave:');
  print('   Return URL: ${ApiConfig.waveReturnUrl}');
  print('   Notify URL: ${ApiConfig.waveNotifyUrl}');
  print('   Cancel URL: ${ApiConfig.waveCancelUrl}');
}

/// Test du parsing des paramètres
void testParameterParsing() {
  final testUrl =
      'callitris://payment/success?transaction_id=wave_123&amount=1000&order_id=456&status=success&timestamp=1234567890';

  print('🔍 URL de test: $testUrl');

  final uri = Uri.tryParse(testUrl);
  if (uri != null) {
    print('✅ URL parsée avec succès:');
    print('   Scheme: ${uri.scheme}');
    print('   Host: ${uri.host}');
    print('   Path: ${uri.path}');
    print('   Paramètres:');

    uri.queryParameters.forEach((key, value) {
      print('     $key: $value');
    });

    // Test d'extraction des paramètres importants
    final transactionId = uri.queryParameters['transaction_id'];
    final amount = uri.queryParameters['amount'];
    final orderId = uri.queryParameters['order_id'];
    final status = uri.queryParameters['status'];

    print('\n📊 Paramètres extraits:');
    print('   Transaction ID: $transactionId');
    print('   Montant: $amount FCFA');
    print('   Commande ID: $orderId');
    print('   Statut: $status');
  } else {
    print('❌ Impossible de parser l\'URL');
  }
}

/// Fonction utilitaire pour tester les callbacks
void testCallbackSimulation() {
  print('\n🧪 Simulation de callbacks Wave:');

  // Simulation d'un callback de succès
  final successCallback = {
    'transaction_id': 'wave_test_123',
    'status': 'success',
    'amount': '1000',
    'order_id': '456',
    'currency': 'XOF',
    'payment_method': 'wave',
    'timestamp': DateTime.now().millisecondsSinceEpoch.toString(),
  };

  print('📤 Callback de succès simulé:');
  successCallback.forEach((key, value) {
    print('   $key: $value');
  });

  // Génération du deep link correspondant
  final deepLink = DeepLinkService.generateAppDeepLink(
    '/success',
    params: successCallback,
  );
  print('\n🔗 Deep link généré: $deepLink');
}

/// Fonction pour tester la configuration complète
void testCompleteConfiguration() {
  print('\n🔧 Test de la configuration complète:');

  print('📱 Configuration mobile:');
  print('   App Scheme: ${ApiConfig.appScheme}');
  print('   Deep Links:');
  print('     Success: ${ApiConfig.paymentSuccessDeepLink}');
  print('     Failure: ${ApiConfig.paymentFailureDeepLink}');
  print('     Cancel:  ${ApiConfig.paymentCancelDeepLink}');
  print('     Return:  ${ApiConfig.paymentReturnDeepLink}');

  print('\n🌐 Configuration backend:');
  print('   Base URL: ${ApiConfig.baseUrl}');
  print('   Wave Callbacks:');
  print('     Success: ${ApiConfig.waveSuccessCallbackUrl}');
  print('     Failure: ${ApiConfig.waveFailureCallbackUrl}');
  print('     Return:  ${ApiConfig.waveReturnUrl}');
  print('     Notify:  ${ApiConfig.waveNotifyUrl}');
  print('     Cancel:  ${ApiConfig.waveCancelUrl}');
}

/// Point d'entrée pour tous les tests
void runAllTests() {
  main();
  testCallbackSimulation();
  testCompleteConfiguration();
}
