# 🌊 Gestionnaire de Webhooks Wave pour Callitris

Ce système traite automatiquement les webhooks envoyés par Wave après les paiements et met à jour votre base de données en conséquence.

## 📁 Fichiers Inclus

- `wave_webhook_handler.php` - Gestionnaire principal des webhooks
- `wave_webhook_config.php` - Fichier de configuration (template)
- `test_wave_webhook.php` - Interface de test et de monitoring
- `wave_database_setup.sql` - Script de création des tables
- `WAVE_WEBHOOK_README.md` - Cette documentation

## 🚀 Installation Rapide

### 1. Configuration de la Base de Données

```sql
-- Exécutez le script SQL pour créer les tables
mysql -u your_username -p your_database < wave_database_setup.sql
```

### 2. Configuration PHP

Modifiez les paramètres dans `wave_webhook_handler.php` :

```php
// Configuration de la base de données
define('DB_HOST', 'localhost');
define('DB_NAME', 'callitris');
define('DB_USER', 'your_username');
define('DB_PASS', 'your_password');

// Clé secrète Wave (optionnel mais recommandé)
define('WAVE_WEBHOOK_SECRET', 'your_wave_webhook_secret');
```

### 3. Configuration Wave

Dans votre dashboard Wave, configurez l'URL du webhook :
```
https://api.callitris-distribution.com/client-api.callitris-distribution.com/webhooks/wave
```

### 4. Test

Accédez à `test_wave_webhook.php` pour tester le système avant la mise en production.

## 📊 Types de Webhooks Supportés

### ✅ Paiement Réussi
```json
{
  "id": "AE_ijzo7oGgrlM7",
  "type": "checkout.session.completed",
  "data": {
    "id": "cos-1b01sghpg100j",
    "amount": "100",
    "checkout_status": "complete",
    "client_reference": null,
    "currency": "XOF",
    "payment_status": "succeeded",
    "transaction_id": "TCN4Y4ZC3FM",
    "when_completed": "2022-11-08T15:05:45Z"
  }
}
```

### ❌ Paiement Échoué
```json
{
  "id": "EV_QvEZuDSQbLdI",
  "type": "checkout.session.payment_failed",
  "data": {
    "id": "cos-18qq25rgr100a",
    "amount": "1000",
    "checkout_status": "failed",
    "client_reference": "1f31dfd7-aec8-4adf-84ff-4a9c1981be2a",
    "currency": "XOF",
    "payment_status": "failed"
  }
}
```

## 🗄️ Structure de la Base de Données

### Table `wave_transactions`
Stocke toutes les transactions Wave avec leur statut.

### Table `wave_callback_logs`
Enregistre tous les webhooks reçus pour le debugging.

### Table `commandes` (mise à jour)
Mise à jour automatique du statut de paiement des commandes.

## 🔄 Mapping des Statuts

| Wave checkout_status | Statut Transaction | Description |
|---------------------|-------------------|-------------|
| `complete` | `success` | Paiement réussi |
| `failed` | `failed` | Paiement échoué |
| `pending` | `pending` | En attente |
| `cancelled` | `cancelled` | Annulé |

## 🛠️ Fonctionnalités

### ✨ Traitement Automatique
- Validation des données reçues
- Mise à jour de la table `wave_transactions`
- Mise à jour de la table `commandes`
- Logging détaillé de toutes les opérations

### 🔒 Sécurité
- Vérification de signature HMAC (optionnel)
- Validation stricte des données
- Protection contre les requêtes malformées
- Logs de sécurité

### 📝 Logging
- Logs détaillés avec niveaux (DEBUG, INFO, WARNING, ERROR)
- Enregistrement de tous les webhooks dans la base
- Rotation automatique des logs (optionnel)

## 🧪 Tests

### Test Automatique
```bash
# Test de paiement réussi
curl -X GET "https://votre-domaine.com/path/to/wave_webhook_handler.php?test=1"

# Test de paiement échoué
curl -X GET "https://votre-domaine.com/path/to/wave_webhook_handler.php?test=1&type=failed"
```

### Interface de Test
Utilisez `test_wave_webhook.php` pour :
- Tester différents scénarios
- Vérifier la configuration
- Monitorer les logs
- Tester avec des données personnalisées

## 📋 Monitoring

### Vérification des Logs
```bash
tail -f server_config/logs/wave_webhooks.log
```

### Requêtes SQL Utiles
```sql
-- Voir les transactions récentes
SELECT * FROM wave_transactions ORDER BY created_at DESC LIMIT 10;

-- Voir les webhooks en échec
SELECT * FROM wave_callback_logs WHERE status_code != 200;

-- Statistiques des paiements
SELECT status, COUNT(*) as count, SUM(amount) as total 
FROM wave_transactions 
GROUP BY status;
```

## 🚨 Dépannage

### Problèmes Courants

1. **Erreur de connexion à la base de données**
   - Vérifiez les paramètres DB_HOST, DB_NAME, DB_USER, DB_PASS
   - Assurez-vous que l'utilisateur a les permissions nécessaires

2. **Webhook non reçu**
   - Vérifiez l'URL configurée dans Wave
   - Assurez-vous que le serveur est accessible depuis Internet
   - Vérifiez les logs du serveur web

3. **Signature invalide**
   - Vérifiez que WAVE_WEBHOOK_SECRET correspond à celui configuré dans Wave
   - Laissez vide pour désactiver la vérification (non recommandé en production)

4. **Permissions de fichiers**
   - Le dossier `logs/` doit être accessible en écriture
   - `chmod 755 logs/` ou `chmod 777 logs/` si nécessaire

### Logs de Debug
Changez `LOG_LEVEL` à `'DEBUG'` pour plus de détails :
```php
define('LOG_LEVEL', 'DEBUG');
```

## 🔧 Configuration Avancée

### Variables d'Environnement
Pour plus de sécurité, utilisez des variables d'environnement :
```php
define('DB_HOST', $_ENV['DB_HOST'] ?? 'localhost');
define('DB_PASS', $_ENV['DB_PASS'] ?? 'default_password');
```

### Notifications Email
Ajoutez des notifications en cas d'erreur :
```php
// Dans la fonction logMessage()
if ($level === 'ERROR' && defined('ADMIN_EMAIL')) {
    mail(ADMIN_EMAIL, 'Erreur Webhook Wave', $message);
}
```

### Limitation d'IP
Restreignez l'accès aux IPs de Wave :
```php
$allowedIPs = ['IP_WAVE_1', 'IP_WAVE_2'];
if (!in_array($_SERVER['REMOTE_ADDR'], $allowedIPs)) {
    http_response_code(403);
    exit('Accès refusé');
}
```

## 📞 Support

Pour toute question ou problème :
1. Consultez les logs détaillés
2. Utilisez l'interface de test
3. Vérifiez la documentation Wave
4. Contactez l'équipe de développement Callitris

---

**Version:** 1.0  
**Dernière mise à jour:** 2024  
**Compatibilité:** PHP 7.4+, MySQL 5.7+
