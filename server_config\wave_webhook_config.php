<?php
/**
 * Configuration pour le gestionnaire de webhooks Wave
 * 
 * Copiez ce fichier et renommez-le en 'wave_webhook_config_local.php'
 * puis modifiez les valeurs selon votre environnement
 */

// Configuration de la base de données
define('DB_HOST', 'localhost');
define('DB_NAME', 'callitris');
define('DB_USER', 'your_username');
define('DB_PASS', 'your_password');
define('DB_CHARSET', 'utf8mb4');

// Configuration des logs
define('LOG_FILE', __DIR__ . '/logs/wave_webhooks.log');
define('LOG_LEVEL', 'INFO'); // DEBUG, INFO, WARNING, ERROR

// Clé secrète Wave pour vérifier la signature
// Obtenez cette clé depuis votre dashboard Wave
define('WAVE_WEBHOOK_SECRET', 'your_wave_webhook_secret_here');

// Configuration des URLs
define('WEBHOOK_URL', 'https://api.callitris-distribution.com/client-api.callitris-distribution.com/webhooks/wave');

// Configuration des notifications (optionnel)
define('ADMIN_EMAIL', '<EMAIL>');
define('NOTIFY_ON_ERROR', true);
define('NOTIFY_ON_SUCCESS', false);

// Configuration de sécurité
define('ALLOWED_IPS', [
    // IPs de Wave (à configurer selon la documentation Wave)
    '0.0.0.0', // Remplacez par les vraies IPs de Wave
]);

// Configuration des timeouts
define('DB_TIMEOUT', 30);
define('LOG_RETENTION_DAYS', 30);

?>
