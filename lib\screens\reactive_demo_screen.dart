import 'package:flutter/material.dart';
import 'package:callitris/widgets/reactive_user_widget.dart';
import 'package:callitris/widgets/reactive_orders_widget.dart';
import 'package:callitris/widgets/reactive_app_widget.dart';
import 'package:callitris/services/auth_service.dart';
import 'package:callitris/services/order_service.dart';
import 'package:callitris/services/app_state_service.dart';

/// Écran de démonstration des widgets réactifs
/// Montre comment l'application se met à jour automatiquement
class ReactiveDemoScreen extends StatefulWidget {
  const ReactiveDemoScreen({Key? key}) : super(key: key);

  @override
  State<ReactiveDemoScreen> createState() => _ReactiveDemoScreenState();
}

class _ReactiveDemoScreenState extends State<ReactiveDemoScreen> 
    with ReactiveWidgetMixin {
  
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Réactivité de l\'App'),
        actions: [
          // Indicateur de chargement global
          const ReactiveLoadingIndicator(
            child: Padding(
              padding: EdgeInsets.all(16),
              child: SizedBox(
                width: 20,
                height: 20,
                child: CircularProgressIndicator(strokeWidth: 2),
              ),
            ),
          ),
          
          // Bouton de rafraîchissement
          IconButton(
            onPressed: () => refreshAllData(),
            icon: const Icon(Icons.refresh),
            tooltip: 'Actualiser toutes les données',
          ),
        ],
      ),
      
      body: ReactiveRefreshWidget(
        child: SingleChildScrollView(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Statut de connectivité
              const ReactiveConnectivityWidget(),
              
              const SizedBox(height: 16),
              
              // Section utilisateur réactive
              _buildUserSection(),
              
              const SizedBox(height: 24),
              
              // Section commandes réactive
              _buildOrdersSection(),
              
              const SizedBox(height: 24),
              
              // Section statistiques
              _buildStatsSection(),
              
              const SizedBox(height: 24),
              
              // Section actions de test
              _buildTestActionsSection(),
            ],
          ),
        ),
      ),
    );
  }
  
  /// Section utilisateur avec widgets réactifs
  Widget _buildUserSection() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                const Icon(Icons.person, color: Colors.blue),
                const SizedBox(width: 8),
                Text(
                  'Profil Utilisateur',
                  style: Theme.of(context).textTheme.titleLarge,
                ),
                const Spacer(),
                IconButton(
                  onPressed: () => AuthService.refreshUserData(),
                  icon: const Icon(Icons.refresh, size: 20),
                  tooltip: 'Actualiser le profil',
                ),
              ],
            ),
            
            const SizedBox(height: 16),
            
            // Statut de connexion réactif
            ReactiveConnectionStatus(
              builder: (isLoggedIn, isLoading) {
                if (isLoading) {
                  return const Row(
                    children: [
                      SizedBox(
                        width: 16,
                        height: 16,
                        child: CircularProgressIndicator(strokeWidth: 2),
                      ),
                      SizedBox(width: 8),
                      Text('Vérification...'),
                    ],
                  );
                }
                
                return Row(
                  children: [
                    Icon(
                      isLoggedIn ? Icons.check_circle : Icons.error,
                      color: isLoggedIn ? Colors.green : Colors.red,
                      size: 16,
                    ),
                    const SizedBox(width: 8),
                    Text(
                      isLoggedIn ? 'Connecté' : 'Déconnecté',
                      style: TextStyle(
                        color: isLoggedIn ? Colors.green : Colors.red,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ],
                );
              },
            ),
            
            const SizedBox(height: 12),
            
            // Informations utilisateur réactives
            ReactiveUserWidget(
              notLoggedInWidget: const Text(
                'Veuillez vous connecter pour voir vos informations',
                style: TextStyle(color: Colors.grey),
              ),
              builder: (context, userData, isLoggedIn) {
                if (!isLoggedIn) {
                  return const SizedBox.shrink();
                }
                
                return Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      children: [
                        const ReactiveUserAvatar(radius: 25),
                        const SizedBox(width: 12),
                        Expanded(
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              ReactiveUserName(
                                textStyle: Theme.of(context).textTheme.titleMedium,
                              ),
                              if (userData?['telephone'] != null)
                                Text(
                                  userData!['telephone'],
                                  style: Theme.of(context).textTheme.bodySmall,
                                ),
                            ],
                          ),
                        ),
                      ],
                    ),
                    
                    const SizedBox(height: 12),
                    
                    // Solde réactif
                    Container(
                      padding: const EdgeInsets.all(12),
                      decoration: BoxDecoration(
                        color: Colors.green.withOpacity(0.1),
                        borderRadius: BorderRadius.circular(8),
                      ),
                      child: Row(
                        children: [
                          const Icon(Icons.account_balance_wallet, 
                                   color: Colors.green, size: 20),
                          const SizedBox(width: 8),
                          const Text('Solde: '),
                          ReactiveUserBalance(
                            textStyle: const TextStyle(
                              fontWeight: FontWeight.bold,
                              color: Colors.green,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ],
                );
              },
            ),
          ],
        ),
      ),
    );
  }
  
  /// Section commandes avec widgets réactifs
  Widget _buildOrdersSection() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                const Icon(Icons.shopping_cart, color: Colors.orange),
                const SizedBox(width: 8),
                Text(
                  'Mes Commandes',
                  style: Theme.of(context).textTheme.titleLarge,
                ),
                const SizedBox(width: 8),
                const ReactiveOrdersCount(
                  prefix: '(',
                  suffix: ')',
                  textStyle: TextStyle(color: Colors.grey),
                ),
                const Spacer(),
                IconButton(
                  onPressed: () => OrderService.refreshOrders(),
                  icon: const Icon(Icons.refresh, size: 20),
                  tooltip: 'Actualiser les commandes',
                ),
              ],
            ),
            
            const SizedBox(height: 16),
            
            // Indicateur de chargement des commandes
            const ReactiveOrdersLoadingIndicator(),
            
            const SizedBox(height: 8),
            
            // Liste des commandes réactive (limitée à 3 pour la démo)
            SizedBox(
              height: 200,
              child: ReactiveOrdersList(
                padding: EdgeInsets.zero,
                emptyWidget: const Center(
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Icon(Icons.shopping_cart_outlined, 
                           size: 48, color: Colors.grey),
                      SizedBox(height: 8),
                      Text('Aucune commande'),
                    ],
                  ),
                ),
                itemBuilder: (context, order) {
                  return Card(
                    margin: const EdgeInsets.only(bottom: 8),
                    child: ListTile(
                      dense: true,
                      title: Text('Commande #${order['id'] ?? 'N/A'}'),
                      subtitle: Text('${order['montant'] ?? 'N/A'} FCFA'),
                      trailing: _buildOrderStatusChip(order),
                    ),
                  );
                },
              ),
            ),
          ],
        ),
      ),
    );
  }
  
  /// Section statistiques réactives
  Widget _buildStatsSection() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                const Icon(Icons.analytics, color: Colors.purple),
                const SizedBox(width: 8),
                Text(
                  'Statistiques',
                  style: Theme.of(context).textTheme.titleLarge,
                ),
              ],
            ),
            
            const SizedBox(height: 16),
            
            // Dernière mise à jour
            const ReactiveLastUpdateWidget(),
            
            const SizedBox(height: 12),
            
            // Statistiques diverses
            StreamBuilder<bool>(
              stream: AppStateService.instance.isConnectedStream,
              builder: (context, snapshot) {
                final isConnected = snapshot.data ?? true;
                return Row(
                  children: [
                    Icon(
                      isConnected ? Icons.wifi : Icons.wifi_off,
                      color: isConnected ? Colors.green : Colors.red,
                      size: 16,
                    ),
                    const SizedBox(width: 8),
                    Text(
                      isConnected ? 'En ligne' : 'Hors ligne',
                      style: TextStyle(
                        color: isConnected ? Colors.green : Colors.red,
                      ),
                    ),
                  ],
                );
              },
            ),
          ],
        ),
      ),
    );
  }
  
  /// Section actions de test
  Widget _buildTestActionsSection() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Actions de Test',
              style: Theme.of(context).textTheme.titleLarge,
            ),
            
            const SizedBox(height: 16),
            
            Wrap(
              spacing: 8,
              runSpacing: 8,
              children: [
                ElevatedButton.icon(
                  onPressed: () => AuthService.refreshUserData(),
                  icon: const Icon(Icons.person_outline, size: 16),
                  label: const Text('Rafraîchir Profil'),
                ),
                
                ElevatedButton.icon(
                  onPressed: () => OrderService.refreshOrders(),
                  icon: const Icon(Icons.shopping_cart_outlined, size: 16),
                  label: const Text('Rafraîchir Commandes'),
                ),
                
                ElevatedButton.icon(
                  onPressed: () => refreshAllData(),
                  icon: const Icon(Icons.refresh, size: 16),
                  label: const Text('Tout Rafraîchir'),
                ),
                
                ElevatedButton.icon(
                  onPressed: () => showError('Test d\'erreur réactive'),
                  icon: const Icon(Icons.error_outline, size: 16),
                  label: const Text('Test Erreur'),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.red,
                    foregroundColor: Colors.white,
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }
  
  Widget _buildOrderStatusChip(Map<String, dynamic> order) {
    final reste = double.tryParse(order['reste']?.toString() ?? '0') ?? 0;
    final montant = double.tryParse(order['montant']?.toString() ?? '0') ?? 0;
    
    Color chipColor;
    String statusText;
    
    if (reste <= 0) {
      chipColor = Colors.green;
      statusText = 'Terminée';
    } else if (reste == montant) {
      chipColor = Colors.orange;
      statusText = 'En attente';
    } else {
      chipColor = Colors.blue;
      statusText = 'En cours';
    }
    
    return Chip(
      label: Text(
        statusText,
        style: const TextStyle(
          color: Colors.white,
          fontSize: 10,
        ),
      ),
      backgroundColor: chipColor,
      materialTapTargetSize: MaterialTapTargetSize.shrinkWrap,
    );
  }
}
