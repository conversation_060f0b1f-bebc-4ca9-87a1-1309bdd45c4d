import 'package:flutter/material.dart';
import 'package:callitris/utils/appTheme.dart';
import 'package:callitris/services/carnet_service.dart';
import 'package:callitris/services/auth_service.dart';

class CarnetDetailScreen extends StatefulWidget {
  final String carnetId;
  final String carnetName;
  final Map<String, dynamic> carnetData;
  final List<Map<String, dynamic>>? preloadedElements;

  const CarnetDetailScreen({
    super.key,
    required this.carnetId,
    required this.carnetName,
    required this.carnetData,
    this.preloadedElements,
  });

  @override
  State<CarnetDetailScreen> createState() => _CarnetDetailScreenState();
}

class _CarnetDetailScreenState extends State<CarnetDetailScreen> {
  bool _isLoading = true;
  Map<String, dynamic> _carnetDetails = {};
  List<Map<String, dynamic>> _carnetElements = [];
  bool _isElementsLoading = false;
  bool _hasError = false;
  bool _hasElementsError = false;
  String _errorMessage = '';
  String _elementsErrorMessage = '';
  bool _isSubscribed = false;

  @override
  void initState() {
    super.initState();
    _loadCarnetDetails();
    _loadCarnetElements();
  }

  Future<void> _loadCarnetDetails() async {
    setState(() {
      _isLoading = true;
      _hasError = false;
    });

    try {
      // Vérifier si l'utilisateur est connecté
      final isLoggedIn = await AuthService.isLoggedIn();

      if (isLoggedIn) {
        // Récupérer les détails du carnet
        final result = await CarnetService.getCarnetDetails(widget.carnetId);

        if (result['success']) {
          setState(() {
            _carnetDetails = result['data'];
            _isLoading = false;

            // Vérifier si l'utilisateur est déjà abonné à ce carnet
            _isSubscribed = _carnetDetails['is_subscribed'] == true;
          });
        } else {
          setState(() {
            _isLoading = false;
            _hasError = true;
            _errorMessage = result['message'];
          });
        }
      } else {
        setState(() {
          _isLoading = false;
          _hasError = true;
          _errorMessage =
              'Vous devez être connecté pour voir les détails du carnet';
        });
      }
    } catch (e) {
      setState(() {
        _isLoading = false;
        _hasError = true;
        _errorMessage = 'Erreur lors du chargement des détails: $e';
      });
    }
  }

  Future<void> _loadCarnetElements() async {
    setState(() {
      _isElementsLoading = true;
      _hasElementsError = false;
    });

    try {
      // Récupérer les éléments du carnet
      final result = await CarnetService.getCarnetElements(widget.carnetId);

      if (result['success']) {
        final data = result['data'];

        // Traiter la structure spécifique de la réponse
        if (data is List && data.isNotEmpty && data[0] is List) {
          // Structure observée: [ [ {item1}, {item2}, ... ] ]
          setState(() {
            _carnetElements = List<Map<String, dynamic>>.from(data[0]);
            _isElementsLoading = false;
          });
        } else if (data is List) {
          // Structure alternative: [ {item1}, {item2}, ... ]
          setState(() {
            _carnetElements = List<Map<String, dynamic>>.from(data);
            _isElementsLoading = false;
          });
        } else if (data is Map && data.containsKey('id_kit')) {
          // Structure pour un seul élément: { id_kit: ..., ... }
          setState(() {
            _carnetElements = [Map<String, dynamic>.from(data)];
            _isElementsLoading = false;
          });
        } else {
          setState(() {
            _isElementsLoading = false;
            _hasElementsError = true;
            _elementsErrorMessage = 'Format de données non reconnu';
          });
        }
      } else {
        setState(() {
          _isElementsLoading = false;
          _hasElementsError = true;
          _elementsErrorMessage = result['message'];
        });
      }
    } catch (e) {
      setState(() {
        _isElementsLoading = false;
        _hasElementsError = true;
        _elementsErrorMessage = 'Erreur lors du chargement des éléments: $e';
      });
    }
  }

  // Ajouter cette méthode pour construire la liste des éléments du carnet
  Widget _buildCarnetElementsList() {
    if (_isElementsLoading) {
      return const Center(
        child: Padding(
          padding: EdgeInsets.all(32.0),
          child: CircularProgressIndicator(),
        ),
      );
    }

    if (_hasElementsError) {
      return Center(
        child: Padding(
          padding: const EdgeInsets.all(32.0),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(Icons.error_outline, size: 48, color: Colors.red[300]),
              const SizedBox(height: 16),
              Text(
                'Erreur de chargement des éléments',
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                  color: AppTheme.color.textColor,
                ),
              ),
              const SizedBox(height: 8),
              Text(
                _elementsErrorMessage,
                style: TextStyle(fontSize: 14, color: AppTheme.color.brunGris),
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 16),
              ElevatedButton(
                onPressed: _loadCarnetElements,
                style: ElevatedButton.styleFrom(
                  backgroundColor: AppTheme.color.primaryColor,
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(12),
                  ),
                ),
                child: const Text('Réessayer'),
              ),
            ],
          ),
        ),
      );
    }

    if (_carnetElements.isEmpty) {
      return Center(
        child: Padding(
          padding: const EdgeInsets.all(32.0),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(
                Icons.inventory_2_outlined,
                size: 48,
                color: Colors.grey[400],
              ),
              const SizedBox(height: 16),
              Text(
                'Aucun élément disponible',
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                  color: AppTheme.color.textColor,
                ),
              ),
              const SizedBox(height: 8),
              Text(
                'Ce carnet ne contient pas encore d\'éléments',
                style: TextStyle(fontSize: 14, color: AppTheme.color.brunGris),
                textAlign: TextAlign.center,
              ),
            ],
          ),
        ),
      );
    }

    // Utiliser un affichage en grille pour les écrans plus larges
    // et une liste simple pour les écrans plus étroits
    return LayoutBuilder(
      builder: (context, constraints) {
        // Déterminer si nous devons utiliser une grille ou une liste
        final useGrid = constraints.maxWidth > 600;

        if (useGrid) {
          return GridView.builder(
            shrinkWrap: true,
            physics: const NeverScrollableScrollPhysics(),
            gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
              crossAxisCount: 2,
              childAspectRatio: 0.75,
              crossAxisSpacing: 16,
              mainAxisSpacing: 16,
            ),
            itemCount: _carnetElements.length,
            itemBuilder: (context, index) {
              final element = _carnetElements[index];
              return _buildCarnetElementCard(element);
            },
          );
        } else {
          return ListView.builder(
            shrinkWrap: true,
            physics: const NeverScrollableScrollPhysics(),
            itemCount: _carnetElements.length,
            itemBuilder: (context, index) {
              final element = _carnetElements[index];
              return _buildCarnetElementCard(element);
            },
          );
        }
      },
    );
  }

  // Ajouter cette méthode pour construire une carte d'élément de carnet
  Widget _buildCarnetElementCard(Map<String, dynamic> element) {
    // Extraire les valeurs avec des fallbacks
    final String id = element['id_kit']?.toString() ?? 'ID inconnu';
    final String name = element['option_kit'] ?? 'Pack sans nom';
    final String description =
        element['description_kit'] ?? 'Aucune description disponible';

    // Traiter l'URL de l'image
    String imageUrl = '';
    if (element['photo_kit'] != null &&
        element['photo_kit'].toString().isNotEmpty) {
      // Construire l'URL complète
      final String photoPath = element['photo_kit'].toString();
      if (photoPath.startsWith('http')) {
        imageUrl = photoPath;
      } else {
        // Utiliser la nouvelle URL de base
        imageUrl =
            'https://app.callitris-distribution.com/app/${photoPath.replaceAll('../../', '')}';
      }
    }

    // Extraire les informations de prix
    final String dailyPrice = element['cout_journalier_kit']?.toString() ?? '0';
    final String totalPrice = element['montant_total_kit']?.toString() ?? '0';

    // Extraire la durée
    final String duration = element['durée_id']?.toString() ?? '0';

    // Extraire la disponibilité
    final int stock =
        int.tryParse(element['kit_de_production_total']?.toString() ?? '0') ??
        0;
    final bool isInStock = stock > 0;

    return Container(
      margin: const EdgeInsets.only(bottom: 16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 8,
            offset: const Offset(0, 3),
          ),
        ],
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          onTap: () {
            // Action lors du tap sur un élément
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(content: Text('Élément sélectionné: $name')),
            );
          },
          borderRadius: BorderRadius.circular(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Image de l'élément
              ClipRRect(
                borderRadius: const BorderRadius.only(
                  topLeft: Radius.circular(16),
                  topRight: Radius.circular(16),
                ),
                child: Container(
                  height: 160,
                  width: double.infinity,
                  decoration: BoxDecoration(
                    color: AppTheme.color.primaryColor.withOpacity(0.1),
                  ),
                  child:
                      imageUrl.isNotEmpty
                          ? Image.network(
                            imageUrl,
                            fit: BoxFit.cover,
                            errorBuilder: (context, error, stackTrace) {
                              return Center(
                                child: Icon(
                                  Icons.image_not_supported_outlined,
                                  color: AppTheme.color.primaryColor,
                                  size: 40,
                                ),
                              );
                            },
                            loadingBuilder: (context, child, loadingProgress) {
                              if (loadingProgress == null) return child;
                              return Center(
                                child: CircularProgressIndicator(
                                  value:
                                      loadingProgress.expectedTotalBytes != null
                                          ? loadingProgress
                                                  .cumulativeBytesLoaded /
                                              loadingProgress
                                                  .expectedTotalBytes!
                                          : null,
                                ),
                              );
                            },
                          )
                          : Center(
                            child: Icon(
                              Icons.inventory_2,
                              color: AppTheme.color.primaryColor,
                              size: 40,
                            ),
                          ),
                ),
              ),

              // Informations sur l'élément
              Padding(
                padding: const EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // Badge de disponibilité
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Container(
                          padding: const EdgeInsets.symmetric(
                            horizontal: 10,
                            vertical: 5,
                          ),
                          decoration: BoxDecoration(
                            color:
                                isInStock
                                    ? Colors.green.withOpacity(0.1)
                                    : Colors.red.withOpacity(0.1),
                            borderRadius: BorderRadius.circular(12),
                          ),
                          child: Row(
                            mainAxisSize: MainAxisSize.min,
                            children: [
                              Icon(
                                isInStock ? Icons.check_circle : Icons.cancel,
                                color: isInStock ? Colors.green : Colors.red,
                                size: 14,
                              ),
                              const SizedBox(width: 4),
                              Text(
                                isInStock ? 'Disponible' : 'Rupture de stock',
                                style: TextStyle(
                                  fontSize: 12,
                                  fontWeight: FontWeight.bold,
                                  color: isInStock ? Colors.green : Colors.red,
                                ),
                              ),
                            ],
                          ),
                        ),

                        // Quantité en stock
                        Text(
                          'Stock: $stock',
                          style: TextStyle(
                            fontSize: 12,
                            color: AppTheme.color.brunGris,
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 12),

                    // Nom du pack
                    Text(
                      name,
                      style: TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                        color: AppTheme.color.textColor,
                      ),
                      maxLines: 2,
                      overflow: TextOverflow.ellipsis,
                    ),
                    const SizedBox(height: 8),

                    // Description
                    Text(
                      description,
                      style: TextStyle(
                        fontSize: 14,
                        color: AppTheme.color.brunGris,
                      ),
                      maxLines: 2,
                      overflow: TextOverflow.ellipsis,
                    ),
                    const SizedBox(height: 16),

                    // Informations de prix et durée
                    Row(
                      children: [
                        // Prix total
                        Expanded(
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(
                                'Prix total',
                                style: TextStyle(
                                  fontSize: 12,
                                  color: AppTheme.color.brunGris,
                                ),
                              ),
                              const SizedBox(height: 4),
                              Text(
                                '$totalPrice FCFA',
                                style: TextStyle(
                                  fontSize: 16,
                                  fontWeight: FontWeight.bold,
                                  color: AppTheme.color.primaryColor,
                                ),
                              ),
                            ],
                          ),
                        ),

                        // Prix journalier
                        Expanded(
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(
                                'Versement journalier',
                                style: TextStyle(
                                  fontSize: 12,
                                  color: AppTheme.color.brunGris,
                                ),
                              ),
                              const SizedBox(height: 4),
                              Text(
                                '$dailyPrice FCFA/jour',
                                style: TextStyle(
                                  fontSize: 16,
                                  fontWeight: FontWeight.bold,
                                  color: AppTheme.color.textColor,
                                ),
                              ),
                            ],
                          ),
                        ),

                        // Durée
                        Expanded(
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(
                                'Durée',
                                style: TextStyle(
                                  fontSize: 12,
                                  color: AppTheme.color.brunGris,
                                ),
                              ),
                              const SizedBox(height: 4),
                              Text(
                                '$duration jours',
                                style: TextStyle(
                                  fontSize: 16,
                                  fontWeight: FontWeight.bold,
                                  color: AppTheme.color.textColor,
                                ),
                              ),
                            ],
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 16),

                    // Bouton d'action
                    SizedBox(
                      width: double.infinity,
                      height: 44,
                      child: ElevatedButton(
                        onPressed:
                            isInStock
                                ? () {
                                  // Action pour ajouter au panier ou voir les détails
                                  ScaffoldMessenger.of(context).showSnackBar(
                                    SnackBar(
                                      content: Text('$name ajouté au panier'),
                                    ),
                                  );
                                }
                                : null,
                        style: ElevatedButton.styleFrom(
                          backgroundColor: AppTheme.color.primaryColor,
                          disabledBackgroundColor: Colors.grey.shade300,
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(12),
                          ),
                        ),
                        child: Text(
                          'Ajouter au panier',
                          style: const TextStyle(
                            fontSize: 14,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFFF8F9FA),
      appBar: AppBar(
        elevation: 0,
        backgroundColor: Colors.white,
        title: Text(
          widget.carnetName,
          style: TextStyle(
            fontSize: 20,
            fontWeight: FontWeight.w700,
            color: AppTheme.color.textColor,
            letterSpacing: -0.5,
          ),
        ),
        actions: [
          IconButton(
            icon: const Icon(Icons.share_outlined),
            onPressed: () {
              // Partager le carnet
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(
                  content: Text('Fonctionnalité de partage à venir'),
                ),
              );
            },
          ),
        ],
      ),
      body:
          _isLoading
              ? const Center(child: CircularProgressIndicator())
              : _hasError
              ? _buildErrorState()
              : _buildCarnetDetailsContent(),
      bottomNavigationBar: _isLoading || _hasError ? null : _buildBottomBar(),
    );
  }

  Widget _buildErrorState() {
    return Center(
      child: SingleChildScrollView(
        padding: const EdgeInsets.all(24),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.error_outline, size: 80, color: Colors.red[300]),
            const SizedBox(height: 24),
            Text(
              'Erreur de chargement',
              style: TextStyle(
                fontSize: 20,
                fontWeight: FontWeight.bold,
                color: AppTheme.color.textColor,
              ),
            ),
            const SizedBox(height: 16),
            Text(
              _errorMessage,
              style: TextStyle(fontSize: 16, color: AppTheme.color.brunGris),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 32),
            SizedBox(
              width: 200,
              height: 50,
              child: ElevatedButton(
                onPressed: _loadCarnetDetails,
                style: ElevatedButton.styleFrom(
                  backgroundColor: AppTheme.color.primaryColor,
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(12),
                  ),
                ),
                child: const Text(
                  'Réessayer',
                  style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildCarnetDetailsContent() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Informations générales du carnet
          const SizedBox(height: 24),

          // Titre de la section des éléments
          Row(
            children: [
              Container(
                padding: const EdgeInsets.all(10),
                decoration: BoxDecoration(
                  color: AppTheme.color.primaryColor.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Icon(
                  Icons.inventory_2,
                  color: AppTheme.color.primaryColor,
                  size: 24,
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Éléments du carnet',
                      style: TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                        color: AppTheme.color.textColor,
                      ),
                    ),
                    const SizedBox(height: 4),
                    Text(
                      'Découvrez les produits disponibles dans ce carnet',
                      style: TextStyle(
                        fontSize: 14,
                        color: AppTheme.color.brunGris,
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),

          // Liste des éléments du carnet
          _buildCarnetElementsList(),
        ],
      ),
    );
  }

  Widget _buildCarnetBanner() {
    return Container(
      height: 180,
      decoration: BoxDecoration(
        color: AppTheme.color.primaryColor.withOpacity(0.1),
        borderRadius: BorderRadius.circular(16),
        image: DecorationImage(
          image: const AssetImage('assets/images/carnet_banner.jpg'),
          fit: BoxFit.cover,
          colorFilter: ColorFilter.mode(
            Colors.black.withOpacity(0.3),
            BlendMode.darken,
          ),
        ),
      ),
      child: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.book, size: 60, color: Colors.white),
            const SizedBox(height: 16),
            Text(
              widget.carnetName,
              style: const TextStyle(
                fontSize: 24,
                fontWeight: FontWeight.bold,
                color: Colors.white,
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildCarnetInfo() {
    return Card(
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
      elevation: 2,
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'À propos de ce carnet',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
                color: AppTheme.color.textColor,
              ),
            ),
            const SizedBox(height: 16),
            _buildInfoRow(
              Icons.info_outline,
              'Type',
              widget.carnetData['type'] ?? 'Standard',
            ),
            const Divider(height: 24),
            _buildInfoRow(Icons.calendar_today, 'Disponibilité', 'Immédiate'),
            const Divider(height: 24),
            _buildInfoRow(
              Icons.description_outlined,
              'Description',
              widget.carnetData['description'] ??
                  'Ce carnet vous permet de suivre et gérer vos activités quotidiennes de manière efficace.',
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildInfoRow(IconData icon, String title, String value) {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Icon(icon, size: 20, color: AppTheme.color.brunGris),
        const SizedBox(width: 12),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                title,
                style: TextStyle(fontSize: 14, color: AppTheme.color.brunGris),
              ),
              const SizedBox(height: 4),
              Text(
                value,
                style: TextStyle(fontSize: 16, color: AppTheme.color.textColor),
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildCarnetFeatures() {
    final features = [
      {'icon': Icons.check_circle_outline, 'text': 'Suivi personnalisé'},
      {'icon': Icons.access_time, 'text': 'Accès 24h/24'},
      {'icon': Icons.sync, 'text': 'Synchronisation automatique'},
      {'icon': Icons.security, 'text': 'Données sécurisées'},
    ];

    return Card(
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
      elevation: 2,
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Caractéristiques',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
                color: AppTheme.color.textColor,
              ),
            ),
            const SizedBox(height: 16),
            ...features.map(
              (feature) => Padding(
                padding: const EdgeInsets.only(bottom: 12),
                child: Row(
                  children: [
                    Icon(
                      feature['icon'] as IconData,
                      size: 20,
                      color: AppTheme.color.primaryColor,
                    ),
                    const SizedBox(width: 12),
                    Text(
                      feature['text'] as String,
                      style: TextStyle(
                        fontSize: 16,
                        color: AppTheme.color.textColor,
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildDurationOptions() {
    final durations = [
      {'months': 1, 'price': '5.000', 'color': Colors.blue},
      {'months': 3, 'price': '12.000', 'color': Colors.orange},
      {'months': 6, 'price': '20.000', 'color': Colors.green},
      {'months': 12, 'price': '35.000', 'color': Colors.purple},
    ];

    return Card(
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
      elevation: 2,
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Options de durée',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
                color: AppTheme.color.textColor,
              ),
            ),
            const SizedBox(height: 16),
            ...durations.map(
              (option) => Padding(
                padding: const EdgeInsets.only(bottom: 12),
                child: Container(
                  decoration: BoxDecoration(
                    border: Border.all(
                      color: (option['color'] as Color).withOpacity(0.3),
                      width: 1,
                    ),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: ListTile(
                    leading: CircleAvatar(
                      backgroundColor: (option['color'] as Color).withOpacity(
                        0.2,
                      ),
                      child: Text(
                        '${option['months']}m',
                        style: TextStyle(
                          color: option['color'] as Color,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ),
                    title: Text(
                      '${option['months']} mois',
                      style: const TextStyle(fontWeight: FontWeight.bold),
                    ),
                    trailing: Text(
                      '${option['price']} FCFA',
                      style: TextStyle(
                        fontWeight: FontWeight.bold,
                        color: option['color'] as Color,
                      ),
                    ),
                    onTap: () {
                      _showSubscriptionConfirmation(option);
                    },
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  void _showSubscriptionConfirmation(Map<String, dynamic> option) {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (BuildContext context) {
        return AlertDialog(
          title: Text(
            'Confirmer l\'abonnement',
            style: TextStyle(
              color: AppTheme.color.primaryColor,
              fontWeight: FontWeight.bold,
            ),
          ),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                'Vous êtes sur le point de vous abonner au carnet "${widget.carnetName}" pour une durée de ${option['months']} mois.',
                style: TextStyle(fontSize: 16, color: AppTheme.color.textColor),
              ),
              const SizedBox(height: 16),
              Text(
                'Prix: ${option['price']} FCFA',
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                  color: option['color'] as Color,
                ),
              ),
            ],
          ),
          actions: [
            TextButton(
              onPressed: () {
                Navigator.pop(context);
              },
              child: Text(
                'Annuler',
                style: TextStyle(color: AppTheme.color.brunGris),
              ),
            ),
            ElevatedButton(
              onPressed: () {
                Navigator.pop(context);
                _processSubscription(option);
              },
              style: ElevatedButton.styleFrom(
                backgroundColor: AppTheme.color.primaryColor,
              ),
              child: const Text('Confirmer'),
            ),
          ],
        );
      },
    );
  }

  void _processSubscription(Map<String, dynamic> option) async {
    // Afficher un dialogue de chargement
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (BuildContext context) {
        return const Center(child: CircularProgressIndicator());
      },
    );

    try {
      // Vérifier si l'utilisateur est connecté
      final isLoggedIn = await AuthService.isLoggedIn();

      if (isLoggedIn) {
        // Récupérer les données de l'utilisateur
        final userData = await AuthService.getUserData();

        if (userData != null && userData.containsKey('id_client')) {
          // Souscrire au carnet
          final result = await CarnetService.subscribeToCarnet(
            widget.carnetId,
            userData['id_client'].toString(),
          );

          // Fermer le dialogue de chargement
          Navigator.pop(context);

          if (result['success']) {
            setState(() {
              _isSubscribed = true;
            });

            // Afficher un message de succès
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Text(
                  'Abonnement réussi pour ${option['months']} mois',
                ),
                backgroundColor: Colors.green,
              ),
            );
          } else {
            // Afficher un message d'erreur
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Text('Erreur: ${result['message']}'),
                backgroundColor: Colors.red,
              ),
            );
          }
        } else {
          // Fermer le dialogue de chargement
          Navigator.pop(context);
          _showLoginRequiredDialog();
        }
      } else {
        // Fermer le dialogue de chargement
        Navigator.pop(context);
        _showLoginRequiredDialog();
      }
    } catch (e) {
      // Fermer le dialogue de chargement
      Navigator.pop(context);

      // Afficher un message d'erreur
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('Erreur: $e'), backgroundColor: Colors.red),
      );
    }
  }

  Widget _buildBottomBar() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 10,
            offset: const Offset(0, -5),
          ),
        ],
      ),
      child: SafeArea(
        child: SizedBox(
          width: double.infinity,
          height: 50,
          child: ElevatedButton.icon(
            onPressed:
                _isSubscribed
                    ? () {
                      // Naviguer vers la page du carnet souscrit
                      ScaffoldMessenger.of(context).showSnackBar(
                        const SnackBar(
                          content: Text(
                            'Accès au carnet en cours de développement',
                          ),
                        ),
                      );
                    }
                    : () {
                      _showSubscriptionConfirmation({
                        'months': 1,
                        'price': '5.000',
                        'color': Colors.blue,
                      });
                    },
            icon: Icon(
              _isSubscribed ? Icons.check_circle : Icons.add_circle_outline,
            ),
            label: Text(
              _isSubscribed ? 'Accéder au carnet' : 'S\'abonner maintenant',
              style: const TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
            ),
            style: ElevatedButton.styleFrom(
              backgroundColor:
                  _isSubscribed ? Colors.green : AppTheme.color.primaryColor,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(12),
              ),
            ),
          ),
        ),
      ),
    );
  }

  void _showLoginRequiredDialog() {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (BuildContext context) {
        return AlertDialog(
          title: Text(
            'Connexion requise',
            style: TextStyle(
              color: AppTheme.color.primaryColor,
              fontWeight: FontWeight.bold,
            ),
          ),
          content: const Text(
            'Vous devez être connecté pour souscrire à un carnet.',
          ),
          actions: [
            TextButton(
              onPressed: () {
                Navigator.pop(context);
              },
              child: const Text('Annuler'),
            ),
            ElevatedButton(
              onPressed: () {
                Navigator.pop(context);
                // Naviguer vers l'écran de connexion
                // Navigator.pushNamed(context, '/login');
              },
              style: ElevatedButton.styleFrom(
                backgroundColor: AppTheme.color.primaryColor,
              ),
              child: const Text('Se connecter'),
            ),
          ],
        );
      },
    );
  }
}
