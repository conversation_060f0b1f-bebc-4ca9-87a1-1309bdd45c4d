# 🚀 Checklist de déploiement Wave Deep Links - Callitris

## 📋 Résumé de la configuration

Votre application Callitris est maintenant configurée pour gérer les deep links après les paiements Wave avec votre domaine `https://dev-mani.io/teams/client-api.callitris-distribution.com`.

## ✅ Ce qui a été configuré

### 📱 Application Mobile
- [x] **Android** : Intent-filters dans `AndroidManifest.xml`
- [x] **iOS** : URL schemes dans `Info.plist`
- [x] **Service Deep Links** : `lib/services/deep_link_service.dart`
- [x] **Gestionnaire de retour** : `lib/screens/payment/payment_return_handler.dart`
- [x] **Configuration API** : URLs Wave dans `lib/config/api_config.dart`
- [x] **Package** : `app_links` ajouté au `pubspec.yaml`

### 🌐 Backend
- [x] **Gestionnaire PHP** : `server_config/wave_callback_handler.php`
- [x] **Configuration Apache** : `server_config/apache_wave_config.conf`
- [x] **Configuration Nginx** : `server_config/nginx_wave_config.conf`
- [x] **Script SQL** : `server_config/wave_database_setup.sql`

## 🔧 Étapes de déploiement

### 1. Déploiement Backend (OBLIGATOIRE)

```bash
# 1. Copiez les fichiers sur votre serveur
scp server_config/wave_callback_handler.php <EMAIL>:/path/to/teams/client-api.callitris-distribution.com/callback/wave/
scp server_config/wave_payment_handler.php <EMAIL>:/path/to/teams/client-api.callitris-distribution.com/

# 2. Créez le dossier logs
ssh <EMAIL> "mkdir -p /path/to/teams/client-api.callitris-distribution.com/callback/wave/logs"
ssh <EMAIL> "chmod 755 /path/to/teams/client-api.callitris-distribution.com/callback/wave/logs"

# 3. Configurez votre serveur web (Apache ou Nginx)
# Voir les fichiers de configuration fournis
```

### 2. Configuration Base de Données

```sql
-- Exécutez le script SQL fourni
mysql -u votre_user -p callitris_db < server_config/wave_database_setup.sql
```

### 3. Configuration Wave

Dans votre tableau de bord Wave, configurez ces URLs :

```
Return URL:  https://dev-mani.io/teams/client-api.callitris-distribution.com/callback/wave/return
Success URL: https://dev-mani.io/teams/client-api.callitris-distribution.com/callback/wave/success
Failure URL: https://dev-mani.io/teams/client-api.callitris-distribution.com/callback/wave/failure
Cancel URL:  https://dev-mani.io/teams/client-api.callitris-distribution.com/callback/wave/cancel
Webhook URL: https://dev-mani.io/teams/client-api.callitris-distribution.com/callback/wave/notify
```

### 4. Personnalisation Backend

Modifiez `wave_callback_handler.php` avec vos credentials :

```php
// Configuration de base de données
define('DB_HOST', 'votre_host_db');
define('DB_NAME', 'votre_nom_db');
define('DB_USER', 'votre_utilisateur_db');
define('DB_PASS', 'votre_mot_de_passe_db');

// Configuration Wave
define('WAVE_API_KEY', 'votre_cle_api_wave');
define('WAVE_SECRET', 'votre_secret_wave');
define('WAVE_SANDBOX', false); // true pour les tests
```

## 🧪 Tests de validation

### 1. Test des callbacks backend

```bash
# Test callback de succès
curl -X POST "https://dev-mani.io/teams/client-api.callitris-distribution.com/callback/wave/success" \
  -H "Content-Type: application/json" \
  -d '{
    "transaction_id": "wave_test_123",
    "status": "success",
    "amount": "1000",
    "order_id": "456"
  }'

# Réponse attendue: Page HTML avec redirection vers callitris://payment/success?...
```

### 2. Test des deep links mobile

```bash
# Android (via ADB)
adb shell am start -W -a android.intent.action.VIEW \
  -d "callitris://payment/success?transaction_id=test123&amount=1000" \
  com.callitris.pay

# iOS (via Simulator)
xcrun simctl openurl booted "callitris://payment/success?transaction_id=test123&amount=1000"
```

### 3. Test complet du flux

1. **Lancez l'app** : `flutter run`
2. **Initiez un paiement** dans l'application
3. **Simulez un callback** avec curl
4. **Vérifiez** que l'app reçoit le deep link et affiche le bon dialog

## 📊 Monitoring

### Logs à surveiller

```bash
# Logs Wave backend
tail -f /path/to/teams/client-api.callitris-distribution.com/callback/wave/logs/wave_callbacks.log

# Logs serveur web
tail -f /var/log/nginx/callitris_wave_callbacks.log
tail -f /var/log/apache2/callitris_wave_callbacks.log

# Logs application mobile
flutter logs
```

### Requêtes SQL de monitoring

```sql
-- Transactions récentes
SELECT * FROM v_wave_transactions_details 
ORDER BY wave_created_at DESC LIMIT 10;

-- Statistiques du jour
SELECT * FROM v_wave_payment_stats 
WHERE payment_date = CURDATE();

-- Transactions en échec
SELECT * FROM wave_transactions 
WHERE status = 'failed' 
ORDER BY created_at DESC LIMIT 5;
```

## 🔒 Sécurité

### Recommandations

1. **HTTPS obligatoire** : Tous les callbacks doivent utiliser HTTPS
2. **Validation des signatures** : Vérifiez les signatures Wave si disponibles
3. **Rate limiting** : Limitez les tentatives de callback
4. **Logs sécurisés** : Ne loggez pas les données sensibles
5. **Permissions** : Dossier logs en 755, fichiers en 644

### Configuration de sécurité

```apache
# Dans .htaccess
# Bloquer les tentatives d'accès direct aux logs
<Files "*.log">
    Order Allow,Deny
    Deny from all
</Files>

# Rate limiting
<IfModule mod_evasive.c>
    DOSHashTableSize    2048
    DOSPageCount        10
    DOSPageInterval     1
    DOSSiteCount        50
    DOSSiteInterval     1
    DOSBlockingPeriod   600
</IfModule>
```

## 🆘 Dépannage

### Problèmes fréquents

1. **App ne s'ouvre pas avec deep link** :
   - Vérifiez que l'app est installée
   - Testez avec `adb shell am start`
   - Vérifiez AndroidManifest.xml et Info.plist

2. **Callback non reçu** :
   - Vérifiez les URLs dans Wave
   - Testez avec curl
   - Vérifiez les logs serveur

3. **Erreur de base de données** :
   - Vérifiez les credentials
   - Exécutez le script SQL
   - Vérifiez les permissions

4. **Deep link invalide** :
   - Vérifiez le format : `callitris://payment/success?params`
   - Testez la génération avec `test_wave_deep_links.dart`

### Commandes de debug

```bash
# Flutter
flutter clean && flutter pub get
flutter run --verbose

# Android
adb logcat | grep -i callitris
adb shell dumpsys package domain-preferred-apps

# Serveur
curl -I https://dev-mani.io/teams/client-api.callitris-distribution.com/callback/wave/success
```

## 📞 Support

Pour obtenir de l'aide :

1. **Consultez les logs** : `logs/wave_callbacks.log`
2. **Testez les endpoints** avec curl
3. **Vérifiez la configuration** serveur web
4. **Testez les deep links** manuellement

## 🎯 Flux complet de paiement

```
1. Utilisateur clique "Payer avec Wave" dans l'app
2. App appelle votre API pour initialiser le paiement
3. API retourne l'URL de paiement Wave
4. App redirige vers Wave
5. Utilisateur effectue le paiement dans Wave
6. Wave envoie callback à votre backend
7. Backend traite le callback et génère un deep link
8. Deep link ouvre l'app avec le résultat
9. App affiche le dialog de confirmation
```

Cette configuration est maintenant prête pour la production avec votre domaine `dev-mani.io` !
