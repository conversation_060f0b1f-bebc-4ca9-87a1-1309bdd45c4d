import 'package:flutter/material.dart';
import 'package:callitris/utils/appTheme.dart';
import 'package:package_info_plus/package_info_plus.dart';
import 'package:url_launcher/url_launcher.dart';

class AboutScreen extends StatefulWidget {
  const AboutScreen({super.key});

  @override
  State<AboutScreen> createState() => _AboutScreenState();
}

class _AboutScreenState extends State<AboutScreen> {
  String _appVersion = '';
  String _buildNumber = '';
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _loadAppInfo();
  }

  Future<void> _loadAppInfo() async {
    try {
      final packageInfo = await PackageInfo.fromPlatform();
      setState(() {
        _appVersion = packageInfo.version;
        _buildNumber = packageInfo.buildNumber;
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _appVersion = '1.0.0';
        _buildNumber = '1';
        _isLoading = false;
      });
    }
  }

  Future<void> _launchURL(String url) async {
    final Uri uri = Uri.parse(url);
    if (!await launchUrl(uri, mode: LaunchMode.externalApplication)) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Impossible d\'ouvrir $url'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  void _showLegalDocument(String title) {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder:
          (context) => AlertDialog(
            title: Text(title),
            content: SingleChildScrollView(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                mainAxisSize: MainAxisSize.min,
                children: [
                  Text(
                    'Article 1 : Objet de la cotisation'
                    'Callitris Distribution vient à travers ce produit vous proposer une collecte de fond en vue de vous permettre d\'anticiper sur vos différentes dépenses relatives aux grands évènements de l\'année, notamment la rentrée scolaire et les fêtes religieuses.\n'
                    'Il est strictement confidentiel.',
                    style: TextStyle(
                      fontSize: 14,
                      height: 1.5,
                      color: Colors.grey[800],
                    ),
                  ),
                  const SizedBox(height: 16),
                  Text(
                    'Article 2 : La souscription'
                    'Sachez de prime abord qu\'aucun remboursement n\'est pas possible lorsque vous vous inscrivez. Nous vous considérons sain de corps et d\'esprit lorsque vous vous engagez à souscrire à nos offres.\n\n'
                    'Vous êtes inscrit à l\'achat de votre carnet au prix de 500 FCFA. Le commercial vous communiquera les instructions relatives à votre inscription.\n\n'
                    'En cas de perte de votre carnet de cotisation, vous serez dans l\'obligation d\'en acheter un autre.',
                    style: TextStyle(
                      fontSize: 14,
                      height: 1.5,
                      color: Colors.grey[800],
                    ),
                  ),
                  const SizedBox(height: 16),
                  Text(
                    'Sed euismod, nisl vel ultricies lacinia, nisl nisl aliquam nisl, eget aliquam nisl nisl sit amet nisl. Sed euismod, nisl vel ultricies lacinia, nisl nisl aliquam nisl, eget aliquam nisl nisl sit amet nisl.',
                    style: TextStyle(
                      fontSize: 14,
                      height: 1.5,
                      color: Colors.grey[800],
                    ),
                  ),
                ],
              ),
            ),
            actions: [
              TextButton(
                onPressed: () {
                  Navigator.pop(context);
                },
                child: const Text('Fermer'),
              ),
            ],
          ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: const Text('À propos')),
      body:
          _isLoading
              ? const Center(child: CircularProgressIndicator())
              : SingleChildScrollView(
                padding: const EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.center,
                  children: [
                    _buildAppLogo(),
                    const SizedBox(height: 24),
                    _buildAppInfo(),
                    const SizedBox(height: 32),
                    _buildCompanyInfo(),
                    const SizedBox(height: 32),
                    _buildLegalSection(),
                    const SizedBox(height: 32),
                    _buildSocialLinks(),
                    const SizedBox(height: 24),
                    Text(
                      '© ${DateTime.now().year} Callitris. Tous droits réservés.',
                      style: TextStyle(color: Colors.grey[600], fontSize: 12),
                      textAlign: TextAlign.center,
                    ),
                    const SizedBox(height: 16),
                  ],
                ),
              ),
    );
  }

  Widget _buildAppLogo() {
    return Column(
      children: [
        Container(
          width: 100,
          height: 100,
          decoration: BoxDecoration(
            color: AppTheme.color.primaryColor.withOpacity(0.1),
            shape: BoxShape.circle,
          ),
          child: Center(
            child: Icon(
              Icons.shopping_basket,
              size: 60,
              color: AppTheme.color.primaryColor,
            ),
          ),
        ),
        const SizedBox(height: 16),
        const Text(
          'Callitris',
          style: TextStyle(fontSize: 24, fontWeight: FontWeight.bold),
        ),
        const SizedBox(height: 8),
        Text(
          'Version $_appVersion (Build $_buildNumber)',
          style: TextStyle(color: Colors.grey[600], fontSize: 14),
        ),
      ],
    );
  }

  Widget _buildAppInfo() {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'À propos de l\'application',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 16),
            const Text(
              'Callitris est une application de distribution qui permet aux commerçants de gérer leurs commandes, leurs stocks et leurs clients de manière efficace.',
              style: TextStyle(fontSize: 14, height: 1.5),
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                Expanded(
                  child: ElevatedButton(
                    onPressed: () {
                      _launchURL('https://callitris-distribution.com');
                    },
                    style: ElevatedButton.styleFrom(
                      backgroundColor: AppTheme.color.primaryColor,
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(8),
                      ),
                    ),
                    child: const Text('Visiter le site web'),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildCompanyInfo() {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'Notre entreprise',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 16),
            _buildInfoRow(
              icon: Icons.business,
              title: 'Callitris Distribution',
              subtitle: 'Entreprise de distribution',
            ),
            const SizedBox(height: 12),
            _buildInfoRow(
              icon: Icons.location_on,
              title: 'Adresse',
              subtitle: 'Abidjan, Côte d\'Ivoire',
            ),
            const SizedBox(height: 12),
            _buildInfoRow(
              icon: Icons.email,
              title: 'Email',
              subtitle: '<EMAIL>',
              onTap: () {
                _launchURL('mailto:<EMAIL>');
              },
            ),
            const SizedBox(height: 12),
            _buildInfoRow(
              icon: Icons.phone,
              title: 'Téléphone',
              subtitle: '+225 XX XX XX XX',
              onTap: () {
                _launchURL('tel:+225XXXXXXXX');
              },
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildLegalSection() {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'Informations légales',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 16),
            _buildLegalItem(
              title: 'Conditions d\'utilisation',
              onTap: () {
                _showLegalDocument('Conditions d\'utilisation');
              },
            ),
            const Divider(),
            _buildLegalItem(
              title: 'Politique de confidentialité',
              onTap: () {
                _showLegalDocument('Politique de confidentialité');
              },
            ),
            const Divider(),
            _buildLegalItem(
              title: 'Licences tierces',
              onTap: () {
                _showLegalDocument('Licences tierces');
              },
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSocialLinks() {
    return Row(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        _buildSocialButton(
          icon: Icons.facebook,
          onTap: () {
            _launchURL('https://facebook.com');
          },
        ),
        const SizedBox(width: 16),
        _buildSocialButton(
          icon: Icons.link,
          onTap: () {
            _launchURL('https://linkedin.com');
          },
        ),
        const SizedBox(width: 16),
        _buildSocialButton(
          icon: Icons.camera_alt,
          onTap: () {
            _launchURL('https://instagram.com');
          },
        ),
        const SizedBox(width: 16),
        _buildSocialButton(
          icon: Icons.language,
          onTap: () {
            _launchURL('https://callitris-distribution.com');
          },
        ),
      ],
    );
  }

  Widget _buildInfoRow({
    required IconData icon,
    required String title,
    required String subtitle,
    VoidCallback? onTap,
  }) {
    return InkWell(
      onTap: onTap,
      borderRadius: BorderRadius.circular(8),
      child: Padding(
        padding: const EdgeInsets.symmetric(vertical: 8, horizontal: 4),
        child: Row(
          children: [
            Container(
              padding: const EdgeInsets.all(8),
              decoration: BoxDecoration(
                color: AppTheme.color.primaryColor.withOpacity(0.1),
                shape: BoxShape.circle,
              ),
              child: Icon(icon, color: AppTheme.color.primaryColor, size: 20),
            ),
            const SizedBox(width: 16),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    title,
                    style: const TextStyle(
                      fontWeight: FontWeight.w600,
                      fontSize: 15,
                    ),
                  ),
                  const SizedBox(height: 2),
                  Text(
                    subtitle,
                    style: TextStyle(color: Colors.grey[600], fontSize: 13),
                  ),
                ],
              ),
            ),
            if (onTap != null)
              Icon(Icons.arrow_forward_ios, size: 14, color: Colors.grey[400]),
          ],
        ),
      ),
    );
  }

  Widget _buildLegalItem({required String title, required VoidCallback onTap}) {
    return ListTile(
      title: Text(
        title,
        style: const TextStyle(fontSize: 15, fontWeight: FontWeight.w500),
      ),
      trailing: const Icon(Icons.arrow_forward_ios, size: 16),
      contentPadding: EdgeInsets.zero,
      onTap: onTap,
    );
  }

  Widget _buildSocialButton({
    required IconData icon,
    required VoidCallback onTap,
  }) {
    return InkWell(
      onTap: onTap,
      borderRadius: BorderRadius.circular(30),
      child: Container(
        padding: const EdgeInsets.all(12),
        decoration: BoxDecoration(
          color: AppTheme.color.primaryColor.withOpacity(0.1),
          shape: BoxShape.circle,
        ),
        child: Icon(icon, color: AppTheme.color.primaryColor, size: 24),
      ),
    );
  }
}
