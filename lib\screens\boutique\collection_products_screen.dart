import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:callitris/utils/appTheme.dart';
import 'package:callitris/screens/boutique/product_detail_screen.dart';
import 'package:callitris/utils/navigation_service.dart';

class CollectionProductsScreen extends StatefulWidget {
  final String title;
  final String collectionId;
  final String collectionType;
  final int duration;
  
  const CollectionProductsScreen({
    super.key,
    required this.title,
    required this.collectionId,
    required this.collectionType,
    required this.duration,
  });

  @override
  State<CollectionProductsScreen> createState() => _CollectionProductsScreenState();
}

class _CollectionProductsScreenState extends State<CollectionProductsScreen> with SingleTickerProviderStateMixin {
  bool _isLoading = true;
  List<Map<String, dynamic>> _products = [];
  late ScrollController _scrollController;
  bool _isScrolled = false;
  
  // Animation
  late AnimationController _animationController;
  late Animation<double> _fadeAnimation;
  
  @override
  void initState() {
    super.initState();
    _scrollController = ScrollController();
    _scrollController.addListener(_onScroll);
    
    // Initialiser les animations
    _animationController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 800),
    );
    
    _fadeAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _animationController, curve: Curves.easeOut),
    );
    
    _loadProducts();
    
    // Démarrer l'animation après le chargement
    _animationController.forward();
  }
  
  @override
  void dispose() {
    _scrollController.removeListener(_onScroll);
    _scrollController.dispose();
    _animationController.dispose();
    super.dispose();
  }
  
  void _onScroll() {
    if (_scrollController.offset > 0 && !_isScrolled) {
      setState(() {
        _isScrolled = true;
      });
    } else if (_scrollController.offset <= 0 && _isScrolled) {
      setState(() {
        _isScrolled = false;
      });
    }
  }
  
  Future<void> _loadProducts() async {
    // Simuler un chargement depuis une API
    await Future.delayed(const Duration(milliseconds: 800));
    
    // Données fictives pour les packages (produits)
    final List<Map<String, dynamic>> allProducts = [
      {
        'id': '1',
        'name': 'Pack Smartphone Samsung Galaxy A54',
        'price': 250000,
        'dailyPrice': 12500,
        'duration': 20, // jours
        'imageUrl': 'assets/images/phone1.jpg',
        'category': 'Électronique',
        'badge': 'Populaire',
        'description': 'Smartphone Samsung Galaxy A54 avec 128Go de stockage et 6Go de RAM.',
        'collectionId': 'cat1', // ID de la collection à laquelle ce produit appartient
        'inStock': true, // Ajout de la propriété inStock
        'features': [
          'Écran Super AMOLED 6.4 pouces',
          'Processeur Exynos 1380',
          'RAM 6 Go, Stockage 128 Go',
          'Batterie 5000mAh',
          'Appareil photo principal 50MP',
          'Android 13',
        ],
      },
      {
        'id': '2',
        'name': 'Téléviseur LG 43 pouces Smart TV',
        'price': 180000,
        'dailyPrice': 9000,
        'duration': 20,
        'imageUrl': 'assets/images/tv1.jpg',
        'category': 'Électronique',
        'badge': null,
        'description': 'Téléviseur LG Smart TV 43 pouces avec résolution 4K.',
        'collectionId': 'cat1',
        'inStock': true,
        'features': [
          'Écran LED 43 pouces',
          'Résolution 4K Ultra HD',
          'Smart TV avec WebOS',
          'Connectivité HDMI et USB',
          'Son Dolby Digital',
        ],
      },
      {
        'id': '3',
        'name': 'Réfrigérateur Samsung 350L',
        'price': 320000,
        'dailyPrice': 16000,
        'duration': 20,
        'imageUrl': 'assets/images/fridge1.jpg',
        'category': 'Électroménager',
        'badge': 'Nouveau',
        'description': 'Réfrigérateur Samsung 350L avec congélateur.',
        'collectionId': 'cat2',
        'inStock': true,
        'features': [
          'Capacité 350 litres',
          'Technologie No Frost',
          'Compartiment congélateur',
          'Classe énergétique A+',
          'Distributeur d\'eau',
        ],
      },
      {
        'id': '4',
        'name': 'Machine à laver LG 8kg',
        'price': 280000,
        'dailyPrice': 14000,
        'duration': 20,
        'imageUrl': 'assets/images/washer1.jpg',
        'category': 'Électroménager',
        'badge': null,
        'description': 'Machine à laver LG 8kg avec plusieurs programmes.',
        'collectionId': 'cat2',
        'inStock': true,
        'features': [
          'Capacité 8 kg',
          'Vitesse d\'essorage 1400 tr/min',
          'Technologie 6 Motion Direct Drive',
          'Smart Diagnosis',
          'Programmes multiples',
        ],
      },
      {
        'id': '5',
        'name': 'Pack Noël Enfants',
        'price': 100000,
        'dailyPrice': 5000,
        'duration': 20,
        'imageUrl': 'assets/images/christmas1.jpg',
        'category': 'Fêtes',
        'badge': 'Populaire',
        'description': 'Pack cadeau pour Noël avec jouets et vêtements pour enfants.',
        'collectionId': 'car1',
        'inStock': true,
        'features': [
          'Jouets éducatifs',
          'Vêtements pour enfants',
          'Livres de contes',
          'Décorations de Noël',
          'Bonbons et chocolats',
        ],
      },
      {
        'id': '6',
        'name': 'Pack Nouvel An Adultes',
        'price': 150000,
        'dailyPrice': 7500,
        'duration': 20,
        'imageUrl': 'assets/images/newyear1.jpg',
        'category': 'Fêtes',
        'badge': 'Exclusif',
        'description': 'Pack cadeau pour le Nouvel An avec accessoires et vêtements pour adultes.',
        'collectionId': 'car1',
        'inStock': true,
        'features': [
          'Accessoires de fête',
          'Vêtements élégants',
          'Champagne et verres',
          'Décorations de Nouvel An',
          'Jeux de société',
        ],
      },
    ];
    
    // Filtrer les produits par collection
    final filteredProducts = allProducts.where((product) => 
      product['collectionId'] == widget.collectionId).toList();
    
    if (mounted) {
      setState(() {
        _products = filteredProducts;
        _isLoading = false;
      });
    }
  }
  
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.grey[50],
      body: _isLoading
          ? _buildLoadingState()
          : FadeTransition(
              opacity: _fadeAnimation,
              child: CustomScrollView(
                controller: _scrollController,
                slivers: [
                  _buildAppBar(),
                  _buildProductsList(),
                ],
              ),
            ),
    );
  }
  
  Widget _buildLoadingState() {
    return const Center(
      child: CircularProgressIndicator(),
    );
  }
  
  Widget _buildAppBar() {
    return SliverAppBar(
      expandedHeight: 140,
      floating: false,
      pinned: true,
      elevation: 0,
      backgroundColor: _isScrolled ? Colors.white : AppTheme.color.primaryColor,
      systemOverlayStyle: SystemUiOverlayStyle(
        statusBarColor: _isScrolled ? Colors.white : AppTheme.color.primaryColor,
        statusBarIconBrightness: _isScrolled ? Brightness.dark : Brightness.light,
      ),
      leading: IconButton(
        icon: Icon(
          Icons.arrow_back,
          color: _isScrolled ? AppTheme.color.textColor : Colors.white,
        ),
        onPressed: () => Navigator.pop(context),
      ),
      title: Text(
        widget.title,
        style: TextStyle(
          color: _isScrolled ? AppTheme.color.textColor : Colors.white,
          fontWeight: FontWeight.bold,
        ),
      ),
      flexibleSpace: FlexibleSpaceBar(
        background: Container(
          decoration: BoxDecoration(
            gradient: LinearGradient(
              begin: Alignment.topCenter,
              end: Alignment.bottomCenter,
              colors: [
                AppTheme.color.primaryColor,
                AppTheme.color.primaryColor.withOpacity(0.8),
              ],
            ),
          ),
          child: Padding(
            padding: const EdgeInsets.fromLTRB(20, 80, 20, 20),
            child: Row(
              children: [
                Container(
                  padding: const EdgeInsets.all(12),
                  decoration: BoxDecoration(
                    color: Colors.white.withOpacity(0.2),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Icon(
                    widget.collectionType == 'Catalogue' ? Icons.menu_book : Icons.book,
                    color: Colors.white,
                    size: 28,
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        widget.title,
                        style: const TextStyle(
                          color: Colors.white,
                          fontSize: 22,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      const SizedBox(height: 4),
                      Text(
                        '${widget.collectionType} • ${widget.duration} mois',
                        style: TextStyle(
                          color: Colors.white.withOpacity(0.9),
                          fontSize: 14,
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
  
  Widget _buildProductsList() {
    if (_products.isEmpty) {
      return SliverFillRemaining(
        child: Center(
          child: Column(
            mainAxisSize: MainAxisSize.min,  // Assure que la colonne prend le minimum d'espace nécessaire
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(
                Icons.inventory_2_outlined,
                size: 70,  // Réduit légèrement la taille de l'icône
                color: Colors.grey[400],
              ),
              const SizedBox(height: 12),  // Réduit l'espace
              Text(
                'Aucun produit disponible',
                style: TextStyle(
                  fontSize: 16,  // Réduit la taille de la police
                  fontWeight: FontWeight.bold,
                  color: AppTheme.color.textColor,
                ),
              ),
              const SizedBox(height: 6),  // Réduit l'espace
              Padding(
                padding: const EdgeInsets.symmetric(horizontal: 32.0),  // Augmente le padding horizontal
                child: Text(
                  'Ce ${widget.collectionType.toLowerCase()} ne contient pas encore de produits',
                  style: TextStyle(
                    color: AppTheme.color.brunGris,
                    fontSize: 13,  // Réduit la taille de la police
                  ),
                  textAlign: TextAlign.center,
                ),
              ),
            ],
          ),
        ),
      );
    }
    
    return SliverPadding(
      padding: const EdgeInsets.all(16),
      sliver: SliverGrid(
        gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
          crossAxisCount: 2,
          childAspectRatio: 0.6,  // Augmenté davantage pour donner plus d'espace vertical
          crossAxisSpacing: 16,
          mainAxisSpacing: 16,
        ),
        delegate: SliverChildBuilderDelegate(
          (context, index) {
            final product = _products[index];
            return _buildProductCard(product);
          },
          childCount: _products.length,
        ),
      ),
    );
  }
  
  Widget _buildProductCard(Map<String, dynamic> product) {
    return GestureDetector(
      onTap: () {
        // Naviguer vers la page de détail du produit
        routeSlideBottomAnimation(
          context,
          ProductDetailScreen(
            product: product,
          ),
        );
      },
      child: Container(
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(16),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withOpacity(0.05),
              blurRadius: 10,
              offset: const Offset(0, 4),
            ),
          ],
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Image du produit avec badge
            Stack(
              children: [
                ClipRRect(
                  borderRadius: const BorderRadius.vertical(top: Radius.circular(16)),
                  child: AspectRatio(
                    aspectRatio: 1,
                    child: Image.asset(
                      product['imageUrl'],
                      fit: BoxFit.cover,
                    ),
                  ),
                ),
                if (product['badge'] != null)
                  Positioned(
                    top: 8,
                    right: 8,
                    child: Container(
                      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                      decoration: BoxDecoration(
                        color: AppTheme.color.primaryColor,
                        borderRadius: BorderRadius.circular(8),
                      ),
                      child: Text(
                        product['badge'],
                        style: const TextStyle(
                          color: Colors.white,
                          fontSize: 10,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ),
                  ),
              ],
            ),
            
            // Informations du produit
            Expanded(  // Ajout de Expanded pour éviter le débordement
              child: Padding(
                padding: const EdgeInsets.all(12),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // Nom du produit
                    Text(
                      product['name'],
                      maxLines: 2,
                      overflow: TextOverflow.ellipsis,
                      style: TextStyle(
                        fontSize: 14,
                        fontWeight: FontWeight.bold,
                        color: AppTheme.color.textColor,
                      ),
                    ),
                    const SizedBox(height: 4),  // Réduit l'espace
                    
                    // Prix journalier
                    Row(
                      children: [
                        Text(
                          '${product['dailyPrice']} FCFA',
                          style: TextStyle(
                            fontSize: 14,  // Réduit la taille de police
                            fontWeight: FontWeight.bold,
                            color: AppTheme.color.primaryColor,
                          ),
                        ),
                        const Text(
                          '/jour',
                          style: TextStyle(
                            fontSize: 10,  // Réduit la taille de police
                            color: Colors.grey,
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 2),  // Réduit l'espace
                    
                    // Prix total
                    Text(
                      'Total: ${product['price']} FCFA',
                      style: TextStyle(
                        fontSize: 10,  // Réduit la taille de police
                        color: AppTheme.color.brunGris,
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
