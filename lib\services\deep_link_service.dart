import 'dart:async';
import 'package:callitris/services/order_service.dart';
import 'package:flutter/material.dart';
import 'package:app_links/app_links.dart';
import 'package:callitris/config/api_config.dart';
import 'package:callitris/services/auth_service.dart';

/// Service de gestion des deep links pour l'application Callitris
class DeepLinkService {
  static const String _tag = '[DeepLinkService]';
  static late AppLinks _appLinks;
  static StreamSubscription<Uri>? _linkSubscription;

  // Stream controller pour les deep links
  static final StreamController<String> _linkStreamController =
      StreamController<String>.broadcast();

  // Stream public pour écouter les deep links
  static Stream<String> get linkStream => _linkStreamController.stream;

  // Callback pour gérer les deep links
  static Function(String)? _onLinkReceived;

  /// Initialise le service de deep links
  static Future<void> initialize({Function(String)? onLinkReceived}) async {
    try {
      print('$_tag: Initialisation du service de deep links...');

      _onLinkReceived = onLinkReceived;
      _appLinks = AppLinks();

      // Écouter les deep links entrants
      _linkSubscription = _appLinks.uriLinkStream.listen(
        (Uri uri) {
          print('$_tag: Nouveau deep link reçu: ${uri.toString()}');
          handleDeepLink(uri.toString());
        },
        onError: (err) {
          print('$_tag: Erreur dans le stream de deep links: $err');
        },
      );

      // Vérifier s'il y a un deep link initial (app lancée via deep link)
      await _checkInitialLink();

      print('$_tag: Service de deep links initialisé avec succès');
    } catch (e) {
      print('$_tag: Erreur lors de l\'initialisation: $e');
    }
  }

  /// Vérifie s'il y a un deep link initial
  static Future<void> _checkInitialLink() async {
    try {
      final Uri? initialLink = await _appLinks.getInitialLink();
      if (initialLink != null) {
        print('$_tag: Deep link initial détecté: ${initialLink.toString()}');
        await handleDeepLink(initialLink.toString());
      }
    } catch (e) {
      print('$_tag: Erreur lors de la vérification du deep link initial: $e');
    }
  }

  /// Traite un deep link reçu
  static Future<void> handleDeepLink(String link) async {
    try {
      print('$_tag: Traitement du deep link: $link');

      final uri = Uri.tryParse(link);
      if (uri == null) {
        print('$_tag: URL invalide: $link');
        return;
      }

      // Ajouter le lien au stream
      _linkStreamController.add(link);

      // Appeler le callback si défini
      if (_onLinkReceived != null) {
        _onLinkReceived!(link);
      }

      // Traiter selon le type de deep link
      if (uri.scheme == ApiConfig.appScheme) {
        await _handleAppSchemeLink(uri);
      } else if (uri.scheme == 'https' && uri.host == 'dev-mani.io') {
        await _handleHttpsCallbackLink(uri);
      } else {
        print('$_tag: Schéma de deep link non supporté: ${uri.scheme}');
      }
    } catch (e) {
      print('$_tag: Erreur lors du traitement du deep link: $e');
    }
  }

  /// Gère les deep links avec le schéma de l'app (callitris://)
  static Future<void> _handleAppSchemeLink(Uri uri) async {
    try {
      print('$_tag: Traitement du deep link app: ${uri.toString()}');

      if (uri.host == 'payment') {
        await _handlePaymentDeepLink(uri);
      } else {
        print('$_tag: Host non reconnu pour le schéma app: ${uri.host}');
      }
    } catch (e) {
      print('$_tag: Erreur lors du traitement du deep link app: $e');
    }
  }

  /// Gère les deep links de paiement
  static Future<void> _handlePaymentDeepLink(Uri uri) async {
    try {
      print('$_tag: Traitement du deep link de paiement: ${uri.path}');

      // Extraire les paramètres de l'URL
      final Map<String, String> params = uri.queryParameters;
      final String path = uri.path;

      switch (path) {
        case '/return':
          await _handlePaymentReturn(params);
          break;
        case '/success':
          await _handlePaymentSuccess(params);
          break;
        case '/cancel':
          await _handlePaymentCancel(params);
          break;
        case '/failure':
          await _handlePaymentFailure(params);
          break;
        default:
          print('$_tag: Chemin de paiement non reconnu: $path');
      }
    } catch (e) {
      print('$_tag: Erreur lors du traitement du deep link de paiement: $e');
    }
  }

  /// Gère les callbacks HTTPS depuis le backend
  static Future<void> _handleHttpsCallbackLink(Uri uri) async {
    try {
      print('$_tag: Traitement du callback HTTPS: ${uri.toString()}');

      if (uri.pathSegments.contains('callback') &&
          uri.pathSegments.contains('wave')) {
        await _handleWaveCallback(uri);
      } else {
        print('$_tag: Callback HTTPS non reconnu: ${uri.path}');
      }
    } catch (e) {
      print('$_tag: Erreur lors du traitement du callback HTTPS: $e');
    }
  }

  /// Gère les callbacks Wave depuis le backend
  static Future<void> _handleWaveCallback(Uri uri) async {
    try {
      print('$_tag: Traitement du callback Wave: ${uri.path}');

      final Map<String, String> params = uri.queryParameters;
      final List<String> pathSegments = uri.pathSegments;

      if (pathSegments.contains('success')) {
        await _handlePaymentSuccess(params);
      } else if (pathSegments.contains('failure')) {
        await _handlePaymentFailure(params);
      } else if (pathSegments.contains('cancel')) {
        await _handlePaymentCancel(params);
      } else {
        print('$_tag: Type de callback Wave non reconnu');
      }
    } catch (e) {
      print('$_tag: Erreur lors du traitement du callback Wave: $e');
    }
  }

  /// Gère le retour de paiement (statut indéterminé)
  static Future<void> _handlePaymentReturn(Map<String, String> params) async {
    try {
      print('$_tag: Retour de paiement détecté');
      print('$_tag: Paramètres: $params');

      // Sauvegarder les informations de retour
      await OrderService.saveData('payment_return_params', params.toString());
      await OrderService.saveData(
        'payment_return_timestamp',
        DateTime.now().toIso8601String(),
      );

      // Ici vous pouvez ajouter la logique pour vérifier le statut du paiement
      // auprès de votre backend
    } catch (e) {
      print('$_tag: Erreur lors du traitement du retour de paiement: $e');
    }
  }

  /// Gère le succès du paiement
  static Future<void> _handlePaymentSuccess(Map<String, String> params) async {
    try {
      print('$_tag: Paiement réussi détecté');
      print('$_tag: Paramètres: $params');

      // Sauvegarder les informations de succès
      await OrderService.saveData('payment_status', 'success');
      await OrderService.saveData('payment_success_params', params.toString());
      await OrderService.saveData(
        'payment_success_timestamp',
        DateTime.now().toIso8601String(),
      );

      // Extraire les informations importantes
      final String? transactionId = params['transaction_id'] ?? params['id'];
      final String? amount = params['amount'] ?? params['montant'];
      final String? orderId = params['order_id'] ?? params['commande_id'];

      if (transactionId != null) {
        await OrderService.saveData(
          'last_successful_transaction_id',
          transactionId,
        );
      }

      print(
        '$_tag: Transaction ID: $transactionId, Montant: $amount, Commande: $orderId',
      );
    } catch (e) {
      print('$_tag: Erreur lors du traitement du succès de paiement: $e');
    }
  }

  /// Gère l'annulation du paiement
  static Future<void> _handlePaymentCancel(Map<String, String> params) async {
    try {
      print('$_tag: Paiement annulé détecté');
      print('$_tag: Paramètres: $params');

      // Sauvegarder les informations d'annulation
      await OrderService.saveData('payment_status', 'cancelled');
      await OrderService.saveData('payment_cancel_params', params.toString());
      await OrderService.saveData(
        'payment_cancel_timestamp',
        DateTime.now().toIso8601String(),
      );
    } catch (e) {
      print(
        '$_tag: Erreur lors du traitement de l\'annulation de paiement: $e',
      );
    }
  }

  /// Gère l'échec du paiement
  static Future<void> _handlePaymentFailure(Map<String, String> params) async {
    try {
      print('$_tag: Échec de paiement détecté');
      print('$_tag: Paramètres: $params');

      // Sauvegarder les informations d'échec
      await OrderService.saveData('payment_status', 'failed');
      await OrderService.saveData('payment_failure_params', params.toString());
      await OrderService.saveData(
        'payment_failure_timestamp',
        DateTime.now().toIso8601String(),
      );

      final String? errorCode = params['error_code'];
      final String? errorMessage = params['error_message'] ?? params['message'];

      print('$_tag: Code d\'erreur: $errorCode, Message: $errorMessage');
    } catch (e) {
      print('$_tag: Erreur lors du traitement de l\'échec de paiement: $e');
    }
  }

  /// Nettoie les ressources du service
  static void dispose() {
    _linkSubscription?.cancel();
    _linkStreamController.close();
    _onLinkReceived = null;
  }

  /// Génère une URL de deep link pour l'application
  static String generateAppDeepLink(
    String path, {
    Map<String, String>? params,
  }) {
    final uri = Uri(
      scheme: ApiConfig.appScheme,
      host: 'payment',
      path: path,
      queryParameters: params,
    );
    return uri.toString();
  }

  /// Vérifie si un deep link est valide pour l'application
  static bool isValidAppDeepLink(String link) {
    final uri = Uri.tryParse(link);
    return uri != null && uri.scheme == ApiConfig.appScheme;
  }
}
