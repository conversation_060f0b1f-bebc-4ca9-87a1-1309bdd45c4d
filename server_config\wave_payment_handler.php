<?php
/**
 * Gestionnaire de paiement Wave pour Callitris
 * 
 * À déployer sur: https://dev-mani.io/teams/client-api.callitris-distribution.com/
 * 
 * Ce fichier gère:
 * 1. L'initialisation des paiements Wave
 * 2. Les callbacks de retour Wave
 * 3. La redirection vers l'application mobile
 */

header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization');

// Configuration
define('APP_SCHEME', 'callitris');
define('LOG_FILE', __DIR__ . '/logs/wave_payments.log');

// Configuration de base de données (à personnaliser)
define('DB_HOST', 'localhost');
define('DB_NAME', 'callitris_db');
define('DB_USER', 'votre_utilisateur');
define('DB_PASS', 'votre_mot_de_passe');

// Configuration Wave (à personnaliser)
define('WAVE_API_KEY', 'votre_cle_api_wave');
define('WAVE_SECRET', 'votre_secret_wave');
define('WAVE_SANDBOX', true); // false pour la production

/**
 * Fonction de logging
 */
function logMessage($message) {
    $timestamp = date('Y-m-d H:i:s');
    $logEntry = "[$timestamp] $message" . PHP_EOL;
    
    $logDir = dirname(LOG_FILE);
    if (!is_dir($logDir)) {
        mkdir($logDir, 0755, true);
    }
    
    file_put_contents(LOG_FILE, $logEntry, FILE_APPEND | LOCK_EX);
}

/**
 * Connexion à la base de données
 */
function getDbConnection() {
    try {
        $pdo = new PDO(
            'mysql:host=' . DB_HOST . ';dbname=' . DB_NAME . ';charset=utf8mb4',
            DB_USER,
            DB_PASS,
            [
                PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
                PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
                PDO::ATTR_EMULATE_PREPARES => false,
            ]
        );
        return $pdo;
    } catch (PDOException $e) {
        logMessage("Erreur de connexion DB: " . $e->getMessage());
        return null;
    }
}

/**
 * Initialise un paiement Wave
 */
function initializeWavePayment($data) {
    try {
        logMessage("Initialisation paiement Wave: " . json_encode($data));
        
        // Validation des données
        $requiredFields = ['amount', 'customer_phone', 'order_id'];
        foreach ($requiredFields as $field) {
            if (!isset($data[$field]) || empty($data[$field])) {
                throw new Exception("Champ requis manquant: $field");
            }
        }
        
        $amount = floatval($data['amount']);
        $customerPhone = $data['customer_phone'];
        $orderId = $data['order_id'];
        $customerId = $data['customer_id'] ?? null;
        
        // Générer un ID de transaction unique
        $transactionId = 'wave_' . uniqid() . '_' . time();
        
        // URLs de callback pour Wave
        $baseUrl = 'https://dev-mani.io/teams/client-api.callitris-distribution.com';
        $successUrl = $baseUrl . '/callback/wave/success';
        $failureUrl = $baseUrl . '/callback/wave/failure';
        $cancelUrl = $baseUrl . '/callback/wave/cancel';
        $notifyUrl = $baseUrl . '/callback/wave/notify';
        
        // Préparer les données pour Wave
        $waveData = [
            'amount' => $amount,
            'currency' => 'XOF',
            'customer_phone' => $customerPhone,
            'transaction_id' => $transactionId,
            'description' => 'Paiement Callitris - Commande #' . $orderId,
            'success_url' => $successUrl,
            'failure_url' => $failureUrl,
            'cancel_url' => $cancelUrl,
            'notify_url' => $notifyUrl,
            'metadata' => [
                'order_id' => $orderId,
                'customer_id' => $customerId,
                'app_name' => 'Callitris',
                'timestamp' => time()
            ]
        ];
        
        // Sauvegarder la transaction en base
        $pdo = getDbConnection();
        if ($pdo) {
            $sql = "INSERT INTO wave_transactions 
                    (transaction_id, order_id, customer_id, amount, status, created_at) 
                    VALUES (:transaction_id, :order_id, :customer_id, :amount, 'pending', NOW())";
            
            $stmt = $pdo->prepare($sql);
            $stmt->execute([
                ':transaction_id' => $transactionId,
                ':order_id' => $orderId,
                ':customer_id' => $customerId,
                ':amount' => $amount
            ]);
        }
        
        // Ici, vous devez appeler l'API Wave pour initialiser le paiement
        // Exemple d'appel API Wave (à adapter selon la documentation Wave)
        /*
        $waveResponse = callWaveAPI('POST', '/payments/initialize', $waveData);
        
        if ($waveResponse && isset($waveResponse['payment_url'])) {
            return [
                'success' => true,
                'wave_payment_url' => $waveResponse['payment_url'],
                'transaction_id' => $transactionId
            ];
        }
        */
        
        // Pour les tests, retourner une URL simulée
        $simulatedWaveUrl = "https://pay.wave.com/c/test-payment?amount=$amount&currency=XOF&transaction_id=$transactionId";
        
        return [
            'success' => true,
            'wave_payment_url' => $simulatedWaveUrl,
            'transaction_id' => $transactionId,
            'message' => 'Paiement Wave initialisé avec succès'
        ];
        
    } catch (Exception $e) {
        logMessage("Erreur initialisation Wave: " . $e->getMessage());
        return [
            'success' => false,
            'message' => $e->getMessage()
        ];
    }
}

/**
 * Traite les callbacks Wave
 */
function handleWaveCallback($type, $data) {
    try {
        logMessage("Callback Wave reçu - Type: $type, Data: " . json_encode($data));
        
        $transactionId = $data['transaction_id'] ?? null;
        $status = $data['status'] ?? $type;
        $amount = $data['amount'] ?? 0;
        $orderId = $data['order_id'] ?? null;
        
        // Mettre à jour la transaction en base
        $pdo = getDbConnection();
        if ($pdo && $transactionId) {
            $sql = "UPDATE wave_transactions 
                    SET status = :status, 
                        wave_response = :response,
                        updated_at = NOW()
                    WHERE transaction_id = :transaction_id";
            
            $stmt = $pdo->prepare($sql);
            $stmt->execute([
                ':status' => $status,
                ':response' => json_encode($data),
                ':transaction_id' => $transactionId
            ]);
            
            // Mettre à jour la commande principale
            if ($orderId) {
                $sql2 = "UPDATE commandes 
                         SET payment_status = :status,
                             payment_transaction_id = :transaction_id,
                             payment_amount = :amount,
                             payment_date = NOW()
                         WHERE id = :order_id";
                
                $stmt2 = $pdo->prepare($sql2);
                $stmt2->execute([
                    ':status' => $status,
                    ':transaction_id' => $transactionId,
                    ':amount' => $amount,
                    ':order_id' => $orderId
                ]);
            }
        }
        
        // Générer le deep link approprié
        $deepLinkParams = [
            'transaction_id' => $transactionId,
            'status' => $status,
            'timestamp' => time()
        ];
        
        if ($amount > 0) {
            $deepLinkParams['amount'] = $amount;
        }
        
        if ($orderId) {
            $deepLinkParams['order_id'] = $orderId;
        }
        
        if (isset($data['error_message'])) {
            $deepLinkParams['error_message'] = $data['error_message'];
        }
        
        $deepLink = generateDeepLink($type, $deepLinkParams);
        
        logMessage("Deep link généré: $deepLink");
        
        // Rediriger vers l'application
        redirectToApp($deepLink);
        
    } catch (Exception $e) {
        logMessage("Erreur traitement callback: " . $e->getMessage());
        http_response_code(500);
        echo json_encode(['error' => $e->getMessage()]);
    }
}

/**
 * Génère un deep link vers l'application
 */
function generateDeepLink($type, $params = []) {
    $baseUrl = APP_SCHEME . '://payment/' . $type;
    
    if (!empty($params)) {
        $queryString = http_build_query($params);
        $baseUrl .= '?' . $queryString;
    }
    
    return $baseUrl;
}

/**
 * Redirige vers l'application mobile
 */
function redirectToApp($deepLink) {
    $html = '<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Retour vers Callitris</title>
    <style>
        body { font-family: Arial, sans-serif; text-align: center; padding: 50px; background: #f5f5f5; }
        .container { max-width: 400px; margin: 0 auto; background: white; padding: 30px; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .logo { width: 80px; height: 80px; margin: 0 auto 20px; background: #007bff; border-radius: 50%; display: flex; align-items: center; justify-content: center; color: white; font-size: 24px; font-weight: bold; }
        .btn { display: inline-block; padding: 12px 24px; background: #007bff; color: white; text-decoration: none; border-radius: 5px; margin: 10px; }
    </style>
</head>
<body>
    <div class="container">
        <div class="logo">C</div>
        <h2>Retour vers Callitris</h2>
        <p>Redirection en cours...</p>
        <a href="' . htmlspecialchars($deepLink) . '" class="btn">Ouvrir Callitris</a>
    </div>
    
    <script>
        setTimeout(function() {
            window.location.href = "' . htmlspecialchars($deepLink) . '";
        }, 1000);
    </script>
</body>
</html>';
    
    echo $html;
    exit();
}

// Traitement principal
$requestMethod = $_SERVER['REQUEST_METHOD'];
$requestUri = $_SERVER['REQUEST_URI'];

logMessage("Requête reçue - Method: $requestMethod, URI: $requestUri");

// Déterminer le type d'action
if (strpos($requestUri, '/callback/wave/') !== false) {
    // C'est un callback Wave
    $pathSegments = explode('/', trim(parse_url($requestUri, PHP_URL_PATH), '/'));
    $callbackType = end($pathSegments);
    
    $data = [];
    if ($requestMethod === 'POST') {
        $rawInput = file_get_contents('php://input');
        $data = json_decode($rawInput, true) ?: $_POST;
    } else {
        $data = $_GET;
    }
    
    handleWaveCallback($callbackType, $data);
    
} elseif (strpos($requestUri, '/wave/initialize') !== false) {
    // Initialisation d'un paiement Wave
    if ($requestMethod === 'POST') {
        $rawInput = file_get_contents('php://input');
        $data = json_decode($rawInput, true) ?: $_POST;
        
        $result = initializeWavePayment($data);
        echo json_encode($result);
    } else {
        http_response_code(405);
        echo json_encode(['error' => 'Méthode non autorisée']);
    }
    
} else {
    // Endpoint non reconnu
    http_response_code(404);
    echo json_encode(['error' => 'Endpoint non trouvé']);
}
?>
