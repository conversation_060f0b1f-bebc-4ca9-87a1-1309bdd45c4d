import 'dart:async';
import 'package:flutter/foundation.dart';
import 'package:rxdart/rxdart.dart';
import 'package:connectivity_plus/connectivity_plus.dart';

/// Service de gestion d'état global pour l'application Callitris
/// Gère la réactivité, le cache et les mises à jour automatiques
class AppStateService extends ChangeNotifier {
  static const String _tag = '[AppStateService]';

  // Instance singleton
  static AppStateService? _instance;
  static AppStateService get instance => _instance ??= AppStateService._();

  AppStateService._() {
    _initializeService();
  }

  // Streams pour la réactivité
  final BehaviorSubject<bool> _isLoadingSubject = BehaviorSubject<bool>.seeded(
    false,
  );
  final BehaviorSubject<String?> _errorSubject =
      BehaviorSubject<String?>.seeded(null);
  final BehaviorSubject<bool> _isConnectedSubject =
      BehaviorSubject<bool>.seeded(true);
  final BehaviorSubject<DateTime> _lastUpdateSubject =
      BehaviorSubject<DateTime>.seeded(DateTime.now());

  // Cache des données
  final Map<String, CacheEntry> _cache = {};
  final Map<String, Timer> _refreshTimers = {};

  // Streams publics
  Stream<bool> get isLoadingStream => _isLoadingSubject.stream;
  Stream<String?> get errorStream => _errorSubject.stream;
  Stream<bool> get isConnectedStream => _isConnectedSubject.stream;
  Stream<DateTime> get lastUpdateStream => _lastUpdateSubject.stream;

  // Getters
  bool get isLoading => _isLoadingSubject.value;
  String? get error => _errorSubject.value;
  bool get isConnected => _isConnectedSubject.value;
  DateTime get lastUpdate => _lastUpdateSubject.value;

  // Contrôleurs pour différents types de données
  final BehaviorSubject<Map<String, dynamic>?> _userDataSubject =
      BehaviorSubject<Map<String, dynamic>?>.seeded(null);
  final BehaviorSubject<List<Map<String, dynamic>>> _ordersSubject =
      BehaviorSubject<List<Map<String, dynamic>>>.seeded([]);
  final BehaviorSubject<List<Map<String, dynamic>>> _categoriesSubject =
      BehaviorSubject<List<Map<String, dynamic>>>.seeded([]);
  final BehaviorSubject<List<Map<String, dynamic>>> _productsSubject =
      BehaviorSubject<List<Map<String, dynamic>>>.seeded([]);

  // Streams pour les données
  Stream<Map<String, dynamic>?> get userDataStream => _userDataSubject.stream;
  Stream<List<Map<String, dynamic>>> get ordersStream => _ordersSubject.stream;
  Stream<List<Map<String, dynamic>>> get categoriesStream =>
      _categoriesSubject.stream;
  Stream<List<Map<String, dynamic>>> get productsStream =>
      _productsSubject.stream;

  // Getters pour les données
  Map<String, dynamic>? get userData => _userDataSubject.value;
  List<Map<String, dynamic>> get orders => _ordersSubject.value;
  List<Map<String, dynamic>> get categories => _categoriesSubject.value;
  List<Map<String, dynamic>> get products => _productsSubject.value;

  /// Initialise le service
  void _initializeService() {
    _setupConnectivityListener();
    _setupAutoRefresh();
    print('$_tag: Service d\'état global initialisé');
  }

  /// Configure l'écoute de la connectivité
  void _setupConnectivityListener() {
    Connectivity().onConnectivityChanged.listen((
      List<ConnectivityResult> results,
    ) {
      final isConnected =
          results.isNotEmpty &&
          !results.every((result) => result == ConnectivityResult.none);
      _isConnectedSubject.add(isConnected);

      if (isConnected) {
        print('$_tag: Connexion rétablie - Rafraîchissement des données');
        refreshAllData();
      } else {
        print('$_tag: Connexion perdue - Mode hors ligne');
      }
    });
  }

  /// Configure le rafraîchissement automatique
  void _setupAutoRefresh() {
    // Rafraîchissement automatique toutes les 30 secondes si l'app est active
    Timer.periodic(const Duration(seconds: 30), (timer) {
      if (isConnected && !isLoading) {
        refreshAllData(silent: true);
      }
    });
  }

  /// Met à jour l'état de chargement (optimisé pour éviter les émissions inutiles)
  void setLoading(bool loading) {
    if (_isLoadingSubject.value != loading) {
      _isLoadingSubject.add(loading);
      notifyListeners();
    }
  }

  /// Met à jour l'erreur (optimisé pour éviter les émissions inutiles)
  void setError(String? error) {
    if (_errorSubject.value != error) {
      _errorSubject.add(error);
      notifyListeners();
    }
  }

  /// Efface l'erreur
  void clearError() {
    setError(null);
  }

  /// Met à jour les données utilisateur (optimisé)
  void updateUserData(Map<String, dynamic>? userData) {
    // Vérifier si les données ont réellement changé
    if (!_deepEquals(_userDataSubject.value, userData)) {
      _userDataSubject.add(userData);
      _updateCache('user_data', userData);
      _updateLastUpdate();
      notifyListeners();
    }
  }

  /// Met à jour les commandes (optimisé)
  void updateOrders(List<Map<String, dynamic>> orders) {
    // Vérifier si les commandes ont réellement changé
    if (!_deepEquals(_ordersSubject.value, orders)) {
      _ordersSubject.add(orders);
      _updateCache('orders', orders);
      _updateLastUpdate();
      notifyListeners();
    }
  }

  /// Met à jour les catégories
  void updateCategories(List<Map<String, dynamic>> categories) {
    _categoriesSubject.add(categories);
    _updateCache('categories', categories);
    _updateLastUpdate();
    notifyListeners();
  }

  /// Met à jour les produits
  void updateProducts(List<Map<String, dynamic>> products) {
    _productsSubject.add(products);
    _updateCache('products', products);
    _updateLastUpdate();
    notifyListeners();
  }

  /// Met à jour le timestamp de dernière mise à jour
  void _updateLastUpdate() {
    _lastUpdateSubject.add(DateTime.now());
  }

  /// Met à jour le cache
  void _updateCache(String key, dynamic data) {
    _cache[key] = CacheEntry(
      data: data,
      timestamp: DateTime.now(),
      ttl: const Duration(minutes: 5),
    );
  }

  /// Récupère des données du cache
  T? getCachedData<T>(String key) {
    final entry = _cache[key];
    if (entry != null && !entry.isExpired) {
      return entry.data as T?;
    }
    return null;
  }

  /// Vérifie si les données sont en cache et valides
  bool isCacheValid(String key) {
    final entry = _cache[key];
    return entry != null && !entry.isExpired;
  }

  /// Rafraîchit toutes les données
  Future<void> refreshAllData({bool silent = false}) async {
    try {
      if (!silent) {
        setLoading(true);
        clearError();
      }

      print('$_tag: Rafraîchissement de toutes les données...');

      // Ici, vous devez appeler vos services pour récupérer les données
      // Exemple :
      // await _refreshUserData();
      // await _refreshOrders();
      // await _refreshCategories();
      // await _refreshProducts();

      print('$_tag: Toutes les données ont été rafraîchies');
    } catch (e) {
      print('$_tag: Erreur lors du rafraîchissement: $e');
      setError('Erreur lors du rafraîchissement des données');
    } finally {
      if (!silent) {
        setLoading(false);
      }
    }
  }

  /// Rafraîchit les données utilisateur
  Future<void> refreshUserData() async {
    try {
      print('$_tag: Rafraîchissement des données utilisateur...');
      // Appel au service d'authentification
      // final userData = await AuthService.getUserData();
      // updateUserData(userData);
    } catch (e) {
      print(
        '$_tag: Erreur lors du rafraîchissement des données utilisateur: $e',
      );
      setError('Erreur lors du rafraîchissement du profil');
    }
  }

  /// Rafraîchit les commandes
  Future<void> refreshOrders() async {
    try {
      print('$_tag: Rafraîchissement des commandes...');
      // Appel au service de commandes
      // final orders = await OrderService.getUserOrders();
      // updateOrders(orders);
    } catch (e) {
      print('$_tag: Erreur lors du rafraîchissement des commandes: $e');
      setError('Erreur lors du rafraîchissement des commandes');
    }
  }

  /// Invalide le cache pour une clé donnée
  void invalidateCache(String key) {
    _cache.remove(key);
    print('$_tag: Cache invalidé pour: $key');
  }

  /// Invalide tout le cache
  void invalidateAllCache() {
    _cache.clear();
    print('$_tag: Tout le cache a été invalidé');
  }

  /// Configure un rafraîchissement automatique pour une clé
  void setupAutoRefresh(
    String key,
    Duration interval,
    Future<void> Function() refreshFunction,
  ) {
    _refreshTimers[key]?.cancel();

    _refreshTimers[key] = Timer.periodic(interval, (timer) async {
      if (isConnected && !isLoading) {
        try {
          await refreshFunction();
        } catch (e) {
          print(
            '$_tag: Erreur lors du rafraîchissement automatique de $key: $e',
          );
        }
      }
    });
  }

  /// Arrête le rafraîchissement automatique pour une clé
  void stopAutoRefresh(String key) {
    _refreshTimers[key]?.cancel();
    _refreshTimers.remove(key);
  }

  /// Compare deux objets en profondeur pour éviter les mises à jour inutiles
  bool _deepEquals(dynamic a, dynamic b) {
    if (identical(a, b)) return true;
    if (a == null || b == null) return a == b;

    if (a.runtimeType != b.runtimeType) return false;

    if (a is Map && b is Map) {
      if (a.length != b.length) return false;
      for (final key in a.keys) {
        if (!b.containsKey(key) || !_deepEquals(a[key], b[key])) {
          return false;
        }
      }
      return true;
    }

    if (a is List && b is List) {
      if (a.length != b.length) return false;
      for (int i = 0; i < a.length; i++) {
        if (!_deepEquals(a[i], b[i])) return false;
      }
      return true;
    }

    return a == b;
  }

  /// Nettoie les ressources
  @override
  void dispose() {
    _isLoadingSubject.close();
    _errorSubject.close();
    _isConnectedSubject.close();
    _lastUpdateSubject.close();
    _userDataSubject.close();
    _ordersSubject.close();
    _categoriesSubject.close();
    _productsSubject.close();

    for (final timer in _refreshTimers.values) {
      timer.cancel();
    }
    _refreshTimers.clear();

    super.dispose();
  }
}

/// Entrée de cache avec TTL
class CacheEntry {
  final dynamic data;
  final DateTime timestamp;
  final Duration ttl;

  CacheEntry({required this.data, required this.timestamp, required this.ttl});

  bool get isExpired => DateTime.now().difference(timestamp) > ttl;
}
