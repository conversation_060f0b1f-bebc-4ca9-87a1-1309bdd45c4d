import 'dart:async';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:callitris/utils/appTheme.dart';
import 'package:callitris/screens/home/<USER>';
import 'package:callitris/services/auth_service.dart';
import 'package:pin_code_fields/pin_code_fields.dart';
import 'package:lottie/lottie.dart';

class OtpVerificationScreen extends StatefulWidget {
  final String phoneNumber;

  const OtpVerificationScreen({super.key, required this.phoneNumber});

  @override
  State<OtpVerificationScreen> createState() => _OtpVerificationScreenState();
}

class _OtpVerificationScreenState extends State<OtpVerificationScreen>
    with SingleTickerProviderStateMixin {
  final TextEditingController _otpController = TextEditingController();
  bool _isLoading = false;
  bool _hasError = false;
  String _errorMessage = '';
  bool _isSuccess = false;

  // Minuteur pour le renvoi du code
  int _remainingTime = 60;
  Timer? _timer;

  late AnimationController _animationController;
  late Animation<double> _fadeAnimation;
  late Animation<Offset> _slideAnimation;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 800),
    );

    _fadeAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _animationController, curve: Curves.easeOut),
    );

    _slideAnimation = Tween<Offset>(
      begin: const Offset(0, 0.1),
      end: Offset.zero,
    ).animate(
      CurvedAnimation(parent: _animationController, curve: Curves.easeOutCubic),
    );

    _animationController.forward();

    // Démarrer le minuteur
    _startTimer();
  }

  @override
  void dispose() {
    _otpController.dispose();
    _timer?.cancel();
    _animationController.dispose();
    super.dispose();
  }

  void _startTimer() {
    _remainingTime = 60;
    _timer?.cancel();
    _timer = Timer.periodic(const Duration(seconds: 1), (timer) {
      if (_remainingTime > 0) {
        setState(() {
          _remainingTime--;
        });
      } else {
        timer.cancel();
      }
    });
  }

  Future<void> _resendOtp() async {
    if (_remainingTime > 0) return;

    setState(() {
      _isLoading = true;
      _hasError = false;
    });

    // Effacer le champ OTP pour que l'utilisateur puisse entrer le nouveau code
    _otpController.clear();

    final result = await AuthService.sendOtp(widget.phoneNumber);

    setState(() {
      _isLoading = false;
    });

    if (result['success'] == true) {
      // Réinitialiser le minuteur
      _startTimer();

      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Row(
            children: [
              Icon(Icons.check_circle, color: Colors.white),
              SizedBox(width: 10),
              Expanded(
                child: Text(
                  'Un nouveau code a été envoyé à ${_formatPhoneNumber(widget.phoneNumber)}',
                  style: TextStyle(color: Colors.white),
                ),
              ),
            ],
          ),
          backgroundColor: AppTheme.color.greenColor,
          behavior: SnackBarBehavior.floating,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(10),
          ),
          margin: EdgeInsets.all(10),
        ),
      );
    } else {
      setState(() {
        _hasError = true;
        _errorMessage = result['message'] ?? 'Erreur lors de l\'envoi du code';
      });
    }
  }

  Future<void> _verifyOtp() async {
    if (_otpController.text.length != 6) {
      setState(() {
        _hasError = true;
        _errorMessage = 'Veuillez entrer un code à 6 chiffres';
      });
      return;
    }

    setState(() {
      _isLoading = true;
      _hasError = false;
    });

    final result = await AuthService.verifyOtp(
      widget.phoneNumber,
      _otpController.text,
    );

    print('Résultat de la vérification: $result');

    // Vérifier si la réponse contient success=true OU si elle contient id_client et token
    if (result['success'] == true ||
        (result.containsKey('id_client') && result.containsKey('token'))) {
      setState(() {
        _isSuccess = true;
        _isLoading = false;
      });

      await Future.delayed(const Duration(milliseconds: 1500));

      if (mounted) {
        Navigator.of(context).pushAndRemoveUntil(
          MaterialPageRoute(builder: (context) => const HomeScreen()),
          (route) => false,
        );
      }
    } else {
      setState(() {
        _isLoading = false;
        _hasError = true;
        _errorMessage = result['message'] ?? 'Code OTP invalide';
      });

      if (_errorMessage.toLowerCase().contains('expir')) {
        setState(() {
          _remainingTime = 0;
        });

        showDialog(
          context: context,
          barrierDismissible: false,
          builder:
              (context) => AlertDialog(
                title: Text('Code OTP expiré'),
                content: Text(
                  'Votre code de vérification a expiré. Voulez-vous en recevoir un nouveau?',
                ),
                actions: [
                  TextButton(
                    onPressed: () {
                      Navigator.of(context).pop();
                    },
                    child: Text('Annuler'),
                  ),
                  ElevatedButton(
                    onPressed: () {
                      Navigator.of(context).pop();
                      _resendOtp();
                    },
                    style: ElevatedButton.styleFrom(
                      backgroundColor: AppTheme.color.primaryColor,
                      foregroundColor: Colors.white,
                    ),
                    child: Text('Envoyer un nouveau code'),
                  ),
                ],
              ),
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.white,
      appBar: AppBar(
        backgroundColor: Colors.transparent,
        elevation: 0,
        leading: IconButton(
          icon: Icon(
            Icons.arrow_back_ios,
            color: AppTheme.color.primaryColor,
            size: 20,
          ),
          onPressed: () => Navigator.pop(context),
        ),
        title: Text(
          'Vérification OTP',
          style: TextStyle(
            color: AppTheme.color.primaryColor,
            fontSize: 18,
            fontWeight: FontWeight.w700,
          ),
        ),
        centerTitle: true,
        systemOverlayStyle: SystemUiOverlayStyle.dark,
      ),
      body: SafeArea(
        child: FadeTransition(
          opacity: _fadeAnimation,
          child: SlideTransition(
            position: _slideAnimation,
            child: SingleChildScrollView(
              child: Padding(
                padding: const EdgeInsets.symmetric(horizontal: 24.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.center,
                  children: [
                    const SizedBox(height: 30),

                    _isSuccess
                        ? Lottie.asset(
                          'assets/animations/Success.json',
                          width: 120,
                          height: 120,
                          repeat: false,
                        )
                        : Container(
                          width: 100,
                          height: 100,
                          decoration: BoxDecoration(
                            color: AppTheme.color.primaryColor.withValues(
                              alpha: 0.1,
                            ),
                            shape: BoxShape.circle,
                          ),
                          child: Icon(
                            Icons.sms_outlined,
                            size: 50,
                            color: AppTheme.color.primaryColor,
                          ),
                        ),
                    const SizedBox(height: 24),

                    Text(
                      'Vérification du code',
                      style: TextStyle(
                        color: AppTheme.color.primaryColor,
                        fontSize: 24,
                        fontWeight: FontWeight.w700,
                      ),
                    ),
                    const SizedBox(height: 12),
                    Text(
                      'Nous avons envoyé un code à 6 chiffres au\n${_formatPhoneNumber(widget.phoneNumber)}',
                      textAlign: TextAlign.center,
                      style: TextStyle(
                        color: AppTheme.color.brunGris,
                        fontSize: 15,
                        height: 1.5,
                      ),
                    ),

                    const SizedBox(height: 40),

                    // Champ de saisie OTP
                    PinCodeTextField(
                      appContext: context,
                      length: 6,
                      controller: _otpController,
                      obscureText: false,
                      animationType: AnimationType.scale,
                      pinTheme: PinTheme(
                        shape: PinCodeFieldShape.box,
                        borderRadius: BorderRadius.circular(12),
                        fieldHeight: 56,
                        fieldWidth: 44,
                        activeFillColor: Colors.white,
                        inactiveFillColor: Colors.grey.shade50,
                        selectedFillColor: AppTheme.color.primaryColor
                            .withValues(alpha: 0.05),
                        activeColor: AppTheme.color.primaryColor,
                        inactiveColor: Colors.grey.shade300,
                        selectedColor: AppTheme.color.primaryColor,
                      ),
                      cursorColor: AppTheme.color.primaryColor,
                      animationDuration: const Duration(milliseconds: 300),
                      enableActiveFill: true,
                      keyboardType: TextInputType.number,
                      onChanged: (value) {
                        if (_hasError) {
                          setState(() {
                            _hasError = false;
                          });
                        }

                        if (value.length == 6) {
                          _verifyOtp();
                        }
                      },
                      beforeTextPaste: (text) {
                        return text != null &&
                            RegExp(r'^[0-9]+$').hasMatch(text);
                      },
                    ),
                    const SizedBox(height: 16),

                    AnimatedContainer(
                      duration: const Duration(milliseconds: 300),
                      height: _hasError ? 60 : 0,
                      child:
                          _hasError
                              ? Container(
                                padding: const EdgeInsets.symmetric(
                                  horizontal: 16,
                                  vertical: 10,
                                ),
                                decoration: BoxDecoration(
                                  color: AppTheme.color.redColor.withValues(
                                    alpha: 0.1,
                                  ),
                                  borderRadius: BorderRadius.circular(12),
                                  border: Border.all(
                                    color: AppTheme.color.redColor.withValues(
                                      alpha: 0.3,
                                    ),
                                    width: 1,
                                  ),
                                ),
                                child: Row(
                                  children: [
                                    Icon(
                                      Icons.error_outline,
                                      color: AppTheme.color.redColor,
                                      size: 20,
                                    ),
                                    const SizedBox(width: 10),
                                    Expanded(
                                      child: Text(
                                        _errorMessage,
                                        style: TextStyle(
                                          color: AppTheme.color.redColor,
                                          fontSize: 13,
                                          fontWeight: FontWeight.w500,
                                        ),
                                      ),
                                    ),
                                  ],
                                ),
                              )
                              : const SizedBox(),
                    ),

                    const SizedBox(height: 32),

                    Container(
                      width: double.infinity,
                      height: 56,
                      decoration: BoxDecoration(
                        gradient: LinearGradient(
                          colors: [
                            AppTheme.color.primaryColor,
                            AppTheme.color.primaryColor.withBlue(
                              (AppTheme.color.primaryColor.blue * 0.8).toInt(),
                            ),
                          ],
                          begin: Alignment.topLeft,
                          end: Alignment.bottomRight,
                        ),
                        borderRadius: BorderRadius.circular(16),
                        boxShadow: [
                          BoxShadow(
                            color: AppTheme.color.primaryColor.withValues(
                              alpha: 0.3,
                            ),
                            blurRadius: 12,
                            offset: const Offset(0, 6),
                          ),
                        ],
                      ),
                      child: ElevatedButton(
                        onPressed: _isLoading || _isSuccess ? null : _verifyOtp,
                        style: ElevatedButton.styleFrom(
                          backgroundColor: Colors.transparent,
                          foregroundColor: Colors.white,
                          shadowColor: Colors.transparent,
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(16),
                          ),
                          elevation: 0,
                        ),
                        child:
                            _isLoading
                                ? const SizedBox(
                                  width: 24,
                                  height: 24,
                                  child: CircularProgressIndicator(
                                    strokeWidth: 2.5,
                                    valueColor: AlwaysStoppedAnimation<Color>(
                                      Colors.white,
                                    ),
                                  ),
                                )
                                : _isSuccess
                                ? Row(
                                  mainAxisAlignment: MainAxisAlignment.center,
                                  children: [
                                    Icon(
                                      Icons.check_circle,
                                      color: Colors.white,
                                    ),
                                    SizedBox(width: 8),
                                    Text(
                                      'Vérifié avec succès',
                                      style: TextStyle(
                                        fontSize: 16,
                                        fontWeight: FontWeight.w700,
                                      ),
                                    ),
                                  ],
                                )
                                : const Text(
                                  'Vérifier',
                                  style: TextStyle(
                                    fontSize: 16,
                                    fontWeight: FontWeight.w700,
                                  ),
                                ),
                      ),
                    ),

                    const SizedBox(height: 24),

                    // Minuteur et bouton de renvoi améliorés
                    Container(
                      padding: EdgeInsets.symmetric(
                        horizontal: 1,
                        vertical: 15,
                      ),
                      decoration: BoxDecoration(
                        color:
                            _remainingTime > 0
                                ? Colors.grey.shade50
                                : AppTheme.color.redColor.withValues(
                                  alpha: 0.05,
                                ),
                        borderRadius: BorderRadius.circular(12),
                        border:
                            _remainingTime == 0
                                ? Border.all(
                                  color: AppTheme.color.redColor.withValues(
                                    alpha: 0.2,
                                  ),
                                  width: 1,
                                )
                                : null,
                      ),
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Icon(
                            _remainingTime > 0
                                ? Icons.timer_outlined
                                : Icons.error_outline,
                            size: 18,
                            color:
                                _remainingTime > 0
                                    ? AppTheme.color.brunGris
                                    : AppTheme.color.redColor,
                          ),
                          const SizedBox(width: 8),
                          Text(
                            _remainingTime > 0
                                ? 'Vous n\'avez pas reçu de code?'
                                : 'Code expiré ou invalide?',
                            style: TextStyle(
                              color:
                                  _remainingTime > 0
                                      ? AppTheme.color.brunGris
                                      : AppTheme.color.redColor,
                              fontSize: 14,
                            ),
                          ),
                          const SizedBox(width: 8),
                          _remainingTime > 0
                              ? TweenAnimationBuilder<double>(
                                tween: Tween<double>(begin: 1.0, end: 0.0),
                                duration: Duration(seconds: _remainingTime),
                                builder: (context, value, child) {
                                  return Row(
                                    children: [
                                      SizedBox(
                                        width: 20,
                                        height: 20,
                                        child: CircularProgressIndicator(
                                          value: value,
                                          strokeWidth: 2,
                                          backgroundColor: Colors.grey.shade300,
                                          valueColor:
                                              AlwaysStoppedAnimation<Color>(
                                                AppTheme.color.primaryColor,
                                              ),
                                        ),
                                      ),
                                      const SizedBox(width: 8),
                                      Text(
                                        _formatTime(_remainingTime),
                                        style: TextStyle(
                                          color: AppTheme.color.primaryColor,
                                          fontWeight: FontWeight.w600,
                                          fontSize: 14,
                                        ),
                                      ),
                                    ],
                                  );
                                },
                              )
                              : TextButton(
                                onPressed: _resendOtp,
                                style: TextButton.styleFrom(
                                  backgroundColor: AppTheme.color.primaryColor,
                                  padding: EdgeInsets.symmetric(
                                    horizontal: 12,
                                    vertical: 8,
                                  ),
                                  shape: RoundedRectangleBorder(
                                    borderRadius: BorderRadius.circular(8),
                                  ),
                                ),
                                child: Text(
                                  'Renvoyer le code',
                                  style: TextStyle(
                                    color: Colors.white,
                                    fontWeight: FontWeight.w700,
                                    fontSize: 14,
                                  ),
                                ),
                              ),
                        ],
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ),
        ),
      ),
    );
  }

  // Formater le numéro de téléphone pour l'affichage
  String _formatPhoneNumber(String phoneNumber) {
    if (phoneNumber.length != 10) return phoneNumber;

    return '${phoneNumber.substring(0, 2)} ${phoneNumber.substring(2, 4)} ${phoneNumber.substring(4, 6)} ${phoneNumber.substring(6, 8)} ${phoneNumber.substring(8, 10)}';
  }

  // Formater le temps restant
  String _formatTime(int seconds) {
    final int minutes = seconds ~/ 60;
    final int remainingSeconds = seconds % 60;

    return '$minutes:${remainingSeconds.toString().padLeft(2, '0')}';
  }
}
