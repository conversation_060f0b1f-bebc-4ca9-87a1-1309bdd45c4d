import 'package:flutter/material.dart';
import 'package:callitris/config/cinetpay_config.dart';

/// Énumération des méthodes de paiement disponibles
enum PaymentMethod {
  monnaie, // Utiliser la monnaie du portefeuille
  cinetpay,
  orange<PERSON>oney,
  mtnMoney,
  moovMoney,
  wave,
}

/// Extension pour vérifier si une méthode utilise CinetPay
extension PaymentMethodExtension on PaymentMethod {
  /// Retourne true si cette méthode de paiement utilise CinetPay
  bool get usesCinetPay {
    switch (this) {
      case PaymentMethod.cinetpay:
      case PaymentMethod.orangeMoney:
      case PaymentMethod.mtnMoney:
      case PaymentMethod.moovMoney:
        return true;
      case PaymentMethod.monnaie:
      case PaymentMethod.wave:
        return false;
    }
  }

  /// Retourne le nom d'affichage de la méthode de paiement
  String get displayName {
    switch (this) {
      case PaymentMethod.monnaie:
        return 'Monnaie du portefeuille';
      case PaymentMethod.cinetpay:
        return 'CinetPay';
      case PaymentMethod.orangeMoney:
        return 'Orange Money';
      case PaymentMethod.mtnMoney:
        return 'MTN Money';
      case PaymentMethod.moovMoney:
        return 'Moov Money';
      case PaymentMethod.wave:
        return 'Wave CI';
    }
  }
}

/// Widget pour sélectionner la méthode de paiement
class PaymentMethodSelector extends StatefulWidget {
  final PaymentMethod selectedMethod;
  final Function(PaymentMethod) onMethodChanged;
  final double montantVerse;
  final double monnaieDisponible;
  final bool enabled;

  const PaymentMethodSelector({
    super.key,
    required this.selectedMethod,
    required this.onMethodChanged,
    required this.montantVerse,
    required this.monnaieDisponible,
    this.enabled = true,
  });

  @override
  State<PaymentMethodSelector> createState() => _PaymentMethodSelectorState();
}

class _PaymentMethodSelectorState extends State<PaymentMethodSelector> {
  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'Méthode de paiement',
          style: TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.w600,
            color: Color(0xFF0A0A0A),
          ),
        ),
        const SizedBox(height: 12),

        // Option Monnaie

        const SizedBox(height: 12),

        _buildPaymentOption(
          method: PaymentMethod.wave,
          title: 'WAVE CI',
          subtitle: 'Paiement instantané via WAVE CI',
          url: 'assets/images/wave_ci.png',
          iconColor: const Color.fromARGB(255, 54, 115, 255),
          enabled: widget.enabled,
        ),
        const SizedBox(height: 6),
        // Options CinetPay
        _buildPaymentOption(
          method: PaymentMethod.orangeMoney,
          title: 'Orange Money',
          subtitle: '',
          url: "assets/orange_money.png",
          iconColor: const Color(0xFF4CAF50),
          enabled: widget.enabled,
        ),
        const SizedBox(height: 6),
        _buildPaymentOption(
          method: PaymentMethod.mtnMoney,
          title: 'MTN Money',
          subtitle: '',
          url: "assets/mtn_momo.png",
          iconColor: const Color(0xFF4CAF50),
          enabled: widget.enabled,
        ),
        const SizedBox(height: 6),
        _buildPaymentOption(
          method: PaymentMethod.moovMoney,
          title: 'Moov Money',
          subtitle: '',
          url: "assets/images/moov_money.png",
          iconColor: const Color(0xFF4CAF50),
          enabled: widget.enabled,
        ),
        const SizedBox(height: 6),

        _buildPaymentOption(
          method: PaymentMethod.monnaie,
          title:
              'Monnaie : ${widget.monnaieDisponible.toStringAsFixed(0)} FCFA',
          subtitle: '',
          url: "assets/images/wallet.png",
          iconColor: const Color(0xFF2E7D32),
          enabled:
              widget.enabled && widget.monnaieDisponible >= widget.montantVerse,
        ),
        // Message d'information pour CinetPay
        if (widget.selectedMethod.usesCinetPay) ...[
          const SizedBox(height: 12),
          Container(
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: const Color(0xFF4CAF50).withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(8),
              border: Border.all(
                color: const Color(0xFF4CAF50).withValues(alpha: 0.3),
              ),
            ),
            child: Row(
              children: [
                Icon(
                  Icons.info_outline,
                  color: const Color(0xFF4CAF50),
                  size: 20,
                ),
                const SizedBox(width: 8),
                Expanded(
                  child: Text(
                    'Vous serez redirigé vers la page de paiement CinetPay',
                    style: TextStyle(
                      fontSize: 12,
                      color: const Color(0xFF4CAF50),
                    ),
                  ),
                ),
              ],
            ),
          ),
        ],

        if (widget.selectedMethod == PaymentMethod.wave) ...[
          const SizedBox(height: 12),
          Container(
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: const Color.fromARGB(
                255,
                54,
                115,
                255,
              ).withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(8),
              border: Border.all(
                color: const Color.fromARGB(
                  255,
                  54,
                  115,
                  255,
                ).withValues(alpha: 0.3),
              ),
            ),
            child: Row(
              children: [
                Icon(
                  Icons.info_outline,
                  color: const Color.fromARGB(255, 54, 115, 255),
                  size: 20,
                ),
                const SizedBox(width: 8),
                Expanded(
                  child: Text(
                    'Vous serez redirigé vers l\'application WAVE pour effectuer le paiement',
                    style: TextStyle(
                      fontSize: 12,
                      color: const Color.fromARGB(255, 54, 115, 255),
                    ),
                  ),
                ),
              ],
            ),
          ),
        ],

        // Message d'erreur si pas assez de monnaie
        if (widget.selectedMethod == PaymentMethod.monnaie &&
            widget.monnaieDisponible < widget.montantVerse) ...[
          const SizedBox(height: 12),
          Container(
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: Colors.red.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(8),
              border: Border.all(color: Colors.red.withValues(alpha: 0.3)),
            ),
            child: Row(
              children: [
                Icon(Icons.warning_outlined, color: Colors.red, size: 20),
                const SizedBox(width: 8),
                Expanded(
                  child: Text(
                    'Solde insuffisant. Il vous manque ${(widget.montantVerse - widget.monnaieDisponible).toStringAsFixed(0)} FCFA',
                    style: TextStyle(fontSize: 12, color: Colors.red),
                  ),
                ),
              ],
            ),
          ),
        ],
      ],
    );
  }

  Widget _buildPaymentOption({
    required PaymentMethod method,
    required String title,
    required String subtitle,
    required String url,
    required Color iconColor,
    required bool enabled,
  }) {
    final isSelected = widget.selectedMethod == method;

    return GestureDetector(
      onTap: enabled ? () => widget.onMethodChanged(method) : null,
      child: AnimatedContainer(
        duration: const Duration(milliseconds: 200),
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          color:
              enabled
                  ? (isSelected
                      ? iconColor.withValues(alpha: 0.1)
                      : Colors.white)
                  : Colors.grey.shade100,
          borderRadius: BorderRadius.circular(12),
          border: Border.all(
            color:
                enabled
                    ? (isSelected ? iconColor : Colors.grey.shade300)
                    : Colors.grey.shade300,
            width: isSelected ? 2 : 1,
          ),
        ),
        child: Row(
          children: [
            // Radio button
            Container(
              width: 20,
              height: 20,
              decoration: BoxDecoration(
                shape: BoxShape.circle,
                border: Border.all(
                  color:
                      enabled
                          ? (isSelected ? iconColor : Colors.grey.shade400)
                          : Colors.grey.shade400,
                  width: 2,
                ),
                color: isSelected ? iconColor : Colors.transparent,
              ),
              child:
                  isSelected
                      ? const Icon(Icons.check, size: 12, color: Colors.white)
                      : null,
            ),

            const SizedBox(width: 12),

            // Icon
            Container(
              padding: const EdgeInsets.all(8),
              decoration: BoxDecoration(
                color:
                    enabled
                        ? iconColor.withValues(alpha: 0.1)
                        : Colors.grey.shade200,
                borderRadius: BorderRadius.circular(8),
              ),
              child: Image.asset(
                url,
                width: 40,
                height: 40,
                fit: BoxFit.contain,
                errorBuilder: (context, error, stackTrace) {
                  return Icon(
                    Icons.image_not_supported,
                    size: 40,
                    color: Colors.grey.shade400,
                  );
                },
              ),
            ),

            const SizedBox(width: 12),

            // Text content
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    title,
                    style: TextStyle(
                      fontSize: 14,
                      fontWeight: FontWeight.w600,
                      color:
                          enabled
                              ? const Color(0xFF0A0A0A)
                              : Colors.grey.shade500,
                    ),
                    overflow: TextOverflow.ellipsis,
                    maxLines: 1,
                  ),
                  if (subtitle.isNotEmpty) ...[
                    const SizedBox(height: 2),
                    Text(
                      subtitle,
                      style: TextStyle(
                        fontSize: 12,
                        color:
                            enabled
                                ? Colors.grey.shade600
                                : Colors.grey.shade400,
                      ),
                      overflow: TextOverflow.ellipsis,
                      maxLines: 2,
                    ),
                  ],
                ],
              ),
            ),

            // Disabled indicator
            if (!enabled)
              Icon(Icons.lock_outline, color: Colors.grey.shade400, size: 20),
          ],
        ),
      ),
    );
  }
}

/// Widget pour afficher les méthodes de paiement CinetPay disponibles
class CinetPayMethodsInfo extends StatelessWidget {
  const CinetPayMethodsInfo({super.key});

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.grey.shade200),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(Icons.payment, color: const Color(0xFF4CAF50), size: 20),
              const SizedBox(width: 8),
              const Text(
                'Méthodes de paiement acceptées',
                style: TextStyle(
                  fontSize: 14,
                  fontWeight: FontWeight.w600,
                  color: Color(0xFF0A0A0A),
                ),
              ),
            ],
          ),
          const SizedBox(height: 12),

          // Liste des méthodes
          ...CinetPayConfig.availablePaymentMethods.map((method) {
            return Padding(
              padding: const EdgeInsets.only(bottom: 8),
              child: Row(
                children: [
                  Icon(
                    Icons.check_circle,
                    color: const Color(0xFF4CAF50),
                    size: 16,
                  ),
                  const SizedBox(width: 8),
                  Text(
                    _getMethodDisplayName(method),
                    style: TextStyle(fontSize: 12, color: Colors.grey.shade700),
                  ),
                ],
              ),
            );
          }),

          const SizedBox(height: 8),

          // Note de sécurité
          Container(
            padding: const EdgeInsets.all(8),
            decoration: BoxDecoration(
              color: Colors.blue.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(6),
            ),
            child: Row(
              children: [
                Icon(Icons.security, color: Colors.blue, size: 16),
                const SizedBox(width: 6),
                Expanded(
                  child: Text(
                    'Paiements sécurisés par CinetPay',
                    style: TextStyle(
                      fontSize: 11,
                      color: Colors.blue.shade700,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  String _getMethodDisplayName(String method) {
    switch (method) {
      case 'MOBILE_MONEY':
        return 'Mobile Money (Orange, MTN, Moov)';
      case 'WAVE':
        return 'Wave';
      case 'CREDIT_CARD':
        return 'Carte bancaire';
      case 'PAYPAL':
        return 'PayPal';
      case 'BANK_TRANSFER':
        return 'Virement bancaire';
      default:
        return method;
    }
  }
}
