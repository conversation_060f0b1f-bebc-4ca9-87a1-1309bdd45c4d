import 'package:flutter/material.dart';
import 'package:callitris/screens/auth/login_screen.dart';
import 'package:callitris/screens/home/<USER>';
import 'package:callitris/services/auth_service.dart';
import 'package:callitris/services/deep_link_service.dart';
import 'package:callitris/services/app_state_service.dart';
import 'package:callitris/services/order_service.dart';
import 'package:callitris/services/smart_cache_service.dart';
import 'package:callitris/services/network_service.dart';
import 'package:callitris/services/app_initialization_service.dart';
import 'package:callitris/services/optimized_navigation_service.dart';
import 'package:callitris/screens/payment/payment_return_handler.dart';
import 'package:callitris/widgets/reactive_app_widget.dart';
import 'package:callitris/utils/appTheme.dart';

void main() async {
  WidgetsFlutterBinding.ensureInitialized();

  // Initialiser les services réactifs
  await _initializeServices();

  runApp(const MyApp());
}

/// Initialise tous les services de manière optimisée
Future<void> _initializeServices() async {
  try {
    print('[Main]: Initialisation optimisée des services...');

    // Utiliser le service d'initialisation optimisé
    await AppInitializationService.initialize();

    // Précharger les données essentielles en arrière-plan
    AppInitializationService.preloadEssentialData();

    print('[Main]: Tous les services ont été initialisés avec succès');
  } catch (e) {
    print('[Main]: Erreur lors de l\'initialisation des services: $e');
  }
}

class MyApp extends StatelessWidget {
  const MyApp({super.key});

  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      title: 'Callitris',
      debugShowCheckedModeBanner: false,
      navigatorKey: OptimizedNavigationService.navigatorKey,
      theme: ThemeData(
        primaryColor: AppTheme.color.primaryColor,
        scaffoldBackgroundColor: Colors.white,
        fontFamily: 'Poppins',
        appBarTheme: AppBarTheme(
          backgroundColor: Colors.white,
          elevation: 0,
          iconTheme: IconThemeData(color: AppTheme.color.primaryColor),
          titleTextStyle: TextStyle(
            color: AppTheme.color.primaryColor,
            fontSize: 18,
            fontWeight: FontWeight.w600,
          ),
        ),
      ),
      home: ReactiveAppWidget(
        child: PaymentReturnHandler(child: const AuthChecker()),
      ),
    );
  }
}

class AuthChecker extends StatefulWidget {
  const AuthChecker({super.key});

  @override
  State<AuthChecker> createState() => _AuthCheckerState();
}

class _AuthCheckerState extends State<AuthChecker> {
  @override
  void initState() {
    super.initState();
    // Vérifier immédiatement l'état d'authentification
    _checkAuthStatus();
  }

  Future<void> _checkAuthStatus() async {
    // Vérifier si l'utilisateur est connecté sans délai
    final bool isLoggedIn = await AuthService.isLoggedIn();

    if (mounted) {
      // Navigation directe sans animation de splash
      Navigator.of(context).pushReplacement(
        MaterialPageRoute(
          builder:
              (context) =>
                  isLoggedIn ? const HomeScreen() : const LoginScreen(),
        ),
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    // Écran de chargement minimal pendant la vérification
    return Scaffold(
      backgroundColor: Colors.white,
      body: const Center(child: CircularProgressIndicator()),
    );
  }
}
