import 'dart:convert';
import 'dart:async';

import 'package:callitris/config/api_config.dart';
import 'package:callitris/screens/boutique/boutique_screen.dart';
import 'package:callitris/screens/boutique/my_orders_screen.dart';
import 'package:callitris/screens/boutique/order_detail_screen.dart';
import 'package:callitris/screens/carnet/carnet_screen.dart';
import 'package:callitris/screens/catalogue/catalogue_screen.dart';
import 'package:callitris/screens/profile/profile_screen.dart';
import 'package:callitris/screens/promos/promo_screen.dart' as promo;
import 'package:callitris/screens/promos/pub_screen.dart';
import 'package:callitris/services/auth_service.dart';
import 'package:callitris/services/order_service.dart';
import 'package:callitris/utils/appTheme.dart';
import 'package:callitris/utils/image_utils.dart';
import 'package:callitris/widgets/loading_indicator.dart';
import 'package:callitris/widgets/navigation_menu_button.dart';
import 'package:flutter/material.dart';
import 'package:http/http.dart' as http;
import '../boutique/product_detail_screen.dart';

class HomeScreen extends StatefulWidget {
  final int initialIndex;

  const HomeScreen({super.key, this.initialIndex = 0});

  @override
  State<HomeScreen> createState() => _HomeScreenState();
}

class _HomeScreenState extends State<HomeScreen> with TickerProviderStateMixin {
  late int _currentIndex;
  late final PageController _pageController;

  // Données réelles pour les commandes en cours
  List<Map<String, dynamic>> _activeOrders = [];
  bool _isLoadingOrders = true;

  // Données réelles pour le carrousel
  List<Map<String, dynamic>> _promoProducts = [];
  bool _isLoadingProducts = true;

  // Contrôleurs pour l'auto-scroll des promotions
  ScrollController? _promoScrollController;
  Timer? _promoAutoScrollTimer;
  int _currentPromoIndex = 0;

  @override
  void initState() {
    super.initState();
    _currentIndex = widget.initialIndex;
    _pageController = PageController(initialPage: widget.initialIndex);
    _promoScrollController = ScrollController();
    _loadActiveOrders();
    _loadPromoProducts();
  }

  void _startPromoAutoScroll() {
    // Annuler le timer existant s'il y en a un
    _promoAutoScrollTimer?.cancel();

    _promoAutoScrollTimer = Timer.periodic(const Duration(seconds: 4), (timer) {
      if (_promoScrollController?.hasClients == true &&
          _promoProducts.isNotEmpty) {
        _currentPromoIndex = (_currentPromoIndex + 1) % _promoProducts.length;

        final double targetOffset =
            _currentPromoIndex * 216.0; // 200 (largeur) + 16 (margin)

        _promoScrollController?.animateTo(
          targetOffset,
          duration: const Duration(milliseconds: 1000),
          curve: Curves.easeInOut,
        );
      }
    });
  }

  // Charger les commandes actives depuis l'API
  Future<void> _loadActiveOrders() async {
    setState(() {
      _isLoadingOrders = true;
    });

    try {
      // Appel à l'API pour récupérer les commandes actives
      final result = await OrderService.getActiveOrders();

      print(
        'Résultat API commandes actives: $result',
      ); // Ajout de log pour déboguer

      if (result['success'] == true) {
        final List<Map<String, dynamic>> activeOrders =
            (result['activeOrders'] as List<dynamic>?)
                ?.map((item) => Map<String, dynamic>.from(item as Map))
                .toList() ??
            [];
        print('Nombre de commandes actives: $activeOrders');
        setState(() {
          _activeOrders = activeOrders;
          _isLoadingOrders = false;
        });
      } else {
        // En cas d'erreur, utiliser une liste vide (pas de données fictives)
        setState(() {
          _activeOrders = [];
          _isLoadingOrders = false;
        });
      }
    } catch (e) {
      print('Erreur lors du chargement des commandes: $e');
      // En cas d'exception, utiliser une liste vide (pas de données fictives)
      setState(() {
        _activeOrders = [];
        _isLoadingOrders = false;
      });
    }
  }

  // Charger les données publicitaires statiques
  Future<void> _loadPromoProducts() async {
    setState(() {
      _isLoadingProducts = true;
    });

    try {
      final token = await AuthService.getAuthToken();
      final String cat = "PUBLICITE";
      if (token == null) {
        print('Token manquant pour récupérer les publicités');
        setState(() {
          _promoProducts = [];
          _isLoadingProducts = false;
        });
        return;
      }

      print(
        'Chargement des publicités depuis: ${ApiConfig.baseUrl}/pubs/getPub.php?pub=$cat',
      );

      final response = await http
          .get(
            Uri.parse("${ApiConfig.baseUrl}/pubs/getPub.php?pub=$cat"),
            headers: {'Authorization': 'Bearer $token'},
          )
          .timeout(const Duration(seconds: 15));

      print('📡 Statut de la réponse: ${response.statusCode}');
      print('📄 Corps de la réponse: ${response.body}');

      if (response.statusCode == 200) {
        final responseBody = response.body.trim();

        if (responseBody.isEmpty) {
          print('⚠️ Réponse vide de l\'API');
          setState(() {
            _promoProducts = [];
            _isLoadingProducts = false;
          });
          return;
        }

        try {
          // Nettoyer le JSON avant le parsing pour éviter les problèmes d'encodage
          String cleanedJson = responseBody
              .replaceAll(r'\u00e9', 'é')
              .replaceAll(r'\u00e8', 'è')
              .replaceAll(r'\u00ea', 'ê')
              .replaceAll(r'\u00e0', 'à')
              .replaceAll(r'\u00f9', 'ù')
              .replaceAll(r'\u00e7', 'ç');

          print('📄 JSON nettoyé: $cleanedJson');

          final List<dynamic> responseData = jsonDecode(cleanedJson);
          print('📊 Nombre de publicités reçues: ${responseData.length}');

          if (responseData.isEmpty) {
            print('⚠️ Aucune publicité trouvée dans la réponse');
            setState(() {
              _promoProducts = [];
              _isLoadingProducts = false;
            });
            return;
          }

          final List<Map<String, dynamic>> staticAds =
              responseData
                  .map((item) {
                    print('🔍 Traitement de l\'item: $item');

                    // Vérifier que l'item contient les champs nécessaires
                    if (item == null || !item.containsKey('imageUrl')) {
                      print('⚠️ Item invalide ou sans imageUrl: $item');
                      return null;
                    }

                    String rawPath = item['imageUrl']?.toString() ?? '';
                    String cleanedPath = rawPath.replaceAll(r'\/', '/');

                    // Encoder correctement l'URL pour gérer les caractères spéciaux
                    String fullUrl;
                    try {
                      // Séparer le chemin et le nom de fichier
                      List<String> pathParts = cleanedPath.split('/');
                      if (pathParts.isNotEmpty) {
                        // Encoder seulement le nom de fichier (dernière partie)
                        String fileName = pathParts.last;
                        String encodedFileName = Uri.encodeComponent(fileName);
                        pathParts[pathParts.length - 1] = encodedFileName;
                        String encodedPath = pathParts.join('/');
                        fullUrl = "${ApiConfig.baseUrl2}/$encodedPath";
                      } else {
                        fullUrl = "${ApiConfig.baseUrl2}/$cleanedPath";
                      }
                    } catch (e) {
                      print('⚠️ Erreur d\'encodage URL: $e');
                      fullUrl = "${ApiConfig.baseUrl2}/$cleanedPath";
                    }

                    print('🖼️ URL image générée: $fullUrl');

                    return {
                      'id_publicite': item['id_publicite']?.toString() ?? '',
                      'name': item['name']?.toString() ?? 'Produit sans nom',
                      'description':
                          item['description']?.toString() ??
                          'Aucune description',
                      'price':
                          int.tryParse(item['price']?.toString() ?? '0') ?? 0,
                      'imageUrl': fullUrl,
                      'category': item['category']?.toString() ?? 'Général',
                      'isPromo': item['isPromo'] ?? true,
                    };
                  })
                  .where((item) => item != null)
                  .cast<Map<String, dynamic>>()
                  .toList();

          print('✅ ${staticAds.length} publicités traitées avec succès');

          setState(() {
            _promoProducts = staticAds;
            _isLoadingProducts = false;
          });

          // Démarrer l'auto-scroll après le chargement des données
          if (staticAds.isNotEmpty) {
            _startPromoAutoScroll();
          }
        } catch (jsonError) {
          print('❌ Erreur de parsing JSON: $jsonError');
          print('📄 Contenu reçu: $responseBody');
          setState(() {
            _promoProducts = [];
            _isLoadingProducts = false;
          });
        }
      } else {
        print(' Erreur HTTP: ${response.statusCode}');
        print(' Message d\'erreur: ${response.body}');
        setState(() {
          _promoProducts = [];
          _isLoadingProducts = false;
        });
      }
    } catch (e) {
      print(' Exception lors du chargement des produits: $e');
      setState(() {
        _promoProducts = [];
        _isLoadingProducts = false;
      });
    }
  }

  @override
  void dispose() {
    _pageController.dispose();
    _promoAutoScrollTimer?.cancel();
    _promoScrollController?.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: PageView(
        controller: _pageController,
        physics: const NeverScrollableScrollPhysics(),
        onPageChanged: (index) {
          setState(() {
            _currentIndex = index;
          });
        },
        children: [
          _buildHomeContent(),
          _buildCarnetScreen(),
          //const CatalogueScreen(),
          const BoutiqueScreen(),
          const ProfileScreen(),
        ],
      ),
      bottomNavigationBar: BottomNavigationBar(
        currentIndex: _currentIndex,
        onTap: (index) {
          setState(() {
            _currentIndex = index;
            _pageController.jumpToPage(index);
          });
        },
        type: BottomNavigationBarType.fixed,
        backgroundColor: Colors.white,
        selectedItemColor: AppTheme.color.primaryColor,
        unselectedItemColor: AppTheme.color.brunGris,
        selectedLabelStyle: const TextStyle(
          fontWeight: FontWeight.w600,
          fontSize: 12,
        ),
        unselectedLabelStyle: const TextStyle(
          fontWeight: FontWeight.w600,
          fontSize: 12,
        ),
        elevation: 8,
        items: const [
          BottomNavigationBarItem(
            icon: Icon(Icons.home_outlined),
            activeIcon: Icon(Icons.home),
            label: 'Accueil',
          ),
          BottomNavigationBarItem(
            icon: Icon(Icons.book_outlined),
            activeIcon: Icon(Icons.book),
            label: 'Carnet',
          ),
          BottomNavigationBarItem(
            icon: Icon(Icons.menu_book_outlined),
            activeIcon: Icon(Icons.menu_book),
            label: 'Catalogue',
          ),
          // BottomNavigationBarItem(
          //   icon: Icon(Icons.shopping_cart_outlined),
          //   activeIcon: Icon(Icons.shopping_cart),
          //   label: 'Boutique',
          // ),
          BottomNavigationBarItem(
            icon: Icon(Icons.person_outline),
            activeIcon: Icon(Icons.person),
            label: 'Profil',
          ),
        ],
      ),
    );
  }

  Widget _buildHomeContent() {
    return Scaffold(
      backgroundColor: Colors.grey.shade50,
      body: RefreshIndicator(
        onRefresh: () async {
          // Rafraîchir les données réelles
          await Future.wait([_loadActiveOrders(), _loadPromoProducts()]);
        },
        child: CustomScrollView(
          physics: const AlwaysScrollableScrollPhysics(),
          slivers: [
            // AppBar moderne avec gradient
            _buildModernAppBar(),

            // Carrousel publicitaire moderne (déplacé en haut)
            SliverToBoxAdapter(child: _buildModernPromoSection()),

            // Actions rapides redesignées
            SliverToBoxAdapter(child: _buildModernQuickActions()),

            // Commandes en cours redesignées
            SliverToBoxAdapter(child: _buildModernOrdersSection()),

            // Espace pour la bottom nav
            SliverToBoxAdapter(child: const SizedBox(height: 100)),
          ],
        ),
      ),
    );
  }

  // AppBar moderne avec gradient
  Widget _buildModernAppBar() {
    return SliverAppBar(
      expandedHeight: 140,
      floating: false,
      pinned: true,
      elevation: 0,
      backgroundColor: Colors.white,
      flexibleSpace: FlexibleSpaceBar(
        background: Container(
          decoration: BoxDecoration(
            gradient: LinearGradient(
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
              colors: [
                AppTheme.color.primaryColor,
                AppTheme.color.primaryColor.withOpacity(0.8),
              ],
            ),
          ),
          child: SafeArea(
            child: Padding(
              padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                mainAxisAlignment: MainAxisAlignment.end,
                children: [
                  Row(
                    children: [
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              'Bonjour !',
                              style: TextStyle(
                                fontSize: 16,
                                color: Colors.white.withOpacity(0.9),
                                fontWeight: FontWeight.normal,
                              ),
                            ),
                            const SizedBox(height: 4),
                            Text(
                              'Bienvenue sur votre espace cotisation',
                              style: TextStyle(
                                fontSize: 22,
                                color: Colors.white,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                          ],
                        ),
                      ),
                      Container(
                        padding: const EdgeInsets.all(8),
                        decoration: BoxDecoration(
                          color: Colors.white.withOpacity(0.2),
                          borderRadius: BorderRadius.circular(12),
                        ),
                        child: Icon(
                          Icons.waving_hand,
                          color: Colors.white,
                          size: 24,
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ),
        ),
      ),
      actions: [
        NavigationMenuButton(iconColor: const Color.fromARGB(255, 3, 0, 0)),
        IconButton(
          icon: Container(
            padding: const EdgeInsets.all(8),
            decoration: BoxDecoration(
              color: const Color.fromARGB(255, 0, 0, 0).withOpacity(0.2),
              borderRadius: BorderRadius.circular(10),
            ),
            child: const Icon(
              Icons.notifications_outlined,
              color: Color.fromARGB(255, 0, 0, 0),
              size: 20,
            ),
          ),
          onPressed: () {},
        ),
        IconButton(
          icon: Container(
            padding: const EdgeInsets.all(8),
            decoration: BoxDecoration(
              color: const Color.fromARGB(255, 0, 0, 0).withOpacity(0.2),
              borderRadius: BorderRadius.circular(10),
            ),
            child: const Icon(
              Icons.person_outline,
              color: Color.fromARGB(255, 0, 0, 0),
              size: 20,
            ),
          ),
          onPressed: () {
            setState(() {
              _currentIndex = 3; // Profil est maintenant à l'index 3
              _pageController.jumpToPage(3);
            });
          },
        ),
        const SizedBox(width: 8),
      ],
    );
  }

  // Actions rapides redesignées
  Widget _buildModernQuickActions() {
    return Container(
      margin: const EdgeInsets.fromLTRB(20, 20, 20, 0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                width: 4,
                height: 24,
                decoration: BoxDecoration(
                  color: AppTheme.color.primaryColor,
                  borderRadius: BorderRadius.circular(2),
                ),
              ),
              const SizedBox(width: 12),
              Text(
                'Actions rapides',
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                  color: AppTheme.color.textColor,
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          Row(
            children: [
              // Expanded(
              //   child: _buildQuickActionCard(
              //     icon: Icons.shopping_cart_outlined,
              //     title: 'Boutique',
              //     subtitle: 'Découvrir nos produits',
              //     color: Colors.blue,
              //     onTap: () {
              //       Navigator.push(
              //         context,
              //         MaterialPageRoute(
              //           builder: (context) => const BoutiqueScreen(),
              //         ),
              //       );
              //     },
              //   ),
              // ),
              // const SizedBox(width: 12),
              Expanded(
                child: _buildQuickActionCard(
                  icon: Icons.receipt_long_outlined,
                  title: 'Mes commandes',
                  subtitle: 'Suivre vos achats',
                  color: AppTheme.color.primaryColor,
                  onTap: () {
                    Navigator.push(
                      context,
                      MaterialPageRoute(
                        builder: (context) => const MyOrdersScreen(),
                      ),
                    );
                  },
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildQuickActionCard({
    required IconData icon,
    required String title,
    required String subtitle,
    required Color color,
    required VoidCallback onTap,
  }) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          gradient: LinearGradient(
            colors: [Colors.white, color.withValues(alpha: 0.05)],
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
          ),
          borderRadius: BorderRadius.circular(16),
          border: Border.all(color: color.withValues(alpha: 0.2), width: 1),
          boxShadow: [
            BoxShadow(
              color: color.withValues(alpha: 0.1),
              blurRadius: 8,
              offset: const Offset(0, 3),
            ),
          ],
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          mainAxisSize: MainAxisSize.min,
          children: [
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  colors: [color.withValues(alpha: 0.8), color],
                ),
                borderRadius: BorderRadius.circular(12),
                boxShadow: [
                  BoxShadow(
                    color: color.withValues(alpha: 0.3),
                    blurRadius: 4,
                    offset: const Offset(0, 2),
                  ),
                ],
              ),
              child: Icon(icon, color: Colors.white, size: 24),
            ),
            const SizedBox(height: 12),
            Text(
              title,
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.bold,
                color: AppTheme.color.textColor,
              ),
            ),
            const SizedBox(height: 4),
            Text(
              subtitle,
              style: TextStyle(fontSize: 12, color: Colors.grey.shade600),
              maxLines: 2,
              overflow: TextOverflow.ellipsis,
            ),
          ],
        ),
      ),
    );
  }

  // Section promo moderne
  Widget _buildModernPromoSection() {
    return Container(
      margin: const EdgeInsets.fromLTRB(20, 20, 20, 0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                width: 4,
                height: 24,
                decoration: BoxDecoration(
                  color: AppTheme.color.primaryColor,
                  borderRadius: BorderRadius.circular(2),
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Promotions du moment',
                      style: TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                        color: AppTheme.color.textColor,
                      ),
                    ),
                    Text(
                      'Découvrez nos offres spéciales',
                      style: TextStyle(
                        fontSize: 14,
                        color: Colors.grey.shade600,
                      ),
                    ),
                  ],
                ),
              ),
              Container(
                padding: const EdgeInsets.symmetric(
                  horizontal: 12,
                  vertical: 6,
                ),
                decoration: BoxDecoration(
                  color: Colors.red.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(20),
                ),
                child: Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Icon(
                      Icons.local_fire_department,
                      size: 16,
                      color: Colors.red,
                    ),
                    const SizedBox(width: 4),
                    Text(
                      'HOT',
                      style: TextStyle(
                        fontSize: 12,
                        fontWeight: FontWeight.bold,
                        color: Colors.red,
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          _isLoadingProducts
              ? const ProductCarouselSkeleton()
              : _promoProducts.isEmpty
              ? _buildEmptyPromoSection()
              : _buildModernPromoCarousel(),
        ],
      ),
    );
  }

  Widget _buildModernPromoCarousel() {
    // Initialiser le contrôleur si ce n'est pas déjà fait
    _promoScrollController ??= ScrollController();

    return SizedBox(
      height: 200,
      child: ListView.builder(
        controller:
            _promoScrollController, // Ajout du contrôleur pour l'auto-scroll
        scrollDirection: Axis.horizontal,
        padding: EdgeInsets.zero,
        itemCount: _promoProducts.length,
        itemBuilder: (context, index) {
          final product = _promoProducts[index];
          return Container(
            width: 200,
            margin: EdgeInsets.only(
              right: index == _promoProducts.length - 1 ? 0 : 16,
            ),
            child: _buildModernPromoCard(product),
          );
        },
      ),
    );
  }

  Widget _buildModernPromoCard(Map<String, dynamic> product) {
    return GestureDetector(
      onTap: () {
        Navigator.push(
          context,
          MaterialPageRoute(
            builder:
                (context) =>
                    product['category'] == "PROMO"
                        ? promo.InfoScreen(
                          infoId: product['id_publicite'].toString(),
                        )
                        : PubScreen(pubId: product['id_publicite'].toString()),
          ),
        );
      },
      child: Container(
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(20),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withOpacity(0.1),
              blurRadius: 15,
              offset: const Offset(0, 5),
            ),
          ],
        ),
        child: ClipRRect(
          borderRadius: BorderRadius.circular(20),
          child: Stack(
            children: [
              // Image de fond
              ImageUtils.networkImageWithErrorHandling(
                imageUrl:
                    product['imageUrl'] ?? 'assets/images/product_default.jpg',
                width: double.infinity,
                height: double.infinity,
                fit: BoxFit.cover,
                placeholderColor: Colors.grey.shade200,
                placeholderIcon: Icons.image_not_supported_rounded,
              ),

              // Overlay gradient
              Container(
                decoration: BoxDecoration(
                  gradient: LinearGradient(
                    begin: Alignment.topCenter,
                    end: Alignment.bottomCenter,
                    colors: [Colors.transparent, Colors.black.withOpacity(0.7)],
                  ),
                ),
              ),

              if (product['category'] == "PROMO") ...[
                // Badge promo
                Positioned(
                  top: 12,
                  right: 12,
                  child: Container(
                    padding: const EdgeInsets.symmetric(
                      horizontal: 8,
                      vertical: 4,
                    ),
                    decoration: BoxDecoration(
                      color: Colors.red,
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: Text(
                      'PROMO',
                      style: TextStyle(
                        color: Colors.white,
                        fontSize: 10,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                ),
              ],

              if (product['category'] == "PUBLICITE") ...[
                // Badge promo
                Positioned(
                  top: 12,
                  right: 12,
                  child: Container(
                    padding: const EdgeInsets.symmetric(
                      horizontal: 8,
                      vertical: 4,
                    ),
                    decoration: BoxDecoration(
                      color: Colors.green,
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: Text(
                      'PUBLICITE',
                      style: TextStyle(
                        color: Colors.white,
                        fontSize: 10,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                ),
              ],

              // Contenu
              Positioned(
                bottom: 16,
                left: 16,
                right: 16,
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      product['name'] ?? 'Produit sans nom',
                      style: TextStyle(
                        color: Colors.white,
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                      ),
                      maxLines: 2,
                      overflow: TextOverflow.ellipsis,
                    ),
                    const SizedBox(height: 4),
                    Text(
                      product['description'] ?? '',
                      style: TextStyle(
                        color: Colors.white.withOpacity(0.9),
                        fontSize: 12,
                      ),
                      maxLines: 1,
                      overflow: TextOverflow.ellipsis,
                    ),
                    const SizedBox(height: 8),
                    Row(
                      children: [
                        if (product['category'] == "PROMO") ...[
                          Text(
                            '${_formatPrice(product['price'] ?? 0)} %',
                            style: TextStyle(
                              color: Colors.white,
                              fontSize: 14,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ],
                        const Spacer(),
                        Container(
                          padding: const EdgeInsets.symmetric(
                            horizontal: 8,
                            vertical: 4,
                          ),
                          decoration: BoxDecoration(
                            color: const Color.fromARGB(
                              255,
                              236,
                              121,
                              26,
                            ).withOpacity(0.8),
                            borderRadius: BorderRadius.circular(12),
                          ),
                          child: Text(
                            'Voir plus',
                            style: TextStyle(
                              color: Colors.white,
                              fontSize: 10,
                              fontWeight: FontWeight.w600,
                            ),
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  // Section commandes modernes
  Widget _buildModernOrdersSection() {
    return Container(
      margin: const EdgeInsets.fromLTRB(20, 20, 20, 0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                width: 4,
                height: 24,
                decoration: BoxDecoration(
                  color: AppTheme.color.primaryColor,
                  borderRadius: BorderRadius.circular(2),
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Vos commandes en cours',
                      style: TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                        color: AppTheme.color.textColor,
                      ),
                    ),
                    Text(
                      'Suivez l\'état de vos achats',
                      style: TextStyle(
                        fontSize: 14,
                        color: Colors.grey.shade600,
                      ),
                    ),
                  ],
                ),
              ),
              if (_activeOrders.length > 5)
                TextButton(
                  onPressed: () {
                    Navigator.push(
                      context,
                      MaterialPageRoute(
                        builder: (context) => const MyOrdersScreen(),
                      ),
                    );
                  },
                  child: Text(
                    'Voir tout',
                    style: TextStyle(
                      color: AppTheme.color.primaryColor,
                      fontWeight: FontWeight.w900,
                    ),
                  ),
                ),
            ],
          ),
          const SizedBox(height: 16),
          _isLoadingOrders
              ? const OrderCardSkeleton()
              : _activeOrders.isEmpty
              ? _buildNoActiveOrdersSection()
              : Column(
                children:
                    _activeOrders
                        .take(5)
                        .map(
                          (order) => Container(
                            margin: const EdgeInsets.only(bottom: 12),
                            child: _buildModernOrderCard(order),
                          ),
                        )
                        .toList(),
              ),
        ],
      ),
    );
  }

  Widget _buildModernOrderCard(Map<String, dynamic> order) {
    // Extraire les données correctes de l'API
    final String productName =
        order['nom_kit'] ??
        order['livret'] ??
        order['nom_produit'] ??
        order['nom'] ??
        order['productName'] ??
        'Produit sans nom';

    final String rawImageUrl =
        order['photo_kit'] ??
        order['image_produit'] ??
        order['image'] ??
        order['photo'] ??
        order['imageUrl'] ??
        '';

    // Formater l'URL de l'image avec ImageUtils
    final String imageUrl = ImageUtils.formatImageUrl(rawImageUrl);

    final int duration = int.tryParse(order['jour']?.toString() ?? '0') ?? 0;
    final int paye = int.tryParse(order['paye']?.toString() ?? '0') ?? 0;
    final int reste = int.tryParse(order['reste']?.toString() ?? '0') ?? 0;
    final int journalier =
        int.tryParse(order['journalier']?.toString() ?? '0') ?? 0;

    // Calculer la progression
    final double progress =
        duration > 0 ? (paye / duration).clamp(0.0, 1.0) : 0.0;

    // Déterminer le statut basé sur les données réelles
    final String status = reste > 0 ? 'En cours' : 'Terminé';

    return GestureDetector(
      onTap: () {
        Navigator.push(
          context,
          MaterialPageRoute(
            builder:
                (context) =>
                    OrderDetailScreen(orderId: order['id']?.toString() ?? ''),
          ),
        );
      },
      child: Container(
        decoration: BoxDecoration(
          gradient: LinearGradient(
            colors: [Colors.white, Colors.orange.shade50],
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
          ),
          borderRadius: BorderRadius.circular(12),
          border: Border.all(color: Colors.orange.shade100, width: 1),
          boxShadow: [
            BoxShadow(
              color: Colors.orange.withValues(alpha: 0.08),
              blurRadius: 8,
              offset: const Offset(0, 3),
            ),
          ],
        ),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Image du produit en haut
              ClipRRect(
                borderRadius: BorderRadius.circular(8),
                child: AspectRatio(
                  aspectRatio: 16 / 9,
                  child:
                      order['photo_kit'] != null &&
                              order['photo_kit'].toString().isNotEmpty
                          ? Image.network(
                            ImageUtils.formatImageUrl(
                              order['photo_kit'].toString(),
                            ),
                            fit: BoxFit.cover,
                            errorBuilder: (context, error, stackTrace) {
                              return Container(
                                color: const Color(0xFFF5F6FA),
                                child: Center(
                                  child: Column(
                                    mainAxisAlignment: MainAxisAlignment.center,
                                    children: [
                                      Icon(
                                        Icons.shopping_bag,
                                        color: Colors.grey[400],
                                        size: 40,
                                      ),
                                      const SizedBox(height: 8),
                                      Text(
                                        'Callitris',
                                        style: TextStyle(
                                          color: Colors.grey[500],
                                          fontSize: 14,
                                          fontWeight: FontWeight.w500,
                                        ),
                                      ),
                                    ],
                                  ),
                                ),
                              );
                            },
                          )
                          : Container(
                            color: const Color(0xFFF5F6FA),
                            child: Center(
                              child: Column(
                                mainAxisAlignment: MainAxisAlignment.center,
                                children: [
                                  Icon(
                                    Icons.shopping_bag,
                                    color: Colors.grey[400],
                                    size: 40,
                                  ),
                                  const SizedBox(height: 8),
                                  Text(
                                    'Callitris',
                                    style: TextStyle(
                                      color: Colors.grey[500],
                                      fontSize: 14,
                                      fontWeight: FontWeight.w500,
                                    ),
                                  ),
                                ],
                              ),
                            ),
                          ),
                ),
              ),
              const SizedBox(height: 16),

              // En-tête avec icône et statut
              Row(
                children: [
                  Container(
                    width: 36,
                    height: 36,
                    decoration: BoxDecoration(
                      gradient: LinearGradient(
                        colors: [Colors.orange.shade200, Colors.amber.shade300],
                      ),
                      borderRadius: BorderRadius.circular(8),
                      border: Border.all(color: Colors.white, width: 1),
                    ),
                    child: const Icon(
                      Icons.shopping_bag_outlined,
                      color: Colors.white,
                      size: 24,
                    ),
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          productName,
                          style: const TextStyle(
                            fontSize: 15,
                            fontWeight: FontWeight.w600,
                            color: Colors.black87,
                          ),
                          maxLines: 1,
                          overflow: TextOverflow.ellipsis,
                        ),
                        Text(
                          'Nº ${order['id'] ?? 'N/A'} • ${order['pack'] ?? 'Pack'}',
                          style: TextStyle(
                            fontSize: 11,
                            color: Colors.grey[600],
                          ),
                        ),
                      ],
                    ),
                  ),
                  Container(
                    padding: const EdgeInsets.symmetric(
                      horizontal: 8,
                      vertical: 4,
                    ),
                    decoration: BoxDecoration(
                      color: _getStatusColorNew(
                        paye,
                        duration,
                        reste,
                      ).withValues(alpha: 0.1),
                      borderRadius: BorderRadius.circular(6),
                      border: Border.all(
                        color: _getStatusColorNew(
                          paye,
                          duration,
                          reste,
                        ).withValues(alpha: 0.3),
                        width: 1,
                      ),
                    ),
                    child: Text(
                      _getStatusTextNew(paye, duration, reste),
                      style: TextStyle(
                        fontSize: 13,
                        fontWeight: FontWeight.bold,
                        color: _getStatusColorNew(paye, duration, reste),
                      ),
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 16),

              // Section des statistiques
              Container(
                padding: const EdgeInsets.all(14),
                decoration: BoxDecoration(
                  gradient: LinearGradient(
                    colors: [Colors.orange.shade50, Colors.amber.shade50],
                  ),
                  borderRadius: BorderRadius.circular(10),
                  border: Border.all(color: Colors.orange.shade100),
                ),
                child: Column(
                  children: [
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        _buildInfoColumnHome(
                          'Journalier',
                          '${_formatPrice(journalier)} FCFA',
                        ),
                        _buildInfoColumnHome('Jours', duration.toString()),
                        _buildInfoColumnHome('Payés', paye.toString()),
                        _buildInfoColumnHome('Restants', reste.toString()),
                      ],
                    ),
                    const SizedBox(height: 14),

                    // Barre de progression
                    Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            Text(
                              'Progression',
                              style: TextStyle(
                                fontSize: 11,
                                fontWeight: FontWeight.w500,
                                color: Colors.orange.shade600,
                              ),
                            ),
                            Text(
                              '${(progress * 100).toInt()}%',
                              style: TextStyle(
                                fontSize: 11,
                                fontWeight: FontWeight.w600,
                                color: Colors.orange.shade700,
                              ),
                            ),
                          ],
                        ),
                        const SizedBox(height: 6),
                        Container(
                          height: 6,
                          decoration: BoxDecoration(
                            borderRadius: BorderRadius.circular(3),
                            color: Colors.grey.shade200,
                          ),
                          child: FractionallySizedBox(
                            alignment: Alignment.centerLeft,
                            widthFactor: progress > 0 ? progress : 0.01,
                            child: Container(
                              decoration: BoxDecoration(
                                borderRadius: BorderRadius.circular(3),
                                color: _getProgressColorNew(progress),
                              ),
                            ),
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
              const SizedBox(height: 14),

              // Bouton d'action
              SizedBox(
                width: double.infinity,
                child: Container(
                  decoration: BoxDecoration(
                    gradient: LinearGradient(
                      colors: [
                        const Color.fromARGB(255, 255, 255, 255),
                        const Color.fromARGB(255, 217, 221, 249),
                      ],
                    ),
                    borderRadius: BorderRadius.circular(10),
                    border: Border.all(
                      color: AppTheme.color.primaryColor,
                      width: 1,
                    ),
                  ),
                  child: Material(
                    color: Colors.transparent,
                    borderRadius: BorderRadius.circular(10),
                    child: InkWell(
                      onTap: () {
                        Navigator.push(
                          context,
                          MaterialPageRoute(
                            builder:
                                (context) => OrderDetailScreen(
                                  orderId: order['id'].toString(),
                                ),
                          ),
                        );
                      },
                      borderRadius: BorderRadius.circular(10),
                      child: Padding(
                        padding: const EdgeInsets.symmetric(
                          vertical: 12,
                          horizontal: 16,
                        ),
                        child: Row(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            Icon(
                              Icons.wallet,
                              size: 18,
                              color: AppTheme.color.primaryColor,
                            ),
                            const SizedBox(width: 8),
                            Text(
                              'Effectuer un versement'.toUpperCase(),
                              style: TextStyle(
                                fontSize: 14,
                                fontWeight: FontWeight.w600,
                                color: AppTheme.color.textColor,
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Color _getStatusColor(String? status) {
    switch (status?.toLowerCase()) {
      case 'terminé':
      case 'completed':
        return Colors.green;
      case 'en cours':
      case 'in_progress':
        return Colors.orange;
      case 'annulé':
      case 'cancelled':
        return Colors.red;
      default:
        return Colors.blue;
    }
  }

  // Méthodes utilitaires pour le nouveau design des cartes de commande
  Widget _buildInfoColumnHome(String label, String value) {
    return Column(
      children: [
        Text(
          label,
          style: TextStyle(
            fontSize: 12,
            color: Colors.grey[600],
            fontWeight: FontWeight.w500,
          ),
        ),
        const SizedBox(height: 4),
        Text(
          value,
          style: const TextStyle(
            fontSize: 14,
            fontWeight: FontWeight.bold,
            color: Colors.black87,
          ),
        ),
      ],
    );
  }

  Color _getStatusColorNew(int paye, int duration, int reste) {
    if (reste == 0) {
      return Colors.green.shade600;
    } else if (paye > duration / 2) {
      return Colors.orange.shade600;
    } else {
      return Colors.blue.shade600;
    }
  }

  String _getStatusTextNew(int paye, int duration, int reste) {
    if (reste == 0) {
      return 'Terminé';
    } else if (paye > duration / 2) {
      return 'Bientôt fini';
    } else {
      return 'En cours';
    }
  }

  Color _getProgressColorNew(double progress) {
    double percentage = progress * 100;
    if (percentage >= 100) {
      return Colors.green.shade400;
    } else if (percentage >= 34) {
      return Colors.orange.shade400;
    } else {
      return Colors.red.shade400;
    }
  }

  Widget _buildEmptyPromoSection() {
    return Container(
      height: 200,
      width: double.infinity,
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 10,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.campaign_outlined, size: 48, color: Colors.grey[400]),
            const SizedBox(height: 16),
            Text(
              'Aucune publicité disponible',
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.bold,
                color: AppTheme.color.brunGris,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              'Vérifiez votre connexion internet\nou consultez les logs pour plus d\'infos',
              textAlign: TextAlign.center,
              style: TextStyle(fontSize: 12, color: Colors.grey[600]),
            ),
            const SizedBox(height: 16),
            ElevatedButton.icon(
              onPressed: () {
                _loadPromoProducts();
              },
              icon: const Icon(Icons.refresh, size: 16),
              label: const Text('Réessayer'),
              style: ElevatedButton.styleFrom(
                backgroundColor: AppTheme.color.primaryColor,
                foregroundColor: Colors.white,
                padding: const EdgeInsets.symmetric(
                  horizontal: 16,
                  vertical: 8,
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildPromoCarousel() {
    return _AdvertisementCarousel(advertisements: _promoProducts);
  }

  // Widget pour afficher un message quand il n'y a pas de commandes actives
  Widget _buildNoActiveOrdersSection() {
    return Container(
      padding: const EdgeInsets.all(24),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 10,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Column(
        children: [
          Container(
            width: 80,
            height: 80,
            decoration: BoxDecoration(
              color: AppTheme.color.primaryColor.withOpacity(0.1),
              shape: BoxShape.circle,
            ),
            child: Icon(
              Icons.shopping_bag_outlined,
              size: 36,
              color: AppTheme.color.primaryColor,
            ),
          ),
          const SizedBox(height: 16),
          const Text(
            'Vous n\'avez pas de commande en cours',
            style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 8),
          Text(
            'Explorez notre catalogue pour trouver des produits qui vous intéressent',
            style: TextStyle(fontSize: 14, color: AppTheme.color.brunGris),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 24),
          ElevatedButton(
            onPressed: () {
              Navigator.push(
                context,
                MaterialPageRoute(builder: (context) => const BoutiqueScreen()),
              );
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: AppTheme.color.primaryColor,
              foregroundColor: Colors.white,
              padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(8),
              ),
            ),
            child: const Text(
              'EXPLORER LA BOUTIQUE',
              style: TextStyle(fontWeight: FontWeight.bold),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildCarnetScreen() {
    return CarnetScreen();
  }

  Widget _buildOrderCard(Map<String, dynamic> order) {
    // Extraire les données de la commande avec des valeurs par défaut
    final String id =
        order['id']?.toString() ?? order['id_commande']?.toString() ?? '';
    final String productName =
        order['nom_kit'] ??
        order['livret'] ??
        order['productName'] ??
        'Produit sans nom';
    final int totalPrice =
        int.tryParse(
          order['montant_total_kit']?.toString() ??
              order['totalPrice']?.toString() ??
              '0',
        ) ??
        0;
    final int dailyPrice =
        int.tryParse(
          order['cout_journalier_kit']?.toString() ??
              order['journalier']?.toString() ??
              order['dailyPrice']?.toString() ??
              '0',
        ) ??
        0;
    final int duration =
        int.tryParse(
          order['jour']?.toString() ?? order['duration']?.toString() ?? '0',
        ) ??
        0;
    final int paye =
        int.tryParse(
          order['paye']?.toString() ??
              order['versementsEffectues']?.toString() ??
              '0',
        ) ??
        0;
    final int reste =
        int.tryParse(
          order['reste']?.toString() ??
              order['versementsRestants']?.toString() ??
              '0',
        ) ??
        0;

    // Calculer les montants versés et restants
    final int montantVerse = paye * dailyPrice;
    final int montantRestant = reste * dailyPrice;

    // Calculer la progression
    final double progress =
        duration > 0 ? (paye / duration).clamp(0.0, 1.0) : 0.0;

    // Formater l'URL de l'image
    String imageUrl =
        order['photo_kit'] ??
        order['imageUrl'] ??
        'assets/images/placeholder.jpg';
    if (!imageUrl.startsWith('assets/') && !imageUrl.startsWith('http')) {
      imageUrl = ImageUtils.formatImageUrl(imageUrl);
    }

    // Déterminer la date du prochain paiement (aujourd'hui par défaut)
    final String nextPaymentDate =
        order['nextPaymentDate'] ??
        DateTime.now()
            .add(const Duration(days: 1))
            .toString()
            .substring(0, 10)
            .split('-')
            .reversed
            .join('/');

    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 10,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Image et informations
          Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              ClipRRect(
                borderRadius: const BorderRadius.only(
                  topLeft: Radius.circular(16),
                ),
                child:
                    imageUrl.startsWith('assets/')
                        ? Image.asset(
                          imageUrl,
                          width: 100,
                          height: 100,
                          fit: BoxFit.cover,
                          errorBuilder: (context, error, stackTrace) {
                            return Container(
                              width: 100,
                              height: 100,
                              color: Colors.grey[200],
                              child: Icon(
                                Icons.image_not_supported,
                                color: Colors.grey[400],
                              ),
                            );
                          },
                        )
                        : Image.network(
                          imageUrl,
                          width: 100,
                          height: 100,
                          fit: BoxFit.cover,
                          errorBuilder: (context, error, stackTrace) {
                            print(
                              'Erreur de chargement d\'image: $imageUrl - $error',
                            );
                            return Container(
                              width: 100,
                              height: 100,
                              color: Colors.grey[200],
                              child: Icon(
                                Icons.image_not_supported,
                                color: Colors.grey[400],
                              ),
                            );
                          },
                        ),
              ),
              Expanded(
                child: Padding(
                  padding: const EdgeInsets.all(16),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        productName,
                        style: const TextStyle(
                          fontWeight: FontWeight.bold,
                          fontSize: 16,
                        ),
                        maxLines: 2,
                        overflow: TextOverflow.ellipsis,
                      ),
                      const SizedBox(height: 8),
                      Text(
                        'Total: ${_formatPrice(totalPrice)} FCFA',
                        style: TextStyle(
                          color: AppTheme.color.primaryColor,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      const SizedBox(height: 4),
                      Text(
                        '$paye/$duration jours payés',
                        style: TextStyle(
                          color: AppTheme.color.brunGris,
                          fontSize: 14,
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ],
          ),

          // Barre de progression
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                const SizedBox(height: 8),
                LinearProgressIndicator(
                  value: progress,
                  backgroundColor: Colors.grey[200],
                  valueColor: AlwaysStoppedAnimation<Color>(
                    AppTheme.color.primaryColor,
                  ),
                  minHeight: 8,
                  borderRadius: BorderRadius.circular(4),
                ),
                const SizedBox(height: 8),
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Text(
                      'Versé: ${_formatPrice(montantVerse)} FCFA',
                      style: const TextStyle(fontSize: 12, color: Colors.grey),
                    ),
                    Text(
                      'Restant: ${_formatPrice(montantRestant)} FCFA',
                      style: const TextStyle(fontSize: 12, color: Colors.grey),
                    ),
                  ],
                ),
                const SizedBox(height: 16),
              ],
            ),
          ),

          // Bouton de paiement
          Container(
            width: double.infinity,
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
            decoration: BoxDecoration(
              color: Colors.grey[50],
              borderRadius: const BorderRadius.only(
                bottomLeft: Radius.circular(16),
                bottomRight: Radius.circular(16),
              ),
            ),
            child: Row(
              children: [
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'Prochain paiement',
                        style: TextStyle(
                          color: AppTheme.color.brunGris,
                          fontSize: 12,
                        ),
                      ),
                      const SizedBox(height: 2),
                      Text(
                        nextPaymentDate,
                        style: const TextStyle(fontWeight: FontWeight.bold),
                      ),
                    ],
                  ),
                ),
                ElevatedButton(
                  onPressed: () {
                    // Navigation vers l'écran de paiement
                    Navigator.push(
                      context,
                      MaterialPageRoute(
                        builder: (context) => OrderDetailScreen(orderId: id),
                      ),
                    ).then((_) {
                      // Recharger les commandes au retour
                      _loadActiveOrders();
                    });
                  },
                  style: ElevatedButton.styleFrom(
                    backgroundColor: AppTheme.color.primaryColor,
                    foregroundColor: Colors.white,
                    padding: const EdgeInsets.symmetric(
                      horizontal: 24,
                      vertical: 12,
                    ),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(8),
                    ),
                  ),
                  child: const Text(
                    'PAYER',
                    style: TextStyle(fontWeight: FontWeight.bold),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildQuickActions() {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            'Actions rapides',
            style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
          ),
          const SizedBox(height: 16),
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(16),
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withOpacity(0.05),
                  blurRadius: 10,
                  offset: const Offset(0, 4),
                ),
              ],
            ),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceEvenly,
              children: [
                _buildActionButton(
                  icon: Icons.shopping_basket_outlined,
                  label: 'Commander',
                  color: AppTheme.color.primaryColor,
                  onTap: () {
                    Navigator.push(
                      context,
                      MaterialPageRoute(
                        builder: (context) => const MyOrdersScreen(),
                      ),
                    );
                  },
                ),
                _buildActionButton(
                  icon: Icons.menu_book,
                  label: 'Catalogue',
                  color: AppTheme.color.orangeColor,
                  onTap: () {
                    Navigator.push(
                      context,
                      MaterialPageRoute(
                        builder: (context) => const BoutiqueScreen(),
                      ),
                    );
                  },
                ),
                _buildActionButton(
                  icon: Icons.book,
                  label: 'Carnet',
                  color: AppTheme.color.greenColor,
                  onTap: () {
                    Navigator.push(
                      context,
                      MaterialPageRoute(
                        builder: (context) => const CarnetScreen(),
                      ),
                    );
                  },
                ),
                _buildActionButton(
                  icon: Icons.shopping_cart_outlined,
                  label: 'Acheter',
                  color: AppTheme.color.secondaryColor,
                  onTap: () {
                    Navigator.push(
                      context,
                      MaterialPageRoute(
                        builder: (context) => const CatalogueScreen(),
                      ),
                    );
                  },
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildActionButton({
    required IconData icon,
    required String label,
    required Color color,
    required VoidCallback onTap,
  }) {
    return GestureDetector(
      onTap: onTap,
      child: Column(
        children: [
          Container(
            width: 50,
            height: 50,
            decoration: BoxDecoration(
              color: color.withOpacity(0.1),
              shape: BoxShape.circle,
            ),
            child: Icon(icon, color: color, size: 24),
          ),
          const SizedBox(height: 8),
          Text(
            label,
            style: TextStyle(
              fontSize: 12,
              fontWeight: FontWeight.w500,
              color: AppTheme.color.textColor,
            ),
          ),
        ],
      ),
    );
  }

  void _showCatalogueSelectionDialog(BuildContext context) {
    // Afficher le dialogue de sélection du catalogue
  }

  void _showCarnetSelectionDialog(BuildContext context) {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (BuildContext context) {
        return AlertDialog(
          title: Text(
            'Sélection du carnet',
            style: TextStyle(
              color: AppTheme.color.primaryColor,
              fontWeight: FontWeight.bold,
            ),
          ),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Text(
                'Choisissez la durée de votre carnet',
                style: TextStyle(color: AppTheme.color.brunGris),
              ),
              const SizedBox(height: 16),
              ListTile(
                leading: Icon(
                  Icons.calendar_month,
                  color: AppTheme.color.primaryColor,
                ),
                title: const Text('Carnet 1 mois'),
                subtitle: const Text('Idéal pour les petits projets'),
                onTap: () {
                  Navigator.pop(context);
                  _showCarnetUnavailableDialog(context, 1);
                },
              ),
              ListTile(
                leading: Icon(
                  Icons.calendar_month,
                  color: AppTheme.color.orangeColor,
                ),
                title: const Text('Carnet 3 mois'),
                subtitle: const Text('Pour les projets à moyen terme'),
                onTap: () {
                  Navigator.pop(context);
                  _showCarnetUnavailableDialog(context, 3);
                },
              ),
              ListTile(
                leading: Icon(
                  Icons.calendar_month,
                  color: AppTheme.color.greenColor,
                ),
                title: const Text('Carnet 6 mois'),
                subtitle: const Text('Pour les projets à long terme'),
                onTap: () {
                  Navigator.pop(context);
                  _showAvailableCarnetsDialog(context);
                },
              ),
              ListTile(
                leading: Icon(
                  Icons.calendar_month,
                  color: AppTheme.color.secondaryColor,
                ),
                title: const Text('Carnet 12 mois'),
                subtitle: const Text('Pour les grands projets'),
                onTap: () {
                  Navigator.pop(context);
                  _showCarnetUnavailableDialog(context, 12);
                },
              ),
            ],
          ),
          actions: [
            TextButton(
              onPressed: () {
                Navigator.pop(context);
              },
              child: Text(
                'Annuler',
                style: TextStyle(color: AppTheme.color.brunGris),
              ),
            ),
          ],
        );
      },
    );
  }

  void _showCarnetUnavailableDialog(BuildContext context, int months) {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (BuildContext context) {
        return AlertDialog(
          title: const Text(
            'Carnet non disponible',
            style: TextStyle(color: Colors.red, fontWeight: FontWeight.bold),
          ),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Icon(Icons.error_outline, color: Colors.red, size: 48),
              const SizedBox(height: 16),
              Text(
                'Désolé, le carnet pour une durée de $months mois n\'est pas disponible actuellement.',
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 8),
              const Text(
                'Veuillez essayer une autre durée ou réessayer plus tard.',
                textAlign: TextAlign.center,
                style: TextStyle(color: Colors.grey, fontSize: 14),
              ),
            ],
          ),
          actions: [
            TextButton(
              onPressed: () {
                Navigator.pop(context);
                _showCarnetSelectionDialog(context);
              },
              child: const Text('Choisir une autre durée'),
            ),
            ElevatedButton(
              onPressed: () {
                Navigator.pop(context);
              },
              style: ElevatedButton.styleFrom(
                backgroundColor: AppTheme.color.primaryColor,
              ),
              child: const Text('OK'),
            ),
          ],
        );
      },
    );
  }

  void _showAvailableCarnetsDialog(BuildContext context) {
    // Liste fictive des carnets disponibles pour 6 mois
    final List<Map<String, dynamic>> availableCarnets = [
      {
        'id': '1',
        'name': 'Carnet Standard',
        'description': 'Pour les achats du quotidien',
        'montant': 150000,
        'color': AppTheme.color.primaryColor,
      },
      {
        'id': '2',
        'name': 'Carnet Premium',
        'description': 'Pour les achats de luxe',
        'montant': 300000,
        'color': AppTheme.color.orangeColor,
      },
      {
        'id': '3',
        'name': 'Carnet Famille',
        'description': 'Pour les achats familiaux',
        'montant': 250000,
        'color': AppTheme.color.greenColor,
      },
    ];

    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (BuildContext context) {
        return AlertDialog(
          title: Text(
            'Carnets disponibles (6 mois)',
            style: TextStyle(
              color: AppTheme.color.primaryColor,
              fontWeight: FontWeight.bold,
            ),
          ),
          content: SizedBox(
            width: double.maxFinite,
            child: ListView.builder(
              shrinkWrap: true,
              itemCount: availableCarnets.length,
              itemBuilder: (context, index) {
                final carnet = availableCarnets[index];
                return Card(
                  margin: const EdgeInsets.only(bottom: 12),
                  elevation: 2,
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: ListTile(
                    contentPadding: const EdgeInsets.all(16),
                    leading: CircleAvatar(
                      backgroundColor: carnet['color'],
                      child: const Icon(Icons.book, color: Colors.white),
                    ),
                    title: Text(
                      carnet['name'],
                      style: const TextStyle(fontWeight: FontWeight.bold),
                    ),
                    subtitle: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        const SizedBox(height: 4),
                        Text(carnet['description']),
                        const SizedBox(height: 8),
                        Text(
                          'Montant: ${_formatPrice(carnet['montant'])} FCFA',
                          style: TextStyle(
                            color: AppTheme.color.primaryColor,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ],
                    ),
                    trailing: ElevatedButton(
                      onPressed: () {
                        Navigator.pop(context);
                        _showCarnetConfirmationDialog(context, carnet);
                      },
                      style: ElevatedButton.styleFrom(
                        backgroundColor: carnet['color'],
                        foregroundColor: Colors.white,
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(8),
                        ),
                      ),
                      child: const Text('Choisir'),
                    ),
                    isThreeLine: true,
                  ),
                );
              },
            ),
          ),
          actions: [
            TextButton(
              onPressed: () {
                Navigator.pop(context);
                _showCarnetSelectionDialog(context);
              },
              child: const Text('Retour'),
            ),
          ],
        );
      },
    );
  }

  void _showCarnetConfirmationDialog(
    BuildContext context,
    Map<String, dynamic> carnet,
  ) {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (BuildContext context) {
        return AlertDialog(
          title: Text(
            'Confirmation',
            style: TextStyle(
              color: AppTheme.color.primaryColor,
              fontWeight: FontWeight.bold,
            ),
          ),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Icon(
                Icons.check_circle,
                color: AppTheme.color.greenColor,
                size: 48,
              ),
              const SizedBox(height: 16),
              Text(
                'Vous avez choisi le ${carnet['name']} pour 6 mois',
                textAlign: TextAlign.center,
                style: const TextStyle(fontWeight: FontWeight.bold),
              ),
              const SizedBox(height: 8),
              Text(
                'Montant total: ${_formatPrice(carnet['montant'])} FCFA',
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 16),
              const Text(
                'Votre carnet sera activé après confirmation',
                textAlign: TextAlign.center,
                style: TextStyle(color: Colors.grey, fontSize: 14),
              ),
            ],
          ),
          actions: [
            TextButton(
              onPressed: () {
                Navigator.pop(context);
                _showAvailableCarnetsDialog(context);
              },
              child: const Text('Annuler'),
            ),
            ElevatedButton(
              onPressed: () {
                Navigator.pop(context);
                ScaffoldMessenger.of(context).showSnackBar(
                  SnackBar(
                    content: const Text('Carnet activé avec succès!'),
                    backgroundColor: AppTheme.color.greenColor,
                    duration: const Duration(seconds: 3),
                  ),
                );
              },
              style: ElevatedButton.styleFrom(
                backgroundColor: AppTheme.color.primaryColor,
              ),
              child: const Text('Confirmer'),
            ),
          ],
        );
      },
    );
  }

  // Formater le prix avec des espaces comme séparateurs de milliers
  String _formatPrice(dynamic price) {
    // Convertir en entier si nécessaire
    final int priceInt =
        price is int ? price : int.tryParse(price.toString()) ?? 0;
    final String priceString = priceInt.toString();
    final StringBuffer result = StringBuffer();

    for (int i = 0; i < priceString.length; i++) {
      if (i > 0 && (priceString.length - i) % 3 == 0) {
        result.write(' ');
      }
      result.write(priceString[i]);
    }

    return result.toString();
  }
}

/// Widget carrousel publicitaire avec élément central mis en avant
class _AdvertisementCarousel extends StatefulWidget {
  final List<Map<String, dynamic>> advertisements;

  const _AdvertisementCarousel({required this.advertisements});

  @override
  State<_AdvertisementCarousel> createState() => _AdvertisementCarouselState();
}

class _AdvertisementCarouselState extends State<_AdvertisementCarousel> {
  late PageController _pageController;
  int _currentPage = 0;

  @override
  void initState() {
    super.initState();
    _pageController = PageController(
      viewportFraction: 0.8, // Permet de voir les éléments adjacents
      initialPage: 0,
    );

    _pageController.addListener(() {
      int next = _pageController.page!.round();
      if (_currentPage != next) {
        setState(() {
          _currentPage = next;
        });
      }
    });
  }

  @override
  void dispose() {
    _pageController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        SizedBox(
          height: 280, // Hauteur augmentée pour l'élément central plus grand
          child: PageView.builder(
            controller: _pageController,
            itemCount: widget.advertisements.length,
            itemBuilder: (context, index) {
              return _buildAdvertisementCard(index);
            },
          ),
        ),
        const SizedBox(height: 16),
        // Indicateurs de page
        Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: List.generate(
            widget.advertisements.length,
            (index) => _buildPageIndicator(index),
          ),
        ),
      ],
    );
  }

  Widget _buildAdvertisementCard(int index) {
    final advertisement = widget.advertisements[index];
    final bool isActive = index == _currentPage;

    // Utiliser directement l'URL de l'image des données statiques
    String imageUrl =
        advertisement['imageUrl'] ?? 'assets/images/placeholder.jpg';

    return AnimatedContainer(
      duration: const Duration(milliseconds: 300),
      curve: Curves.easeOutQuint,
      margin: EdgeInsets.symmetric(
        horizontal: 8,
        vertical: isActive ? 0 : 20, // L'élément actif est plus grand
      ),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(20),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(isActive ? 0.15 : 0.08),
            blurRadius: isActive ? 15 : 8,
            offset: Offset(0, isActive ? 8 : 4),
          ),
        ],
      ),
      child: GestureDetector(
        onTap: () {
          // Navigation vers le détail du produit
          Navigator.push(
            context,
            MaterialPageRoute(
              builder: (context) => ProductDetailScreen(product: advertisement),
            ),
          );
        },
        child: ClipRRect(
          borderRadius: BorderRadius.circular(20),
          child: Stack(
            fit: StackFit.expand,
            children: [
              // Image de fond
              imageUrl.startsWith('assets/')
                  ? Image.asset(
                    imageUrl,
                    fit: BoxFit.cover,
                    errorBuilder: (context, error, stackTrace) {
                      return Container(
                        color: Colors.grey[200],
                        child: Icon(
                          Icons.image_not_supported,
                          color: Colors.grey[400],
                          size: 48,
                        ),
                      );
                    },
                  )
                  : Image.network(
                    imageUrl,
                    fit: BoxFit.cover,
                    errorBuilder: (context, error, stackTrace) {
                      return Container(
                        color: Colors.grey[200],
                        child: Icon(
                          Icons.image_not_supported,
                          color: Colors.grey[400],
                          size: 48,
                        ),
                      );
                    },
                  ),

              // Overlay dégradé
              Container(
                decoration: BoxDecoration(
                  gradient: LinearGradient(
                    begin: Alignment.topCenter,
                    end: Alignment.bottomCenter,
                    colors: [Colors.transparent, Colors.black.withOpacity(0.8)],
                    stops: const [0.5, 1.0],
                  ),
                ),
              ),

              // Badge "PROMO" pour l'élément actif
              if (isActive)
                Positioned(
                  top: 16,
                  right: 16,
                  child: Container(
                    padding: const EdgeInsets.symmetric(
                      horizontal: 12,
                      vertical: 6,
                    ),
                    decoration: BoxDecoration(
                      color: Colors.red,
                      borderRadius: BorderRadius.circular(20),
                      boxShadow: [
                        BoxShadow(
                          color: Colors.red.withOpacity(0.3),
                          blurRadius: 8,
                          offset: const Offset(0, 2),
                        ),
                      ],
                    ),
                    child: const Text(
                      'PROMO',
                      style: TextStyle(
                        color: Colors.white,
                        fontSize: 12,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                ),

              // Contenu textuel
              Positioned(
                bottom: 0,
                left: 0,
                right: 0,
                child: Container(
                  padding: EdgeInsets.all(isActive ? 20 : 16),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Text(
                        advertisement['name'] ?? 'Publicité',
                        style: TextStyle(
                          color: Colors.white,
                          fontSize: isActive ? 20 : 16,
                          fontWeight: FontWeight.bold,
                          shadows: const [
                            Shadow(
                              offset: Offset(0, 2),
                              blurRadius: 4,
                              color: Colors.black,
                            ),
                          ],
                        ),
                        maxLines: 2,
                        overflow: TextOverflow.ellipsis,
                      ),
                      SizedBox(height: isActive ? 8 : 6),
                      if (isActive && advertisement['description'] != null)
                        Text(
                          advertisement['description'],
                          style: const TextStyle(
                            color: Colors.white70,
                            fontSize: 14,
                            shadows: [
                              Shadow(
                                offset: Offset(0, 1),
                                blurRadius: 2,
                                color: Colors.black,
                              ),
                            ],
                          ),
                          maxLines: 2,
                          overflow: TextOverflow.ellipsis,
                        ),
                      SizedBox(height: isActive ? 8 : 6),
                      Text(
                        'À partir de ${_formatPrice(advertisement['price'] ?? 0)} FCFA',
                        style: TextStyle(
                          color: Colors.white,
                          fontSize: isActive ? 16 : 14,
                          fontWeight: FontWeight.w600,
                          shadows: const [
                            Shadow(
                              offset: Offset(0, 1),
                              blurRadius: 2,
                              color: Colors.black,
                            ),
                          ],
                        ),
                      ),
                      if (isActive) ...[
                        const SizedBox(height: 12),
                        Container(
                          padding: const EdgeInsets.symmetric(
                            horizontal: 16,
                            vertical: 8,
                          ),
                          decoration: BoxDecoration(
                            color: AppTheme.color.primaryColor,
                            borderRadius: BorderRadius.circular(25),
                            boxShadow: [
                              BoxShadow(
                                color: AppTheme.color.primaryColor.withOpacity(
                                  0.3,
                                ),
                                blurRadius: 8,
                                offset: const Offset(0, 2),
                              ),
                            ],
                          ),
                          child: const Text(
                            'DÉCOUVRIR',
                            style: TextStyle(
                              color: Colors.white,
                              fontSize: 12,
                              fontWeight: FontWeight.bold,
                              letterSpacing: 1,
                            ),
                          ),
                        ),
                      ],
                    ],
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildPageIndicator(int index) {
    final bool isActive = index == _currentPage;

    return AnimatedContainer(
      duration: const Duration(milliseconds: 300),
      margin: const EdgeInsets.symmetric(horizontal: 4),
      width: isActive ? 24 : 8,
      height: 8,
      decoration: BoxDecoration(
        color: isActive ? AppTheme.color.primaryColor : Colors.grey[300],
        borderRadius: BorderRadius.circular(4),
      ),
    );
  }

  // Formater le prix avec des espaces comme séparateurs de milliers
  String _formatPrice(dynamic price) {
    final int priceInt =
        price is int ? price : int.tryParse(price.toString()) ?? 0;
    final String priceString = priceInt.toString();
    final StringBuffer result = StringBuffer();

    for (int i = 0; i < priceString.length; i++) {
      if (i > 0 && (priceString.length - i) % 3 == 0) {
        result.write(' ');
      }
      result.write(priceString[i]);
    }

    return result.toString();
  }
}
