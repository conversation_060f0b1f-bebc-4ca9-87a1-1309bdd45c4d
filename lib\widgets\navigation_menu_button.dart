import 'package:callitris/screens/home/<USER>';
import 'package:callitris/utils/appTheme.dart';
import 'package:flutter/material.dart';

class NavigationMenuButton extends StatelessWidget {
  final Color? iconColor;
  final bool showLabel;

  const NavigationMenuButton({
    super.key,
    this.iconColor,
    this.showLabel = false,
  });

  @override
  Widget build(BuildContext context) {
    return PopupMenuButton<String>(
      icon: Icon(
        Icons.menu,
        color: iconColor ?? AppTheme.color.textColor,
      ),
      tooltip: 'Menu de navigation',
      offset: const Offset(0, 50),
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      elevation: 8,
      color: Colors.white,
      itemBuilder: (BuildContext context) => [
        _buildMenuItem(
          value: 'home',
          icon: Icons.home,
          title: 'Accueil',
          subtitle: '',
        ),
        _buildMenuItem(
          value: 'carnet',
          icon: Icons.book,
          title: 'Carnet',
          subtitle: '',
        ),
        _buildMenuItem(
          value: 'boutique',
          icon: Icons.menu_book,
          title: 'Catalogue',
          subtitle: '',
        ),
        _buildMenuItem(
          value: 'profile',
          icon: Icons.person,
          title: 'Profil',
          subtitle: '',
        ),
      ],
      onSelected: (String value) {
        _navigateToPage(context, value);
      },
    );
  }

  PopupMenuItem<String> _buildMenuItem({
    required String value,
    required IconData icon,
    required String title,
    required String subtitle,
  }) {
    return PopupMenuItem<String>(
      value: value,
      child: Container(
        padding: const EdgeInsets.symmetric(vertical: 8, horizontal: 4),
        child: Row(
          children: [
            Container(
              padding: const EdgeInsets.all(8),
              decoration: BoxDecoration(
                color: AppTheme.color.primaryColor.withOpacity(0.1),
                borderRadius: BorderRadius.circular(8),
              ),
              child: Icon(
                icon,
                color: AppTheme.color.primaryColor,
                size: 20,
              ),
            ),
            const SizedBox(width: 12),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                mainAxisSize: MainAxisSize.min,
                children: [
                  Text(
                    title,
                    style: TextStyle(
                      fontSize: 14,
                      fontWeight: FontWeight.w600,
                      color: AppTheme.color.textColor,
                    ),
                  ),
                  const SizedBox(height: 2),
                  Text(
                    subtitle,
                    style: TextStyle(
                      fontSize: 12,
                      color: AppTheme.color.brunGris,
                    ),
                  ),
                ],
              ),
            ),
            Icon(
              Icons.arrow_forward_ios,
              size: 14,
              color: AppTheme.color.brunGris,
            ),
          ],
        ),
      ),
    );
  }

  void _navigateToPage(BuildContext context, String value) {
    // Fermer le menu d'abord
    Navigator.pop(context);

    // Naviguer vers la page appropriée
    switch (value) {
      case 'home':
        Navigator.pushAndRemoveUntil(
          context,
          MaterialPageRoute(builder: (context) => const HomeScreen()),
          (route) => false,
        );
        break;
      case 'carnet':
        Navigator.pushAndRemoveUntil(
          context,
          MaterialPageRoute(builder: (context) => const HomeScreen(initialIndex: 1)),
          (route) => false,
        );
        break;
      case 'boutique':
        Navigator.pushAndRemoveUntil(
          context,
          MaterialPageRoute(builder: (context) => const HomeScreen(initialIndex: 2)),
          (route) => false,
        );
        break;
      case 'profile':
        Navigator.pushAndRemoveUntil(
          context,
          MaterialPageRoute(builder: (context) => const HomeScreen(initialIndex: 3)),
          (route) => false,
        );
        break;
    }
  }
}
