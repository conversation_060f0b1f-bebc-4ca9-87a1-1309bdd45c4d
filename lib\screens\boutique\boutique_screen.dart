import 'package:callitris/config/api_config.dart';
import 'package:callitris/screens/boutique/catalogue_packs_screen.dart';
import 'package:callitris/services/catalogue_service.dart';
import 'package:callitris/utils/appTheme.dart';
import 'package:callitris/widgets/navigation_menu_button.dart';
import 'package:flutter/material.dart';

class BoutiqueScreen extends StatefulWidget {
  const BoutiqueScreen({super.key});

  @override
  State<BoutiqueScreen> createState() => _BoutiqueScreenState();
}

class _BoutiqueScreenState extends State<BoutiqueScreen>
    with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  final ScrollController _scrollController = ScrollController();
  bool _isScrolled = false;
  bool _isLoading = true;

  // Liste des catalogues disponibles
  List<Map<String, dynamic>> _catalogues = [];

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 300),
    );

    _scrollController.addListener(() {
      if (_scrollController.offset > 0 && !_isScrolled) {
        setState(() {
          _isScrolled = true;
        });
      } else if (_scrollController.offset <= 0 && _isScrolled) {
        setState(() {
          _isScrolled = false;
        });
      }
    });

    _loadCatalogues();
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    // Relancer l'animation quand on revient sur la page
    if (_catalogues.isNotEmpty) {
      WidgetsBinding.instance.addPostFrameCallback((_) {
        if (mounted && _animationController.value == 0) {
          _animationController.forward();
        }
      });
    }
  }

  @override
  void dispose() {
    _animationController.dispose();
    _scrollController.dispose();
    super.dispose();
  }

  // Charger les catalogues depuis l'API
  Future<void> _loadCatalogues() async {
    setState(() {
      _isLoading = true;
    });

    try {
      final catalogues = await CatalogueService.getCarnetCatalogues();

      if (catalogues.isNotEmpty) {
        // Assigner des couleurs et icônes aux catalogues
        final List<Map<String, dynamic>> formattedCatalogues =
            catalogues.map((catalogue) {
              // Déterminer l'icône et la couleur en fonction du nom du catalogue
              IconData icon = Icons.book;
              Color color = Colors.blue;

              final catalogueName =
                  catalogue['nom_carnet'].toString().toLowerCase();

              if (catalogueName.contains('marié') ||
                  catalogueName.contains('mariage')) {
                icon = Icons.favorite;
                color = Colors.red;
              } else if (catalogueName.contains('bébé') ||
                  catalogueName.contains('enfant')) {
                icon = Icons.child_care;
                color = Colors.purple;
              } else if (catalogueName.contains('cuisine') ||
                  catalogueName.contains('électroménager')) {
                icon = Icons.kitchen;
                color = Colors.green;
              } else if (catalogueName.contains('tech') ||
                  catalogueName.contains('électronique')) {
                icon = Icons.devices;
                color = Colors.indigo;
              }

              return {
                'id': catalogue['id_carnet'].toString(),
                'name': catalogue['nom_carnet'],
                'carnet_img': catalogue['carnet_img'],
                'date_debut_cotisation':
                    catalogue['date_debut_cotisation'] ?? '',
                'date_fin_cotisation': catalogue['date_fin_cotisation'] ?? '',
                'color': color,
                'icon': icon,
                'products': [], // Sera rempli lors de la sélection
              };
            }).toList();

        setState(() {
          _catalogues = formattedCatalogues;
          _isLoading = false;
        });

        // Démarrer l'animation d'entrée
        WidgetsBinding.instance.addPostFrameCallback((_) {
          _animationController.forward();
        });
      } else {
        setState(() {
          _catalogues = [];
          _isLoading = false;
        });
      }
    } catch (e) {
      print('Erreur lors du chargement des catalogues: $e');
      setState(() {
        _catalogues = [];
        _isLoading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.grey.shade50,
      body:
          _isLoading
              ? _buildLoadingState()
              : CustomScrollView(
                slivers: [
                  // AppBar personnalisée avec effet de parallaxe
                  _buildCustomAppBar(),

                  // Section des catalogues
                  SliverToBoxAdapter(child: _buildCatalogueSection()),
                ],
              ),
    );
  }

  // État de chargement moderne
  Widget _buildLoadingState() {
    return Container(
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topCenter,
          end: Alignment.bottomCenter,
          colors: [
            AppTheme.color.primaryColor.withValues(alpha: 0.1),
            Colors.white,
          ],
        ),
      ),
      child: const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            CircularProgressIndicator(),
            SizedBox(height: 16),
            Text(
              'Chargement des catalogues...',
              style: TextStyle(fontSize: 16, fontWeight: FontWeight.w500),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildCustomAppBar() {
    return SliverAppBar(
      expandedHeight: 140,
      floating: false,
      pinned: true,
      elevation: 0,
      backgroundColor: Colors.white,
      flexibleSpace: FlexibleSpaceBar(
        background: Container(
          decoration: BoxDecoration(
            gradient: LinearGradient(
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
              colors: [
                AppTheme.color.primaryColor,
                AppTheme.color.primaryColor.withValues(alpha: 0.8),
                AppTheme.color.primaryColor.withValues(alpha: 0.6),
              ],
            ),
          ),
          child: SafeArea(
            child: Padding(
              padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                mainAxisAlignment: MainAxisAlignment.end,
                children: [
                  Row(
                    children: [
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              'Catalogues',
                              style: TextStyle(
                                fontSize: 32,
                                fontWeight: FontWeight.bold,
                                color: Colors.white,
                                letterSpacing: -1,
                              ),
                            ),
                            const SizedBox(height: 4),
                            Container(
                              padding: const EdgeInsets.symmetric(
                                horizontal: 12,
                                vertical: 6,
                              ),
                              decoration: BoxDecoration(
                                color: Colors.white.withValues(alpha: 0.2),
                                borderRadius: BorderRadius.circular(20),
                              ),
                              child: Text(
                                'Découvrez nos catalogues',
                                style: TextStyle(
                                  fontSize: 14,
                                  fontWeight: FontWeight.w500,
                                  color: Colors.white.withValues(alpha: 0.9),
                                ),
                              ),
                            ),
                          ],
                        ),
                      ),
                      Container(
                        padding: const EdgeInsets.all(16),
                        decoration: BoxDecoration(
                          color: Colors.white.withValues(alpha: 0.15),
                          borderRadius: BorderRadius.circular(20),
                          border: Border.all(
                            color: Colors.white.withValues(alpha: 0.3),
                            width: 1,
                          ),
                        ),
                        child: Icon(
                          Icons.auto_stories_rounded,
                          color: Colors.white,
                          size: 32,
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ),
        ),
      ),
      actions: [
        NavigationMenuButton(iconColor: const Color.fromARGB(255, 0, 0, 0)),
        Container(
          margin: const EdgeInsets.only(right: 8),
          child: IconButton(
            icon: Container(
              padding: const EdgeInsets.all(8),
              decoration: BoxDecoration(
                color: Colors.white.withValues(alpha: 0.2),
                borderRadius: BorderRadius.circular(12),
              ),
              child: const Icon(
                Icons.search,
                color: Color.fromARGB(255, 0, 0, 0),
                size: 20,
              ),
            ),
            onPressed: () {
              // Ouvrir la recherche
            },
          ),
        ),
        Container(
          margin: const EdgeInsets.only(right: 16),
          child: IconButton(
            icon: Container(
              padding: const EdgeInsets.all(8),
              decoration: BoxDecoration(
                color: const Color.fromARGB(
                  255,
                  0,
                  0,
                  0,
                ).withValues(alpha: 0.2),
                borderRadius: BorderRadius.circular(12),
              ),
              child: const Icon(
                Icons.refresh,
                color: Color.fromARGB(255, 0, 0, 0),
                size: 20,
              ),
            ),
            onPressed: () async {
              await _loadCatalogues();
            },
          ),
        ),
      ],
    );
  }

  // Section des catalogues avec design moderne
  Widget _buildCatalogueSection() {
    return Container(
      margin: const EdgeInsets.only(top: 20),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: const BorderRadius.only(
          topLeft: Radius.circular(30),
          topRight: Radius.circular(30),
        ),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 20,
            offset: const Offset(0, -5),
          ),
        ],
      ),
      child:
          _catalogues.isEmpty
              ? _buildEmptyCatalogueState()
              : _buildCatalogueSelector(),
    );
  }

  Widget _buildEmptyCatalogueState() {
    return Container(
      padding: const EdgeInsets.all(40),
      child: Column(
        children: [
          Icon(
            Icons.inventory_2_outlined,
            size: 80,
            color: Colors.grey.shade400,
          ),
          const SizedBox(height: 16),
          Text(
            'Aucun catalogue disponible',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.w600,
              color: Colors.grey.shade600,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'Vérifiez votre connexion et réessayez',
            style: TextStyle(fontSize: 14, color: Colors.grey.shade500),
          ),
          const SizedBox(height: 20),
          ElevatedButton.icon(
            onPressed: () async {
              await _loadCatalogues();
            },
            icon: const Icon(Icons.refresh),
            label: const Text('Actualiser'),
            style: ElevatedButton.styleFrom(
              backgroundColor: AppTheme.color.primaryColor,
              foregroundColor: Colors.white,
              padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(25),
              ),
            ),
          ),
        ],
      ),
    );
  }

  // Sélection du catalogue avec design moderne
  Widget _buildCatalogueSelector() {
    return Container(
      padding: const EdgeInsets.fromLTRB(20, 30, 20, 30),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // En-tête de section avec animation
          AnimatedBuilder(
            animation: _animationController,
            builder: (context, child) {
              return Transform.translate(
                offset: Offset(0, 20 * (1 - _animationController.value)),
                child: Opacity(
                  opacity: _animationController.value,
                  child: Row(
                    children: [
                      Container(
                        width: 4,
                        height: 24,
                        decoration: BoxDecoration(
                          gradient: LinearGradient(
                            begin: Alignment.topCenter,
                            end: Alignment.bottomCenter,
                            colors: [
                              AppTheme.color.primaryColor,
                              AppTheme.color.primaryColor.withValues(
                                alpha: 0.6,
                              ),
                            ],
                          ),
                          borderRadius: BorderRadius.circular(2),
                        ),
                      ),
                      const SizedBox(width: 12),
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              'Nos Catalogues',
                              style: TextStyle(
                                fontSize: 24,
                                fontWeight: FontWeight.bold,
                                color: AppTheme.color.textColor,
                                letterSpacing: -0.5,
                              ),
                            ),
                            const SizedBox(height: 4),
                            Container(
                              padding: const EdgeInsets.symmetric(
                                horizontal: 12,
                                vertical: 4,
                              ),
                              decoration: BoxDecoration(
                                color: AppTheme.color.primaryColor.withValues(
                                  alpha: 0.1,
                                ),
                                borderRadius: BorderRadius.circular(12),
                              ),
                              child: Text(
                                '${_catalogues.length} catalogue${_catalogues.length > 1 ? 's' : ''} disponible${_catalogues.length > 1 ? 's' : ''}',
                                style: TextStyle(
                                  fontSize: 12,
                                  fontWeight: FontWeight.w600,
                                  color: AppTheme.color.primaryColor,
                                ),
                              ),
                            ),
                          ],
                        ),
                      ),
                      // Icône décorative
                      Container(
                        padding: const EdgeInsets.all(12),
                        decoration: BoxDecoration(
                          gradient: LinearGradient(
                            colors: [
                              AppTheme.color.primaryColor.withValues(
                                alpha: 0.1,
                              ),
                              AppTheme.color.primaryColor.withValues(
                                alpha: 0.05,
                              ),
                            ],
                          ),
                          borderRadius: BorderRadius.circular(16),
                        ),
                        child: Icon(
                          Icons.auto_stories,
                          color: AppTheme.color.primaryColor,
                          size: 24,
                        ),
                      ),
                    ],
                  ),
                ),
              );
            },
          ),
          const SizedBox(height: 32),

          // Grille des catalogues avec animation
          AnimatedBuilder(
            animation: _animationController,
            builder: (context, child) {
              return Transform.translate(
                offset: Offset(0, 30 * (1 - _animationController.value)),
                child: Opacity(
                  opacity: _animationController.value,
                  child: GridView.builder(
                    shrinkWrap: true,
                    physics: const NeverScrollableScrollPhysics(),
                    gridDelegate:
                        const SliverGridDelegateWithFixedCrossAxisCount(
                          crossAxisCount: 2,
                          childAspectRatio: 0.75,
                          crossAxisSpacing: 16,
                          mainAxisSpacing: 16,
                        ),
                    itemCount: _catalogues.length,
                    itemBuilder: (context, index) {
                      final catalogue = _catalogues[index];
                      return AnimatedContainer(
                        duration: Duration(milliseconds: 300 + (index * 100)),
                        curve: Curves.easeOutBack,
                        child: _buildCatalogueCard(catalogue),
                      );
                    },
                  ),
                ),
              );
            },
          ),
        ],
      ),
    );
  }

  Widget _buildCatalogueCard(Map<String, dynamic> catalogue) {
    final String id =
        catalogue['id_carnet']?.toString() ??
        catalogue['id']?.toString() ??
        'ID inconnu';

    final String name =
        catalogue['nom_carnet'] ??
        catalogue['nom'] ??
        catalogue['name'] ??
        'Carnet sans nom';

    final String description =
        catalogue['description'] ??
        catalogue['details'] ??
        'Découvrir ce catalogue';
    final String date_debut_cotisation =
        catalogue['date_debut_cotisation'] ?? 'Date de début inconnue';
    final String date_fin_cotisation =
        catalogue['date_fin_cotisation'] ?? 'Date de fin inconnue';

    final int colorSeed = id.hashCode;
    final List<Color> cardColors = [
      const Color(0xFF6C5CE7), // Violet
      const Color(0xFF00B894), // Vert
      const Color(0xFFE17055), // Orange
      const Color(0xFF0984E3), // Bleu
      const Color(0xFFE84393), // Rose
      const Color(0xFFFDCB6E), // Jaune
    ];

    final List<IconData> cardIcons = [
      Icons.auto_stories,
      Icons.menu_book,
      Icons.library_books,
      Icons.book,
      Icons.chrome_reader_mode,
      Icons.import_contacts,
    ];

    final Color cardColor = cardColors[colorSeed % cardColors.length];
    final IconData cardIcon = cardIcons[colorSeed % cardIcons.length];

    return GestureDetector(
      onTap: () {
        _animationController.forward().then((_) {
          _animationController.reverse();
        });

        Navigator.push(
          context,
          PageRouteBuilder(
            pageBuilder:
                (context, animation, secondaryAnimation) =>
                    CataloguePacksScreen(catalogue: catalogue),
            transitionsBuilder: (
              context,
              animation,
              secondaryAnimation,
              child,
            ) {
              const begin = Offset(1.0, 0.0);
              const end = Offset.zero;
              const curve = Curves.easeInOutCubic;

              var tween = Tween(
                begin: begin,
                end: end,
              ).chain(CurveTween(curve: curve));

              return SlideTransition(
                position: animation.drive(tween),
                child: child,
              );
            },
            transitionDuration: const Duration(milliseconds: 400),
          ),
        ).then((_) {
          if (mounted) {
            _animationController.reset();
            WidgetsBinding.instance.addPostFrameCallback((_) {
              _animationController.forward();
            });
          }
        });
      },
      child: AnimatedContainer(
        duration: const Duration(milliseconds: 200),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(20),
          boxShadow: [
            BoxShadow(
              color: cardColor.withValues(alpha: 0.15),
              blurRadius: 25,
              offset: const Offset(0, 10),
              spreadRadius: 0,
            ),
            BoxShadow(
              color: Colors.black.withValues(alpha: 0.05),
              blurRadius: 10,
              offset: const Offset(0, 2),
              spreadRadius: 0,
            ),
          ],
        ),
        child: Material(
          color: Colors.transparent,
          child: InkWell(
            borderRadius: BorderRadius.circular(20),
            splashColor: cardColor.withValues(alpha: 0.1),
            highlightColor: cardColor.withValues(alpha: 0.05),
            child: Column(
              children: [
                Expanded(
                  flex: 4,
                  child: Container(
                    width: double.infinity,
                    decoration: const BoxDecoration(
                      borderRadius: BorderRadius.only(
                        topLeft: Radius.circular(20),
                        topRight: Radius.circular(20),
                      ),
                    ),
                    child: ClipRRect(
                      borderRadius: const BorderRadius.only(
                        topLeft: Radius.circular(20),
                        topRight: Radius.circular(20),
                      ),
                      child:
                          catalogue['carnet_img'] != null &&
                                  catalogue['carnet_img'].toString().isNotEmpty
                              ? Image.network(
                                catalogue['carnet_img'].toString().startsWith(
                                      'http',
                                    )
                                    ? catalogue['carnet_img']
                                    : '${ApiConfig.baseUrl2}/${catalogue['carnet_img']}',
                                fit: BoxFit.cover,
                                width: double.infinity,
                                height: double.infinity,
                                errorBuilder: (context, error, stackTrace) {
                                  return Container(
                                    decoration: BoxDecoration(
                                      gradient: LinearGradient(
                                        begin: Alignment.topLeft,
                                        end: Alignment.bottomRight,
                                        colors: [
                                          cardColor,
                                          cardColor.withValues(alpha: 0.7),
                                        ],
                                      ),
                                    ),
                                    child: Center(
                                      child: Icon(
                                        cardIcon,
                                        size: 40,
                                        color: Colors.white.withValues(
                                          alpha: 0.8,
                                        ),
                                      ),
                                    ),
                                  );
                                },
                              )
                              : Container(
                                decoration: BoxDecoration(
                                  gradient: LinearGradient(
                                    begin: Alignment.topLeft,
                                    end: Alignment.bottomRight,
                                    colors: [
                                      cardColor,
                                      cardColor.withValues(alpha: 0.7),
                                    ],
                                  ),
                                ),
                                child: Center(
                                  child: Icon(
                                    cardIcon,
                                    size: 40,
                                    color: Colors.white.withValues(alpha: 0.8),
                                  ),
                                ),
                              ),
                    ),
                  ),
                ),
                Expanded(
                  flex: 6,
                  child: Container(
                    width: double.infinity,
                    padding: const EdgeInsets.all(8),
                    decoration: const BoxDecoration(
                      borderRadius: BorderRadius.only(
                        bottomLeft: Radius.circular(20),
                        bottomRight: Radius.circular(20),
                      ),
                    ),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Expanded(
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            mainAxisAlignment: MainAxisAlignment.start,
                            children: [
                              // Titre
                              Text(
                                name,
                                style: TextStyle(
                                  fontSize: 13,
                                  fontWeight: FontWeight.bold,
                                  color: AppTheme.color.textColor,
                                  height: 1.1,
                                ),
                                maxLines: 2,
                                overflow: TextOverflow.ellipsis,
                              ),
                              const SizedBox(height: 4),
                              Text(
                                date_fin_cotisation.isNotEmpty
                                    ? 'Fin de cotisation: $date_fin_cotisation'
                                    : 'Date de fin inconnue',
                                style: TextStyle(
                                  fontSize: 13,
                                  fontWeight: FontWeight.bold,
                                  color: Colors.red,
                                  height: 1.1,
                                ),
                                maxLines: 2,
                                overflow: TextOverflow.ellipsis,
                              ),
                              const SizedBox(height: 4),
                              // Description
                              Expanded(
                                child: Text(
                                  description,
                                  style: TextStyle(
                                    fontSize: 10,
                                    color: AppTheme.color.brunGris,
                                    height: 1.1,
                                  ),
                                  maxLines: 3,
                                  overflow: TextOverflow.ellipsis,
                                ),
                              ),
                            ],
                          ),
                        ),
                        Container(
                          width: double.infinity,
                          height: 26,
                          decoration: BoxDecoration(
                            gradient: LinearGradient(
                              begin: Alignment.centerLeft,
                              end: Alignment.centerRight,
                              colors: [
                                cardColor,
                                cardColor.withValues(alpha: 0.8),
                              ],
                            ),
                            borderRadius: BorderRadius.circular(16),
                            boxShadow: [
                              BoxShadow(
                                color: cardColor.withValues(alpha: 0.3),
                                blurRadius: 8,
                                offset: const Offset(0, 4),
                              ),
                            ],
                          ),
                          child: Material(
                            color: Colors.transparent,
                            child: InkWell(
                              borderRadius: BorderRadius.circular(16),
                              onTap: () {
                                _animationController.forward().then((_) {
                                  _animationController.reverse();
                                });

                                Navigator.push(
                                  context,
                                  PageRouteBuilder(
                                    pageBuilder:
                                        (
                                          context,
                                          animation,
                                          secondaryAnimation,
                                        ) => CataloguePacksScreen(
                                          catalogue: catalogue,
                                        ),
                                    transitionsBuilder: (
                                      context,
                                      animation,
                                      secondaryAnimation,
                                      child,
                                    ) {
                                      const begin = Offset(1.0, 0.0);
                                      const end = Offset.zero;
                                      const curve = Curves.easeInOutCubic;

                                      var tween = Tween(
                                        begin: begin,
                                        end: end,
                                      ).chain(CurveTween(curve: curve));

                                      return SlideTransition(
                                        position: animation.drive(tween),
                                        child: child,
                                      );
                                    },
                                    transitionDuration: const Duration(
                                      milliseconds: 400,
                                    ),
                                  ),
                                ).then((_) {
                                  if (mounted) {
                                    _animationController.reset();
                                    WidgetsBinding.instance
                                        .addPostFrameCallback((_) {
                                          _animationController.forward();
                                        });
                                  }
                                });
                              },
                              child: Center(
                                child: Row(
                                  mainAxisAlignment: MainAxisAlignment.center,
                                  children: [
                                    Icon(
                                      Icons.arrow_forward_rounded,
                                      color: Colors.white,
                                      size: 16,
                                    ),
                                    const SizedBox(width: 6),
                                    Text(
                                      'Voir les packs',
                                      style: TextStyle(
                                        fontSize: 12,
                                        fontWeight: FontWeight.w600,
                                        color: Colors.white,
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
