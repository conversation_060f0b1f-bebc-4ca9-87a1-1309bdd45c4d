import 'package:flutter/material.dart';

/// ListView optimisé avec lazy loading et cache des widgets
class OptimizedListView<T> extends StatefulWidget {
  final List<T> items;
  final Widget Function(BuildContext context, T item, int index) itemBuilder;
  final Widget Function(BuildContext context)? loadingBuilder;
  final Widget Function(BuildContext context)? emptyBuilder;
  final Future<void> Function()? onLoadMore;
  final bool hasMore;
  final ScrollController? controller;
  final EdgeInsets? padding;
  final double? itemExtent;
  final int loadMoreThreshold;
  final bool enableCache;

  const OptimizedListView({
    super.key,
    required this.items,
    required this.itemBuilder,
    this.loadingBuilder,
    this.emptyBuilder,
    this.onLoadMore,
    this.hasMore = false,
    this.controller,
    this.padding,
    this.itemExtent,
    this.loadMoreThreshold = 3,
    this.enableCache = true,
  });

  @override
  State<OptimizedListView<T>> createState() => _OptimizedListViewState<T>();
}

class _OptimizedListViewState<T> extends State<OptimizedListView<T>> {
  late ScrollController _scrollController;
  bool _isLoadingMore = false;
  final Map<int, Widget> _widgetCache = {};

  @override
  void initState() {
    super.initState();
    _scrollController = widget.controller ?? ScrollController();
    _scrollController.addListener(_onScroll);
  }

  @override
  void dispose() {
    if (widget.controller == null) {
      _scrollController.dispose();
    } else {
      _scrollController.removeListener(_onScroll);
    }
    _widgetCache.clear();
    super.dispose();
  }

  void _onScroll() {
    if (widget.onLoadMore == null || !widget.hasMore || _isLoadingMore) {
      return;
    }

    final position = _scrollController.position;
    final threshold = widget.loadMoreThreshold * (widget.itemExtent ?? 100);

    if (position.pixels >= position.maxScrollExtent - threshold) {
      _loadMore();
    }
  }

  Future<void> _loadMore() async {
    if (_isLoadingMore) return;

    setState(() {
      _isLoadingMore = true;
    });

    try {
      await widget.onLoadMore!();
    } catch (e) {
      debugPrint('Erreur lors du chargement: $e');
    } finally {
      if (mounted) {
        setState(() {
          _isLoadingMore = false;
        });
      }
    }
  }

  Widget _buildCachedItem(int index) {
    if (!widget.enableCache) {
      return widget.itemBuilder(context, widget.items[index], index);
    }

    // Utiliser le cache si disponible
    if (_widgetCache.containsKey(index)) {
      return _widgetCache[index]!;
    }

    // Créer et mettre en cache le widget
    final builtWidget = widget.itemBuilder(context, widget.items[index], index);

    // Limiter la taille du cache pour éviter les fuites mémoire
    if (_widgetCache.length > 100) {
      _widgetCache.clear();
    }

    _widgetCache[index] = builtWidget;
    return builtWidget;
  }

  @override
  Widget build(BuildContext context) {
    if (widget.items.isEmpty) {
      return widget.emptyBuilder?.call(context) ?? _buildDefaultEmpty();
    }

    return ListView.builder(
      controller: _scrollController,
      padding: widget.padding,
      itemExtent: widget.itemExtent,
      itemCount: widget.items.length + (widget.hasMore ? 1 : 0),
      itemBuilder: (context, index) {
        if (index >= widget.items.length) {
          // Indicateur de chargement
          return widget.loadingBuilder?.call(context) ?? _buildDefaultLoading();
        }

        return _buildCachedItem(index);
      },
    );
  }

  Widget _buildDefaultEmpty() {
    return const Center(
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(Icons.inbox_outlined, size: 64, color: Colors.grey),
          SizedBox(height: 16),
          Text(
            'Aucun élément',
            style: TextStyle(fontSize: 16, color: Colors.grey),
          ),
        ],
      ),
    );
  }

  Widget _buildDefaultLoading() {
    return const Padding(
      padding: EdgeInsets.all(16.0),
      child: Center(
        child: SizedBox(
          width: 24,
          height: 24,
          child: CircularProgressIndicator(strokeWidth: 2),
        ),
      ),
    );
  }
}

/// GridView optimisé avec lazy loading
class OptimizedGridView<T> extends StatefulWidget {
  final List<T> items;
  final Widget Function(BuildContext context, T item, int index) itemBuilder;
  final SliverGridDelegate gridDelegate;
  final Widget Function(BuildContext context)? loadingBuilder;
  final Widget Function(BuildContext context)? emptyBuilder;
  final Future<void> Function()? onLoadMore;
  final bool hasMore;
  final ScrollController? controller;
  final EdgeInsets? padding;
  final int loadMoreThreshold;
  final bool enableCache;

  const OptimizedGridView({
    super.key,
    required this.items,
    required this.itemBuilder,
    required this.gridDelegate,
    this.loadingBuilder,
    this.emptyBuilder,
    this.onLoadMore,
    this.hasMore = false,
    this.controller,
    this.padding,
    this.loadMoreThreshold = 3,
    this.enableCache = true,
  });

  @override
  State<OptimizedGridView<T>> createState() => _OptimizedGridViewState<T>();
}

class _OptimizedGridViewState<T> extends State<OptimizedGridView<T>> {
  late ScrollController _scrollController;
  bool _isLoadingMore = false;
  final Map<int, Widget> _widgetCache = {};

  @override
  void initState() {
    super.initState();
    _scrollController = widget.controller ?? ScrollController();
    _scrollController.addListener(_onScroll);
  }

  @override
  void dispose() {
    if (widget.controller == null) {
      _scrollController.dispose();
    } else {
      _scrollController.removeListener(_onScroll);
    }
    _widgetCache.clear();
    super.dispose();
  }

  void _onScroll() {
    if (widget.onLoadMore == null || !widget.hasMore || _isLoadingMore) {
      return;
    }

    final position = _scrollController.position;
    const threshold = 200.0; // pixels

    if (position.pixels >= position.maxScrollExtent - threshold) {
      _loadMore();
    }
  }

  Future<void> _loadMore() async {
    if (_isLoadingMore) return;

    setState(() {
      _isLoadingMore = true;
    });

    try {
      await widget.onLoadMore!();
    } catch (e) {
      debugPrint('Erreur lors du chargement: $e');
    } finally {
      if (mounted) {
        setState(() {
          _isLoadingMore = false;
        });
      }
    }
  }

  Widget _buildCachedItem(int index) {
    if (!widget.enableCache) {
      return widget.itemBuilder(context, widget.items[index], index);
    }

    if (_widgetCache.containsKey(index)) {
      return _widgetCache[index]!;
    }

    final builtWidget = this.widget.itemBuilder(
      context,
      this.widget.items[index],
      index,
    );

    if (_widgetCache.length > 100) {
      _widgetCache.clear();
    }

    _widgetCache[index] = builtWidget;
    return builtWidget;
  }

  @override
  Widget build(BuildContext context) {
    if (widget.items.isEmpty) {
      return widget.emptyBuilder?.call(context) ?? _buildDefaultEmpty();
    }

    return GridView.builder(
      controller: _scrollController,
      padding: widget.padding,
      gridDelegate: widget.gridDelegate,
      itemCount: widget.items.length + (widget.hasMore ? 1 : 0),
      itemBuilder: (context, index) {
        if (index >= widget.items.length) {
          return widget.loadingBuilder?.call(context) ?? _buildDefaultLoading();
        }

        return _buildCachedItem(index);
      },
    );
  }

  Widget _buildDefaultEmpty() {
    return const Center(
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(Icons.grid_view_outlined, size: 64, color: Colors.grey),
          SizedBox(height: 16),
          Text(
            'Aucun élément',
            style: TextStyle(fontSize: 16, color: Colors.grey),
          ),
        ],
      ),
    );
  }

  Widget _buildDefaultLoading() {
    return const Center(
      child: SizedBox(
        width: 24,
        height: 24,
        child: CircularProgressIndicator(strokeWidth: 2),
      ),
    );
  }
}
