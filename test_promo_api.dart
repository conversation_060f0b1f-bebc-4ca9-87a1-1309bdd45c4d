import 'dart:convert';
import 'dart:io';

/// Script de test pour vérifier l'API des publicités
void main() async {
  print('🧪 Test de l\'API des publicités...\n');

  // Configuration de l'API
  const String baseUrl =
      'https://api.callitris-distribution.com/client-api.callitris-distribution.com';
  const String endpoint = '$baseUrl/pubs/getPub.php';

  print('🔗 URL de l\'API: $endpoint');
  print('');

  // Test 1: Vérifier la connectivité
  print('📡 Test 1: Vérification de la connectivité...');
  try {
    final client = HttpClient();
    final request = await client.getUrl(Uri.parse(endpoint));

    // Ajouter un token de test (vous devrez remplacer par un vrai token)
    request.headers.add('Authorization', 'Bearer YOUR_TEST_TOKEN_HERE');

    final response = await request.close();
    print('✅ Connexion établie - Code: ${response.statusCode}');

    if (response.statusCode == 200) {
      final responseBody = await response.transform(utf8.decoder).join();
      print('📄 Réponse reçue (${responseBody.length} caractères)');
      print(
        '📄 Contenu: ${responseBody.substring(0, responseBody.length > 200 ? 200 : responseBody.length)}...',
      );

      // Test 2: Parser la réponse JSON
      print('\n🔍 Test 2: Analyse de la réponse JSON...');
      try {
        final jsonData = jsonDecode(responseBody);

        if (jsonData is List) {
          print('✅ Format JSON valide - Liste de ${jsonData.length} éléments');

          if (jsonData.isNotEmpty) {
            print('\n📊 Analyse du premier élément:');
            final firstItem = jsonData[0];
            print('   Structure: ${firstItem.keys.toList()}');

            // Vérifier les champs requis
            final requiredFields = [
              'id_publicite',
              'name',
              'description',
              'price',
              'imageUrl',
            ];
            final missingFields = <String>[];

            for (final field in requiredFields) {
              if (!firstItem.containsKey(field)) {
                missingFields.add(field);
              }
            }

            if (missingFields.isEmpty) {
              print('✅ Tous les champs requis sont présents');

              // Test 3: Vérifier les URLs d'images
              print('\n Test 3: Vérification des URLs d\'images...');
              for (int i = 0; i < jsonData.length && i < 3; i++) {
                final item = jsonData[i];
                final imageUrl = item['imageUrl'];
                print('   Image ${i + 1}: $imageUrl');

                if (imageUrl != null && imageUrl.toString().isNotEmpty) {
                  print('   ✅ URL présente');
                } else {
                  print('   ❌ URL manquante ou vide');
                }
              }
            } else {
              print('❌ Champs manquants: $missingFields');
            }
          } else {
            print('⚠️ Liste vide - Aucune publicité trouvée');
          }
        } else {
          print(
            '❌ Format inattendu - Attendu: List, Reçu: ${jsonData.runtimeType}',
          );
        }
      } catch (jsonError) {
        print('❌ Erreur de parsing JSON: $jsonError');
        print('📄 Contenu brut: $responseBody');
      }
    } else if (response.statusCode == 401) {
      print('❌ Erreur d\'authentification - Token invalide ou manquant');
    } else {
      print('❌ Erreur HTTP: ${response.statusCode}');
    }

    client.close();
  } catch (e) {
    print('❌ Erreur de connexion: $e');
  }

  print('\n📋 Résumé des tests:');
  print('1. Vérifiez que votre token d\'authentification est valide');
  print('2. Assurez-vous que l\'API retourne une liste JSON');
  print('3. Vérifiez que chaque élément contient les champs requis');
  print('4. Contrôlez que les URLs d\'images sont valides');

  print('\n🔧 Pour déboguer dans l\'app:');
  print('1. Regardez les logs dans la console Flutter');
  print('2. Vérifiez le token avec AuthService.getAuthToken()');
  print('3. Testez l\'URL de l\'API dans un navigateur');
  print('4. Utilisez les données de test en cas d\'échec');
}
