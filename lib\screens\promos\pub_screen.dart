import 'dart:convert';

import 'package:callitris/config/api_config.dart';
import 'package:callitris/services/auth_service.dart';
import 'package:callitris/utils/appTheme.dart';
import 'package:callitris/widgets/fullscreen_image_viewer.dart';
import 'package:flutter/material.dart';
import 'package:flutter/gestures.dart';
import 'package:http/http.dart' as http;
import 'package:url_launcher/url_launcher.dart';

class PubScreen extends StatefulWidget {
  final String pubId;
  const PubScreen({super.key, required this.pubId});

  @override
  State<PubScreen> createState() => _PubScreenState();
}

class _PubScreenState extends State<PubScreen> {
  Map<String, dynamic> _pub = {};
  bool _isLoading = false;

  @override
  void initState() {
    super.initState();
    _loadPub();
  }

  Future<void> _loadPub() async {
    setState(() {
      _isLoading = true;
    });

    try {
      final token = await AuthService.getAuthToken();

      if (token == null) {
        print('❌ Token manquant pour récupérer la publicité');
        setState(() {
          _pub = {};
          _isLoading = false;
        });
        return;
      }

      final response = await http
          .get(
            Uri.parse(
              "${ApiConfig.baseUrl}/pubs/getPubById.php?id=${widget.pubId}",
            ),
            headers: {'Authorization': 'Bearer $token'},
          )
          .timeout(const Duration(seconds: 15));

      if (response.statusCode == 200) {
        final responseBody = response.body.trim();

        if (responseBody.isEmpty) {
          setState(() {
            _pub = {};
            _isLoading = false;
          });
          return;
        }

        try {
          // Nettoyer le JSON avant le parsing pour éviter les problèmes d'encodage
          String cleanedJson = responseBody
              .replaceAll(r'\u00e9', 'é')
              .replaceAll(r'\u00e8', 'è')
              .replaceAll(r'\u00ea', 'ê')
              .replaceAll(r'\u00e0', 'à')
              .replaceAll(r'\u00f9', 'ù')
              .replaceAll(r'\u00e7', 'ç');

          print('📄 JSON publicité nettoyé: $cleanedJson');

          final Map<String, dynamic> responseData = jsonDecode(cleanedJson);
          print('📊 Publicité reçue: $responseData');

          // Traitement de l'URL de l'image
          String rawPath = responseData['imageUrl']?.toString() ?? '';
          String cleanedPath = rawPath.replaceAll(r'\/', '/');
          String fullUrl;

          try {
            // Séparer le chemin et le nom de fichier
            List<String> pathParts = cleanedPath.split('/');
            if (pathParts.isNotEmpty) {
              // Encoder seulement le nom de fichier (dernière partie)
              String fileName = pathParts.last;
              String encodedFileName = Uri.encodeComponent(fileName);
              pathParts[pathParts.length - 1] = encodedFileName;
              String encodedPath = pathParts.join('/');
              fullUrl = "${ApiConfig.baseUrl2}/$encodedPath";
            } else {
              fullUrl = "${ApiConfig.baseUrl2}/$cleanedPath";
            }
          } catch (e) {
            print('⚠️ Erreur d\'encodage URL: $e');
            fullUrl = "${ApiConfig.baseUrl2}/$cleanedPath";
          }

          setState(() {
            _pub = responseData;
            _pub['imageUrl'] = fullUrl;
            _isLoading = false;
          });
        } catch (jsonError) {
          print('❌ Erreur de parsing JSON: $jsonError');
          print('📄 Contenu reçu: $responseBody');
          setState(() {
            _pub = {};
            _isLoading = false;
          });
        }
      } else {
        print('❌ Erreur HTTP: ${response.statusCode}');
        print('📄 Message d\'erreur: ${response.body}');
        setState(() {
          _pub = {};
          _isLoading = false;
        });
      }
    } catch (e) {
      print('❌ Exception lors du chargement de la publicité: $e');
      setState(() {
        _pub = {};
        _isLoading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: CustomScrollView(
        slivers: [_buildAppBar(), SliverToBoxAdapter(child: _buildPubInfo())],
      ),
    );
  }

  Widget _buildAppBar() {
    // Vérifier si l'URL de l'image est une URL complète ou un chemin relatif
    final String imageUrl = _pub['imageUrl']?.toString() ?? '';
    final bool isNetworkImage =
        imageUrl.isNotEmpty &&
        (imageUrl.startsWith('http://') ||
            imageUrl.startsWith('https://') ||
            imageUrl.contains('public/img') ||
            imageUrl.startsWith('/'));

    return SliverAppBar(
      expandedHeight: 300.0,
      backgroundColor: Colors.white,
      elevation: 0,
      pinned: true,
      stretch: true,
      leading: Container(
        margin: const EdgeInsets.all(8),
        decoration: BoxDecoration(
          color: Colors.white.withValues(alpha: 0.9),
          shape: BoxShape.circle,
        ),
        child: IconButton(
          icon: const Icon(Icons.arrow_back, color: Colors.black),
          onPressed: () => Navigator.of(context).pop(),
        ),
      ),
      actions: [
        // Bouton de partage
        Container(
          margin: const EdgeInsets.all(8),
          decoration: BoxDecoration(
            color: Colors.white.withValues(alpha: 0.9),
            shape: BoxShape.circle,
          ),
          child: IconButton(
            icon: const Icon(Icons.share, color: Colors.black),
            onPressed: () {
              // Logique de partage
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(content: Text('Partage de la publicité...')),
              );
            },
            tooltip: 'Partager',
          ),
        ),
      ],
      flexibleSpace: FlexibleSpaceBar(
        background: GestureDetector(
          onTap: () {
            // Ouvrir l'image en plein écran
            Navigator.of(context).push(
              PageRouteBuilder(
                pageBuilder:
                    (context, animation, secondaryAnimation) =>
                        FullscreenImageViewer(
                          imageUrl: imageUrl,
                          heroTag: 'pub-${_pub['id']?.toString() ?? 'unknown'}',
                          isNetworkImage: isNetworkImage,
                        ),
                transitionsBuilder: (
                  context,
                  animation,
                  secondaryAnimation,
                  child,
                ) {
                  return FadeTransition(opacity: animation, child: child);
                },
                transitionDuration: const Duration(milliseconds: 300),
                opaque: false,
              ),
            );
          },
          child: Stack(
            fit: StackFit.expand,
            children: [
              Hero(
                tag: 'pub-${_pub['id']?.toString() ?? 'unknown'}',
                child:
                    imageUrl.isEmpty
                        ? Container(
                          color: Colors.grey[200],
                          child: Center(
                            child: Icon(
                              Icons.image_not_supported,
                              color: Colors.grey[400],
                              size: 60,
                            ),
                          ),
                        )
                        : isNetworkImage
                        ? Image.network(
                          imageUrl,
                          fit: BoxFit.cover,
                          errorBuilder: (context, error, stackTrace) {
                            return Container(
                              color: Colors.grey[200],
                              child: Center(
                                child: Icon(
                                  Icons.image_not_supported,
                                  color: Colors.grey[400],
                                  size: 60,
                                ),
                              ),
                            );
                          },
                        )
                        : Image.asset(
                          imageUrl,
                          fit: BoxFit.cover,
                          errorBuilder: (context, error, stackTrace) {
                            return Container(
                              color: Colors.grey[200],
                              child: Center(
                                child: Icon(
                                  Icons.image_not_supported,
                                  color: Colors.grey[400],
                                  size: 60,
                                ),
                              ),
                            );
                          },
                        ),
              ),
              // Gradient overlay pour améliorer la lisibilité
              Positioned(
                bottom: 0,
                left: 0,
                right: 0,
                height: 100,
                child: Container(
                  decoration: BoxDecoration(
                    gradient: LinearGradient(
                      begin: Alignment.topCenter,
                      end: Alignment.bottomCenter,
                      colors: [
                        Colors.transparent,
                        Colors.black.withValues(alpha: 0.7),
                      ],
                    ),
                  ),
                ),
              ),
              // Badge PUB
              Positioned(
                top: 60,
                right: 16,
                child: Container(
                  padding: const EdgeInsets.symmetric(
                    horizontal: 12,
                    vertical: 6,
                  ),
                  decoration: BoxDecoration(
                    color: Colors.black.withValues(alpha: 0.6),
                    borderRadius: BorderRadius.circular(20),
                  ),
                  child: const Text(
                    'PUB',
                    style: TextStyle(
                      color: Colors.white,
                      fontSize: 12,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildPubInfo() {
    if (_pub.isEmpty) {
      return Container(
        padding: const EdgeInsets.all(24),
        child: const Center(
          child: Text('Informations de publicité non disponibles'),
        ),
      );
    }

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Container(
          padding: const EdgeInsets.all(24),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  Container(
                    padding: const EdgeInsets.symmetric(
                      horizontal: 10,
                      vertical: 4,
                    ),
                    decoration: BoxDecoration(
                      color: AppTheme.color.primaryColor.withValues(alpha: 0.1),
                      borderRadius: BorderRadius.circular(20),
                    ),
                    child: Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Icon(
                          Icons.campaign,
                          size: 14,
                          color: AppTheme.color.primaryColor,
                        ),
                        const SizedBox(width: 4),
                        Text(
                          'PUB',
                          style: TextStyle(
                            fontSize: 12,
                            fontWeight: FontWeight.bold,
                            color: AppTheme.color.primaryColor,
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 12),

              // Nom de la publicité
              Text(
                _pub['name'] ?? 'Publicité sans nom',
                style: const TextStyle(
                  fontSize: 24,
                  fontWeight: FontWeight.bold,
                  color: Colors.black,
                  height: 1.2,
                ),
              ),
              const SizedBox(height: 8),

              // Description de la publicité avec liens cliquables
              if (_pub['description'] != null &&
                  _pub['description'].toString().isNotEmpty)
                _buildDescriptionWithLinks(_pub['description']),
              const SizedBox(height: 16),

              // Informations sur la publicité
              Container(
                padding: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  color: AppTheme.color.primaryColor.withValues(alpha: 0.05),
                  borderRadius: BorderRadius.circular(12),
                  border: Border.all(
                    color: AppTheme.color.primaryColor.withValues(alpha: 0.2),
                  ),
                ),
                child: Row(
                  children: [
                    Container(
                      padding: const EdgeInsets.all(10),
                      decoration: BoxDecoration(
                        color: AppTheme.color.primaryColor.withValues(
                          alpha: 0.1,
                        ),
                        shape: BoxShape.circle,
                      ),
                      child: Icon(
                        Icons.info_outline,
                        color: AppTheme.color.primaryColor,
                        size: 20,
                      ),
                    ),
                    const SizedBox(width: 16),
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            'Publicité promotionnelle',
                            style: TextStyle(
                              color: AppTheme.color.primaryColor,
                              fontSize: 16,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                          const SizedBox(height: 4),
                          Text(
                            'Découvrez nos offres et services exclusifs',
                            style: TextStyle(
                              fontSize: 14,
                              color: AppTheme.color.brunGris,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              ),

              // Informations supplémentaires si disponibles
              if (_pub['category'] != null) ...[
                const SizedBox(height: 16),
                Container(
                  padding: const EdgeInsets.all(16),
                  decoration: BoxDecoration(
                    color: Colors.grey[50],
                    borderRadius: BorderRadius.circular(12),
                    border: Border.all(color: Colors.grey[200]!),
                  ),
                  child: Row(
                    children: [
                      Icon(
                        Icons.category_outlined,
                        color: AppTheme.color.brunGris,
                        size: 20,
                      ),
                      const SizedBox(width: 12),
                      Text(
                        'Catégorie: ${_pub['category']}',
                        style: TextStyle(
                          fontSize: 14,
                          color: AppTheme.color.brunGris,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                    ],
                  ),
                ),
              ],

              const SizedBox(height: 24),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildDescriptionWithLinks(String description) {
    // Expression régulière pour détecter les URLs
    final RegExp urlRegExp = RegExp(r'https?://[^\s]+', caseSensitive: false);

    final List<TextSpan> spans = [];
    int lastIndex = 0;

    // Trouver tous les liens dans la description
    for (final Match match in urlRegExp.allMatches(description)) {
      // Ajouter le texte avant le lien
      if (match.start > lastIndex) {
        spans.add(
          TextSpan(
            text: description.substring(lastIndex, match.start),
            style: TextStyle(
              fontSize: 16,
              color: AppTheme.color.brunGris,
              height: 1.4,
            ),
          ),
        );
      }

      // Ajouter le lien cliquable
      final String url = match.group(0)!;
      spans.add(
        TextSpan(
          text: url,
          style: TextStyle(
            fontSize: 16,
            color: AppTheme.color.primaryColor,
            height: 1.4,
            decoration: TextDecoration.underline,
            fontWeight: FontWeight.w500,
          ),
          recognizer: TapGestureRecognizer()..onTap = () => _launchURL(url),
        ),
      );

      lastIndex = match.end;
    }

    // Ajouter le texte restant après le dernier lien
    if (lastIndex < description.length) {
      spans.add(
        TextSpan(
          text: description.substring(lastIndex),
          style: TextStyle(
            fontSize: 16,
            color: AppTheme.color.brunGris,
            height: 1.4,
          ),
        ),
      );
    }

    // Si aucun lien n'a été trouvé, afficher le texte normal
    if (spans.isEmpty) {
      return Text(
        description,
        style: TextStyle(
          fontSize: 16,
          color: AppTheme.color.brunGris,
          height: 1.4,
        ),
      );
    }

    return RichText(text: TextSpan(children: spans));
  }

  Future<void> _launchURL(String url) async {
    try {
      final Uri uri = Uri.parse(url);

      // Vérifier si l'URL peut être lancée
      if (await canLaunchUrl(uri)) {
        await launchUrl(
          uri,
          mode:
              LaunchMode
                  .externalApplication, // Ouvre dans le navigateur externe
        );
      } else {
        // Afficher un message d'erreur si l'URL ne peut pas être ouverte
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('Impossible d\'ouvrir le lien: $url'),
              backgroundColor: Colors.red,
            ),
          );
        }
      }
    } catch (e) {
      // Gérer les erreurs de lancement d'URL
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Erreur lors de l\'ouverture du lien: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }
}
