# Script PowerShell pour ajouter PopScope à tous les showDialog
# Ce script modifie tous les fichiers Dart pour envelopper les AlertDialog dans PopScope

$libPath = "lib"
$dartFiles = Get-ChildItem -Path $libPath -Recurse -Filter "*.dart"

foreach ($file in $dartFiles) {
    $content = Get-Content $file.FullName -Raw
    $modified = $false
    
    # Pattern pour trouver les showDialog avec AlertDialog
    $pattern1 = '(\s+builder:\s*\n?\s*\(context\)\s*=>\s*)AlertDialog\('
    $replacement1 = '${1}PopScope(' + "`n" + '            canPop: false,' + "`n" + '            child: AlertDialog('
    
    if ($content -match $pattern1) {
        $content = $content -replace $pattern1, $replacement1
        $modified = $true
        Write-Host "Modified pattern 1 in: $($file.Name)"
    }
    
    # Pattern pour trouver les showDialog avec const AlertDialog
    $pattern2 = '(\s+builder:\s*\n?\s*\(context\)\s*=>\s*)const AlertDialog\('
    $replacement2 = '${1}PopScope(' + "`n" + '            canPop: false,' + "`n" + '            child: const AlertDialog('
    
    if ($content -match $pattern2) {
        $content = $content -replace $pattern2, $replacement2
        $modified = $true
        Write-Host "Modified pattern 2 in: $($file.Name)"
    }
    
    # Pattern pour Dialog
    $pattern3 = '(\s+builder:\s*\n?\s*\(context\)\s*=>\s*)Dialog\('
    $replacement3 = '${1}PopScope(' + "`n" + '            canPop: false,' + "`n" + '            child: Dialog('
    
    if ($content -match $pattern3) {
        $content = $content -replace $pattern3, $replacement3
        $modified = $true
        Write-Host "Modified pattern 3 in: $($file.Name)"
    }
    
    # Pattern pour StatefulBuilder avec AlertDialog
    $pattern4 = '(\s+builder:\s*\n?\s*\(context\)\s*=>\s*)StatefulBuilder\(\s*\n\s*builder:\s*\(context,\s*setState\)\s*\{\s*\n\s*return AlertDialog\('
    $replacement4 = '${1}PopScope(' + "`n" + '            canPop: false,' + "`n" + '            child: StatefulBuilder(' + "`n" + '              builder: (context, setState) {' + "`n" + '                return AlertDialog('
    
    if ($content -match $pattern4) {
        $content = $content -replace $pattern4, $replacement4
        $modified = $true
        Write-Host "Modified pattern 4 in: $($file.Name)"
    }
    
    if ($modified) {
        # Maintenant nous devons ajouter les fermetures de PopScope
        # Ceci est plus complexe car nous devons trouver la fin correspondante de chaque AlertDialog/Dialog
        
        # Pour l'instant, nous allons faire une approche simple
        # Nous devons fermer chaque PopScope ajouté
        
        # Compter le nombre de PopScope ajoutés
        $popScopeCount = ($content | Select-String -Pattern "PopScope\(" -AllMatches).Matches.Count
        
        # Ajouter les fermetures correspondantes avant les dernières parenthèses de showDialog
        # Ceci nécessite une logique plus sophistiquée
        
        Set-Content -Path $file.FullName -Value $content -Encoding UTF8
        Write-Host "Updated file: $($file.Name)"
    }
}

Write-Host "Script completed!"
